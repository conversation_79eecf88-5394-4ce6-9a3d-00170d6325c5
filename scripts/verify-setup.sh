#!/bin/bash

echo "Verifying DataScope Setup..."

# Check Docker services
echo -e "\n=== Checking Docker Services ==="
docker-compose ps

# Check Application Build
echo -e "\n=== Verifying Backend Build ==="
mvn clean package -DskipTests

# Check Frontend Build
echo -e "\n=== Verifying Frontend Build ==="
cd boss-data-scope2
npm install
npm run build

# Check Database Schema
echo -e "\n=== Verifying Database Schema ==="
docker-compose exec mysql mysql -u root -psecret datascope -e "SHOW TABLES;"

# Check Redis Connection
echo -e "\n=== Verifying Redis Connection ==="
docker-compose exec redis redis-cli ping

# Check Monitoring
echo -e "\n=== Verifying Monitoring Stack ==="
curl -s http://localhost:9090/-/healthy
echo "Prometheus Status: $?"
curl -s http://localhost:3000/api/health
echo "Grafana Status: $?"

# Check Documentation
echo -e "\n=== Verifying Documentation ==="
[ -f "README.md" ] && echo "README.md exists" || echo "README.md missing"
[ -f "docs/architecture.md" ] && echo "Architecture docs exist" || echo "Architecture docs missing"

echo -e "\nVerification Complete!"
