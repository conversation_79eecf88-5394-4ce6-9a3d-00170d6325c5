{"compilerOptions": {"target": "ES2022", "lib": ["ES2023", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "strict": true, "moduleResolution": "node", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "preserve", "baseUrl": "../../", "paths": {"@/*": ["./src/*"]}}, "include": ["../../src/**/*", "../../src/**/*.vue"], "exclude": ["node_modules"]}