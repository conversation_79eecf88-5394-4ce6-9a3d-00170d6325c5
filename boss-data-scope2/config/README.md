# 项目配置目录

本目录包含项目的各种配置文件，按类型组织，便于管理和维护。

## 目录结构

- `env/`: 环境配置文件，包括不同环境的配置模板
  - `env.example.js`: 环境配置示例，用于指导用户创建自己的环境配置
  - `env.development.js`: 开发环境配置示例
  - `env.production.js`: 生产环境配置示例
  
- `typescript/`: TypeScript配置文件
  - `tsconfig.base.json`: 基础TypeScript配置
  - `tsconfig.app.json`: 应用TypeScript配置
  - `tsconfig.node.json`: Node环境TypeScript配置
  - `tsconfig.test.json`: 测试环境TypeScript配置

## 使用说明

### 环境配置

1. 复制对应的示例配置文件到项目根目录
2. 根据需要修改配置值
3. 确保敏感配置值（如API密钥）不要提交到代码库

### TypeScript配置

TypeScript配置文件是通过引用方式组织的，根配置文件引用了特定环境的配置。

- `tsconfig.json`: 默认配置，引用基础配置
- 特定环境配置引用基础配置并添加特定环境的设置