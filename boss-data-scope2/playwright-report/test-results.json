{"config": {"configFile": "/Users/<USER>/sources/data-scope2/boss-data-scope2/playwright.config.ts", "rootDir": "/Users/<USER>/sources/data-scope2/boss-data-scope2/tests/e2e", "forbidOnly": false, "fullyParallel": false, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 5, "metadata": {}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "playwright-report"}], ["json", {"outputFile": "playwright-report/test-results.json"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/sources/data-scope2/boss-data-scope2/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "chromium", "name": "chromium", "testDir": "/Users/<USER>/sources/data-scope2/boss-data-scope2/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 10000}, {"outputDir": "/Users/<USER>/sources/data-scope2/boss-data-scope2/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "webkit", "name": "webkit", "testDir": "/Users/<USER>/sources/data-scope2/boss-data-scope2/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 10000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.52.0", "workers": 4, "webServer": {"command": "npm run dev", "port": 3000, "reuseExistingServer": true, "timeout": 120000}}, "suites": [], "errors": [{"message": "Error: Timed out waiting 120000ms from config.webServer.", "stack": "Error: Timed out waiting 120000ms from config.webServer."}], "stats": {"startTime": "2025-05-27T12:40:19.295Z", "duration": 120131.909, "expected": 0, "skipped": 0, "unexpected": 0, "flaky": 0}}