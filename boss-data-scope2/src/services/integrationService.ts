/**
 * 集成服务
 * 提供与后端集成API的交互
 */
import type {
  Integration,
  QueryResult,
  IntegrationQueryParam
} from '@/types/integration';

// 引入HTTP客户端
import http from '@/utils/http';
import { lowCodeConfig } from '@/utils/config';
import {getApiBaseUrl} from "@/services/query";
import instance from "@/utils/axios";

// 低代码平台请求头
const getLowcodeRequestHeaders = () => {
  return {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${lowCodeConfig.authToken}`,
    'accept': 'application/json, text/plain, */*',
    'systemcode': lowCodeConfig.systemCode,
    // 注意：下面的设置无法解决跨域问题，因为这些头需要由服务器响应设置
    // 'origin': lowCodeConfig.baseUrl,
    // 'referer': `${lowCodeConfig.baseUrl}/boss-lowcode/index.html`,
    // 'sec-fetch-dest': 'empty',
    // 'sec-fetch-mode': 'cors',
    // 'sec-fetch-site': 'same-origin'
  };
};

// 分页请求响应格式
interface PageResponse<T> {
  first: boolean;
  hasNext: boolean;
  hasPrevious: boolean;
  items: T[];
  last: boolean;
  page: number;
  pages: number;
  size: number;
  total: number;
}

// 基础API地址
const API_BASE = `/api`;

/**
 * 集成服务
 */
export const integrationService = {
  /**
   * 获取集成列表
   * @param params 查询参数，包括分页、筛选条件等
   */
  getIntegrations: async (params?: {
    page?: number;
    size?: number;
    name?: string;
    type?: 'SIMPLE_TABLE' | 'TABLE' | 'CHART' | 'all';
    status?: 'ACTIVE' | 'INACTIVE' | 'DRAFT' | 'all';
  }): Promise<PageResponse<Integration>> => {
    // 创建新的参数对象，避免修改原始参数
    const queryParams: Record<string, any> = {};

    if (params) {
      // 直接设置分页参数 - 根据API文档中的规范
      if (params.page !== undefined) queryParams.page = params.page;
      if (params.size !== undefined) queryParams.size = params.size;

      // 设置筛选条件，排除特殊值
      if (params.name && params.name.trim() !== '') {
        queryParams.name = params.name.trim();
      }

      if (params.type && params.type !== 'all') {
        queryParams.type = params.type;
      }

      if (params.status && params.status !== 'all') {
        queryParams.status = params.status;
      }
    }

    console.log('[集成Service] 处理后的请求参数:', queryParams);

    console.log('[集成Service] 完整请求URL将为：', `${API_BASE}/integrations`, '参数：', queryParams);
    const response = await instance.get(`${API_BASE}/integrations`, { params: queryParams });
    console.log('[集成Service] 获取集成列表API响应:', response);
    return response.data;
  },

  /**
   * 获取集成详情
   * @param id 集成ID
   */
  getIntegrationById: async (id: string): Promise<Integration> => {
    console.log(`[集成Service] 获取集成详情 (ID: ${id})`);
    const response = await instance.get(`${API_BASE}/integrations/${id}`);
    console.log('[集成Service] 集成详情响应:', response);

    if (response.data) {
      console.log('[集成Service] 集成详情字段检查:',
        Object.keys(response.data),
        '是否包含tableConfig:', Boolean(response.data.tableConfig),
        '是否包含queryParams:', Boolean(response.data.queryParams));
    }

    return response.data;
  },

  /**
   * 创建集成
   * @param data 集成数据 - 使用顶级字段结构，不要使用嵌套的config对象
   */
  createIntegration: async (data: {
    name: string;                             // 集成名称（必填）
    description?: string;                     // 描述信息
    type: 'SIMPLE_TABLE' | 'TABLE' | 'CHART'; // 集成类型（必填）
    queryId: string;                          // 关联的查询ID（必填）
    dataSourceId: string;                     // 数据源ID（必填）
    integrationPoint?: any;                   // 集成点配置
    chartConfig?: any;                        // 图表配置（CHART类型）
    tableConfig?: any;                        // 表格配置（TABLE类型）
    formConfig?: any;                         // 表单配置（FORM类型）
    params?: any[];                           // 查询参数列表
  }): Promise<Integration> => {
    const response = await instance.post(`${API_BASE}/integrations`, data);
    return response.data;
  },

  /**
   * 更新集成
   * @param id 集成ID
   * @param data 集成数据 - 使用顶级字段结构，不要使用嵌套的config对象
   */
  updateIntegration: async (id: string, data: {
    name?: string;                             // 集成名称
    description?: string;                      // 描述信息
    type?: 'SIMPLE_TABLE' | 'TABLE' | 'CHART'; // 集成类型
    queryId?: string;                          // 关联的查询ID
    dataSourceId?: string;                     // 数据源ID
    integrationPoint?: any;                    // 集成点配置
    chartConfig?: any;                         // 图表配置（CHART类型）
    tableConfig?: any;                         // 表格配置（TABLE类型）
    formConfig?: any;                          // 表单配置（FORM类型）
    params?: any[];                            // 查询参数列表
  }): Promise<Integration> => {
    const response = await instance.put(`${API_BASE}/integrations/${id}`, data);
    return response.data;
  },

  /**
   * 删除集成
   * @param id 集成ID
   */
  deleteIntegration: async (id: string): Promise<void> => {
    await instance.delete(`${API_BASE}/integrations/${id}`);
  },

  /**
   * 预览集成
   * @param id 集成ID
   */
  previewIntegration: async (id: string): Promise<QueryResult> => {
    const response = await instance.get(`${API_BASE}/integrations/${id}/preview`);
    return response.data;
  },

  /**
   * 更新集成状态
   * @param id 集成ID
   * @param status 状态值
   */
  updateIntegrationStatus: async (id: string, status: 'ACTIVE' | 'INACTIVE' | 'DRAFT'): Promise<Integration> => {
    const response = await instance.patch(`${API_BASE}/integrations/${id}/status`, { status });
    return response.data;
  },

  /**
   * 执行集成查询
   * @param request 查询请求
   */
  executeQuery: async (request: {
    integrationId: string;
    parameters?: Record<string, any>;
    versionId?: string;
    pagination?: {
      page: number;
      pageSize: number;
    }
  }): Promise<QueryResult> => {
    console.log('[集成Service] 执行查询，带版本ID:', request.versionId);
    const response = await instance.post(`${API_BASE}/integration/execute-query`, request);
    return response.data;
  },

  /**
   * 将导出的集成JSON转换为低代码平台格式
   * @param configJson 导出的集成JSON配置
   * @returns 低代码平台的JSON配置
   */
  convertToLowCode: async (configJson: any): Promise<any> => {
    console.log('[集成Service] 开始转换为低代码格式');
    console.log('[集成Service] 输入标准配置:', configJson);

    try {
      // 确保标准配置格式正确，至少要包含meta和一些必要字段
      if (!configJson || typeof configJson !== 'object') {
        throw new Error('标准配置格式不正确：必须是一个对象');
      }

      if (!configJson.meta) {
        throw new Error('标准配置缺少meta字段');
      }

      if (!configJson.meta.pageCode) {
        console.warn('[集成Service] 标准配置缺少pageCode，使用默认值');
        configJson.meta.pageCode = 'default_page_code';
      }

      if (!configJson.meta.apis) {
        console.warn('[集成Service] 标准配置缺少apis定义，使用默认值');
        configJson.meta.apis = {
          query: { method: 'GET', path: '/api/query' },
          download: { method: 'GET', path: '/download' }
        };
      }

      console.log('[集成Service] 验证标准配置通过，开始调用低代码转换服务');

      // 调用低代码转换服务
      const response = await http.post('https://databas-lowcode-kvwkhvjesu.cn-beijing.fcapp.run/convert-to-lowcode', configJson);
      const result = response.data;

      // 检查返回结果的基本结构
      if (!result || !result.data) {
        console.error('[集成Service] 低代码转换服务返回的数据格式异常:', result);
        throw new Error('低代码转换服务返回的数据格式异常');
      }

      console.log('[集成Service] 低代码转换结果:', result);

      // 返回正确的数据结构
      return result.data;
    } catch (error) {
      console.error('[集成Service] 转换为低代码格式失败:', error);
      // 发生错误时返回一个默认的低代码结构
      return {
        "globals": {},
        "components": {
          "entry": {
            "properties": {
              "fieldSet": {
                "value": []
              }
            },
            "type": "entry",
            "events": [],
            "styles": {},
            "exposedVariables": {}
          }
        },
        "queries": [],
        "variables": {}
      };
    }
  },

  /**
   * 查询低代码平台页面是否存在
   * @param pageCode 页面编码
   * @returns 查询结果，包含页面信息或null
   */
  queryLowcodePage: async (pageCode: string): Promise<any> => {
    console.log('[集成Service] 查询低代码页面是否存在, pageCode:', pageCode);
    try {
      // 使用代理URL解决跨域问题
      const url = `/pmc-server/page/pages?limit=20&projectCode=${lowCodeConfig.projectCode}&pageCode=${encodeURIComponent(pageCode)}&page=1`;
      console.log('[集成Service] 查询低代码页面URL:', url);

      const response = await http.get(url);

      console.log('[集成Service] 查询低代码页面响应:', response);

      const result = await response.data;

      // 解析响应
      console.log('[集成Service] 查询低代码页面结果:', result);

      // 判断结果 - 这是一个分页列表，需要检查items数组
      if (result.code === 200 && result.data && result.data.items && result.data.items.length > 0) {
        // 找到了匹配pageCode的页面，返回第一个匹配项
        console.log('[集成Service] 低代码页面已存在, 页面信息:', result.data.items[0]);
        return result.data.items[0];
      } else if (result.code === 200) {
        // 查询成功但没有匹配项，表示页面不存在
        console.log('[集成Service] 低代码页面不存在, pageCode:', pageCode);
        return null;
      } else {
        // 其他情况，可能是服务端返回的其他错误
        console.warn('[集成Service] 查询低代码页面返回未预期的结果:', result);
        return null;
      }
    } catch (error) {
      console.error('[集成Service] 查询低代码页面出错:', error);
      return null; // 出错时返回null，认为页面不存在
    }
  },

  /**
   * 更新已存在的低代码平台页面内容
   * @param pageId 页面ID
   * @param pageName 页面名称
   * @param pageCode 页面编码
   * @param lowcodeConfig 低代码配置JSON
   * @returns 更新结果
   */
  updateLowcodePage: async (pageId: string, pageName: string, pageCode: string, lowcodeConfig: any): Promise<{ id: string, success: boolean, message: string }> => {
    console.log('[集成Service] 更新低代码页面, ID:', pageId);
    console.log('[集成Service] 更新低代码页面, ID类型:', typeof pageId);
    console.log('[集成Service] 更新低代码页面, ID值是否为空:', !pageId);

    try {
      // 将低代码配置转换为字符串，并进行URI编码
      const contentStr = JSON.stringify(lowcodeConfig);
      const encodedContent = encodeURIComponent(contentStr);

      // 检查编码后的内容
      console.log('[集成Service] 内容编码前10个字符:', contentStr.substring(0, 10));
      console.log('[集成Service] 内容编码后10个字符:', encodedContent.substring(0, 10));
      console.log('[集成Service] 内容是否已编码:', encodedContent.includes('%'));

      // 准备请求参数 - 根据接口要求，只需要content、pageId和thumbnailImage
      const requestData = {
        content: encodedContent,
        pageId: pageId,
        thumbnailImage: ''
      };

      console.log('[集成Service] 更新低代码页面请求参数:', requestData);
      console.log('[集成Service] 更新低代码页面请求参数JSON:', JSON.stringify(requestData));
      console.log('[集成Service] 请求中pageId字段:', 'pageId' in requestData, '值:', requestData.pageId);

      const token = localStorage.getItem('token');

      // 使用代理URL解决跨域问题
      const response = await http.post(`/pmc-server/page/savePageSchema`, requestData, {
        headers: {
          'Authorization': `Bearer ${token}`,
        }
      });

      let result = response.data;
      console.log('[集成Service] 更新低代码页面结果:', result);

      if (result.code === 200) {
        return {
          id: pageId,
          success: true,
          message: result.message || '更新成功'
        };
      } else {
        return {
          id: '',
          success: false,
          message: result.message || '更新失败'
        };
      }
    } catch (error) {
      console.error('[集成Service] 更新低代码页面失败:', error);
      return {
        id: '',
        success: false,
        message: error instanceof Error ? error.message : '未知错误'
      };
    }
  },

  /**
   * 发布集成到低代码平台
   * @param integrationId 集成ID
   * @param integrationName 集成名称
   * @param lowcodeConfig 低代码配置JSON
   * @returns 发布结果，包含低代码平台页面ID
   */
  publishToLowCode: async (integrationId: string, integrationName: string, lowcodeConfig: any): Promise<{ id: string, success: boolean, message: string }> => {
    console.log('[集成Service] 发布到低代码平台, ID:', integrationId);
    try {
      // 生成页面编码（将中文名称转换为拼音或使用ID）
      const pageCode = `integration_${integrationId.replace(/-/g, '_')}`;

      // 确保低代码配置有正确的结构
      if (!lowcodeConfig || typeof lowcodeConfig !== 'object') {
        console.warn('[集成Service] 低代码配置数据结构异常，使用默认结构');
        lowcodeConfig = {
          "globals": {},
          "components": {
            "entry": {
              "properties": {
                "fieldSet": {
                  "value": []
                }
              },
              "type": "entry",
              "events": [],
              "styles": {},
              "exposedVariables": {}
            }
          },
          "queries": [],
          "variables": {}
        };
      }

      // 首先查询页面是否存在
      console.log('[集成Service] 查询页面是否已存在, pageCode:', pageCode);
      const existingPage = await integrationService.queryLowcodePage(pageCode);

      // 根据查询结果判断是创建新页面还是更新已有页面
      if (existingPage) {
        console.log('[集成Service] 页面已存在，调用更新API');
        console.log('[集成Service] 现有页面信息类型:', typeof existingPage);
        console.log('[集成Service] 现有页面JSON:', JSON.stringify(existingPage));
        console.log('[集成Service] 现有页面字段列表:', Object.keys(existingPage));

        // 修复：使用正确的字段名pageId而不是id
        const pageIdValue = existingPage.pageId;
        console.log('[集成Service] 页面ID字段(pageId)值:', pageIdValue);

        if (!pageIdValue) {
          console.error('[集成Service] 警告: 无法从查询结果中获取页面ID!');
          throw new Error('无法从查询结果中获取页面ID(pageId)');
        }

        return await integrationService.updateLowcodePage(
          pageIdValue,
          integrationName,
          pageCode,
          lowcodeConfig
        );
      } else {
        console.log('[集成Service] 页面不存在，调用创建API');

        // 将低代码配置转换为字符串，并进行URI编码
        const contentStr = JSON.stringify(lowcodeConfig);
        const encodedContent = encodeURIComponent(contentStr);

        console.log('[集成Service] JSON编码前内容:', contentStr);
        console.log('[集成Service] URI编码后内容:', encodedContent);

        // 检查编码后的内容
        console.log('[集成Service] 创建页面-内容编码前10个字符:', contentStr.substring(0, 10));
        console.log('[集成Service] 创建页面-内容编码后10个字符:', encodedContent.substring(0, 10));
        console.log('[集成Service] 创建页面-内容是否已编码:', encodedContent.includes('%'));

        // 准备请求参数
        const requestData = {
          pageName: integrationName,
          pageCode: pageCode,
          projectCode: lowCodeConfig.projectCode,
          status: 'ENABLED',
          content: encodedContent
        };

        console.log('[集成Service] 创建低代码页面请求参数:', requestData);

        const token = localStorage.getItem('token');

        // 使用代理URL进行请求，解决跨域问题
        const response = await http.post(`${lowCodeConfig.createPageApiPath}`, requestData, {
          headers: {
            'Authorization': `Bearer ${token}`,
            // 使用相对URL不需要添加Authorization和systemcode头，
            // 因为Vite的代理会自动添加这些头
          }
        });

        let result = response.data;
        console.log('[集成Service] 创建低代码页面结果:', result);

        if (result.code === 200) {
          return {
            id: result.data,
            success: true,
            message: result.message || '发布成功'
          };
        } else {
          return {
            id: '',
            success: false,
            message: result.message || '发布失败'
          };
        }
      }
    } catch (error) {
      console.error('[集成Service] 发布到低代码平台失败:', error);
      return {
        id: '',
        success: false,
        message: error instanceof Error ? error.message : '未知错误'
      };
    }
  }
};
