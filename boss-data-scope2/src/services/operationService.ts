import instance from '@/utils/axios';
import type {
  Operation,
  OperationQueryParams,
  OperationPaginationResponse,
  CreateOperationParams,
  UpdateOperationParams,
  OperationExecution,
  Application,
  ProtocolType,
  ContentType
} from '@/types/operation';


const API_BASE = `/api/v1`;

/**
 * 操作服务
 */
export const operationService = {
  /**
   * 获取操作列表
   * @param params 查询参数
   */
  getOperations: async (params?: OperationQueryParams): Promise<OperationPaginationResponse> => {
    console.log('[操作Service] 获取操作列表, 参数:', params);
    const response = await instance.get(`${API_BASE}/operations`, { params });
    return response.data;
  },

  /**
   * 获取操作详情
   * @param id 操作ID
   */
  getOperationById: async (id: string): Promise<Operation> => {
    console.log(`[操作Service] 获取操作详情 (ID: ${id})`);
    const response = await instance.get(`${API_BASE}/operations/${id}`);
    return response.data;
  },

  /**
   * 创建操作
   * @param data 操作数据
   */
  createOperation: async (data: CreateOperationParams): Promise<Operation> => {
    console.log('[操作Service] 创建操作:', data);
    const response = await instance.post(`${API_BASE}/operations`, data);
    return response.data;
  },

  /**
   * 更新操作
   * @param id 操作ID
   * @param data 操作数据
   */
  updateOperation: async (id: string, data: UpdateOperationParams): Promise<Operation> => {
    console.log(`[操作Service] 更新操作 (ID: ${id}):`, data);
    const response = await instance.put(`${API_BASE}/operations/${id}`, data);
    return response.data;
  },

  /**
   * 删除操作
   * @param id 操作ID
   */
  deleteOperation: async (id: string): Promise<void> => {
    console.log(`[操作Service] 删除操作 (ID: ${id})`);
    await instance.delete(`${API_BASE}/operations/${id}`);
  },

  /**
   * 更新操作状态
   * @param id 操作ID
   * @param status 状态值
   */
  updateOperationStatus: async (id: string, status: 'ACTIVE' | 'INACTIVE' | 'DRAFT'): Promise<Operation> => {
    console.log(`[操作Service] 更新操作状态 (ID: ${id}, 状态: ${status})`);
    const response = await instance.patch(`${API_BASE}/operations/${id}/status`, { status });
    return response.data;
  },

  /**
   * 执行操作
   * @param id 操作ID
   * @param params 执行参数
   */
  executeOperation: async (id: string, params?: Record<string, any>): Promise<OperationExecution> => {
    console.log(`[操作Service] 执行操作 (ID: ${id}, 参数:`, params, ')');
    const response = await instance.post(`${API_BASE}/operations/${id}/execute`, params || {});
    return response.data;
  },

  /**
   * 获取操作历史记录
   * @param operationId 操作ID
   * @param params 分页参数
   */
  getOperationHistory: async (operationId: string, params?: { page?: number, size?: number }): Promise<{ items: OperationExecution[], total: number }> => {
    console.log(`[操作Service] 获取操作历史 (ID: ${operationId}, 参数:`, params, ')');
    const response = await instance.get(`${API_BASE}/operations/${operationId}/history`, { params });
    return response.data;
  },

  /**
   * 获取应用列表
   */
  getApplications: async (): Promise<Application[]> => {
    console.log('[操作Service] 获取应用列表');
    const response = await instance.get(`${API_BASE}/applications`);
    return response.data;
  },

  /**
   * 获取应用详情
   * @param id 应用ID
   */
  getApplicationById: async (id: string): Promise<Application> => {
    console.log(`[操作Service] 获取应用详情 (ID: ${id})`);
    const response = await instance.get(`${API_BASE}/applications/${id}`);
    return response.data;
  },

  /**
   * 测试操作执行
   * @param data 操作测试数据
   */
  testOperation: async (data: {
    applicationId: string;
    protocolType: ProtocolType;
    contentType: ContentType;
    apiEndpoint: string;
    apiMethod: string;
    apiParameters: any[];
    testValues: Record<string, any>;
  }): Promise<any> => {
    console.log('[操作Service] 测试操作执行:', data);
    const response = await instance.post(`${API_BASE}/operations/test`, data);
    return response.data;
  }
};
