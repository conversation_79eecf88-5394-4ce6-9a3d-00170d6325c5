import type { TableMetadata, ColumnMetadata, SchemaMetadata } from '@/types/metadata';
import { dataSourceService } from './datasource';

/**
 * 元数据服务 - 处理数据库元数据的获取和转换
 */
export const metadataService = {
  /**
   * 获取数据源的schema列表
   * @param dataSourceId 数据源ID
   * @returns 包含name和value的schema对象数组
   */
  async getSchemas(dataSourceId: string): Promise<SchemaMetadata[]> {
    try {
      // 从数据源服务获取schema列表
      const response = await dataSourceService.getSchemas(dataSourceId);
      console.log(`[MetadataService] 从数据源 ${dataSourceId} 获取到原始响应:`, response);
      
      if (!response || !Array.isArray(response)) {
        console.warn('[MetadataService] 接收到无效的schemas数据:', response);
        return [];
      }
      
      // 将后端返回的数据映射为前端需要的SchemaMetadata格式
      const schemas: SchemaMetadata[] = response.map(item => ({
        value: item.id,            // 使用schema的id作为value
        name: item.name,           // 使用schema的name作为显示名称
        dataSourceId: dataSourceId, // 设置关联的数据源ID
        tablesCount: item.tablesCount || 0,  // 可能包含表的数量信息
        description: item.description || ''  // 可能包含描述信息
      }));
      
      console.log(`[MetadataService] 处理后的schema数据:`, schemas);
      
      // 检查处理后的schemas是否有效
      if (schemas.length === 0) {
        // 创建一个缺省schema作为后备方案
        schemas.push({
          value: 'default',
          name: 'Default Schema',
          dataSourceId
        });
        console.log('[MetadataService] 未检测到有效schema，使用默认schema');
      }
      
      return schemas;
    } catch (error) {
      console.error(`[MetadataService] 获取schema列表失败: ${error}`);
      return [];
    }
  },
  
  /**
   * 格式化schema名称
   * @param schema schema字符串
   * @returns 格式化后的名称
   */
  formatSchemaName(schema: string): string {
    // 在此可以添加自定义的格式化逻辑
    return schema;
  }
};

export default metadataService; 