/**
 * 数据源服务单元测试
 */
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { dataSourceService } from '../datasource'

// 模拟fetch请求
global.fetch = vi.fn()

describe('数据源服务', () => {
  beforeEach(() => {
    vi.resetAllMocks()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  // 测试获取数据源列表
  it('getDataSources - 成功返回数据源列表', async () => {
    // 模拟成功响应
    const mockResponse = {
      success: true,
      code: 200,
      message: '操作成功',
      data: {
        page: 1,
        size: 10,
        total: 3,
        pages: 1,
        items: [
          {
            id: 'test-id-1',
            name: '测试数据源1',
            type: 'mysql',
            host: 'localhost',
            port: 3306
          }
        ]
      }
    }

    // 模拟fetch响应
    global.fetch = vi.fn().mockResolvedValueOnce({
      ok: true,
      json: async () => mockResponse
    })

    // 调用方法
    const result = await dataSourceService.getDataSources()

    // 验证结果
    expect(result).toHaveProperty('items')
    expect(result.items).toHaveLength(1)
    expect(result.items[0].id).toBe('test-id-1')
    
    // 验证请求URL
    expect(global.fetch).toHaveBeenCalledWith(expect.stringContaining('/api/datasources'), expect.any(Object))
  })

  // 测试创建数据源
  it('createDataSource - 成功创建数据源', async () => {
    // 模拟请求数据
    const createData = {
      name: '新数据源',
      description: '测试描述',
      type: 'mysql',
      host: 'localhost',
      port: 3306,
      databaseName: 'test_db',
      username: 'user',
      password: 'pass',
      syncFrequency: 'daily'
    }

    // 模拟成功响应
    const mockResponse = {
      success: true,
      code: 200,
      message: '数据源创建成功',
      data: {
        id: 'new-id',
        name: '新数据源',
        description: '测试描述',
        type: 'mysql',
        host: 'localhost',
        port: 3306
      }
    }

    // 模拟fetch响应
    global.fetch = vi.fn().mockResolvedValueOnce({
      ok: true,
      json: async () => mockResponse
    })

    // 调用方法
    const result = await dataSourceService.createDataSource(createData)

    // 验证结果
    expect(result).toHaveProperty('id', 'new-id')
    expect(result.name).toBe('新数据源')
    
    // 验证请求URL和请求体
    expect(global.fetch).toHaveBeenCalledWith(
      expect.stringContaining('/api/datasources'),
      expect.objectContaining({
        method: 'POST',
        headers: expect.objectContaining({
          'Content-Type': 'application/json'
        }),
        body: expect.any(String)
      })
    )
    
    // 验证请求体内容
    const requestBody = JSON.parse(global.fetch.mock.calls[0][1].body)
    expect(requestBody.name).toBe(createData.name)
    expect(requestBody.type).toBe(createData.type)
  })

  // 测试测试连接
  it('testConnection - 成功测试连接', async () => {
    // 模拟请求参数
    const testParams = {
      type: 'mysql',
      host: 'localhost',
      port: 3306,
      databaseName: 'test_db',
      username: 'user',
      password: 'pass'
    }

    // 模拟成功响应
    const mockResponse = {
      success: true,
      code: 200,
      message: '操作成功',
      data: {
        success: true,
        message: '连接成功',
        details: null
      }
    }

    // 模拟fetch响应
    global.fetch = vi.fn().mockResolvedValueOnce({
      ok: true,
      json: async () => mockResponse
    })

    // 调用方法
    const result = await dataSourceService.testConnection(testParams)

    // 验证结果
    expect(result.success).toBe(true)
    expect(result.message).toBe('连接成功')
    
    // 验证请求URL
    expect(global.fetch).toHaveBeenCalledWith(
      expect.stringContaining('/api/datasources/test-connection'),
      expect.any(Object)
    )
  })

  // 测试检查数据源状态
  it('checkDataSourceStatus - 成功检查数据源状态', async () => {
    // 模拟成功响应
    const mockResponse = {
      success: true,
      code: 200,
      message: '操作成功',
      data: {
        id: 'test-id',
        status: 'active',
        isActive: true,
        lastCheckedAt: [2025,4,5,15,33,31,453437000],
        message: '连接正常',
        details: {
          activeConnections: 3,
          responseTime: 45,
          connectionPoolSize: 10
        }
      }
    }

    // 模拟fetch响应
    global.fetch = vi.fn().mockResolvedValueOnce({
      ok: true,
      json: async () => mockResponse
    })

    // 调用方法
    const result = await dataSourceService.checkDataSourceStatus('test-id')

    // 验证结果
    expect(result.id).toBe('test-id')
    expect(result.status).toBe('active')
    expect(result.isActive).toBe(true)
    
    // 验证请求URL
    expect(global.fetch).toHaveBeenCalledWith(
      expect.stringContaining('/api/datasources/test-id/check-status')
    )
  })
}) 