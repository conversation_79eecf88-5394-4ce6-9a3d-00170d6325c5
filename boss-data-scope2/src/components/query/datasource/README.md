# 数据源面板组件 (DataSourcePanel)

## 概述

`DataSourcePanel` 组件是一个集成了数据源选择、Schema选择和元数据浏览功能的面板组件，用于简化查询编辑器等页面的实现。该组件整合了数据源状态管理、数据加载和元数据刷新等功能，提供了统一的接口。

## 功能特点

- 数据源选择（使用现有的 `DataSourceSelector` 组件）
- Schema选择
- 元数据刷新
- 表和字段浏览（使用 `MetadataExplorer` 组件）
- 已保存查询列表显示（通过插槽）
- 标签页切换
- 状态和错误处理

## 使用方法

```vue
<DataSourcePanel
  v-model:activePanel="leftPanel"
  @data-source-change="handleDataSourceChange"
  @schema-change="handleSchemaSelection"
  @table-select="handleTableSelect"
  @column-select="handleColumnSelect"
  @insert-table="insertTableName"
  @insert-column="insertColumnName"
>
  <template #saved-queries>
    <!-- 已保存的查询列表内容 -->
  </template>
</DataSourcePanel>
```

## 属性

| 属性名 | 类型 | 默认值 | 说明 |
|-------|------|-------|------|
| activePanel | String | 'metadata' | 当前激活的面板，可选值：'metadata' 或 'saved' |

## 事件

| 事件名 | 参数 | 说明 |
|-------|------|------|
| update:activePanel | (value: string) | 面板切换事件 |
| data-source-change | (id: string, dataSource: DataSource\|null) | 数据源变更事件 |
| schema-change | (schema: string) | Schema变更事件 |
| table-select | (table: any) | 表格选择事件 |
| column-select | (column: any, table: any) | 列选择事件 |
| insert-table | (tableName: string) | 插入表名事件 |
| insert-column | (columnName: string) | 插入列名事件 |

## 插槽

| 插槽名 | 说明 |
|-------|------|
| saved-queries | 用于自定义已保存查询列表的内容 |

## 优势

1. **减少代码重复**：将数据源选择、Schema选择和元数据刷新等功能封装到一个组件中，避免在多个页面重复实现相同的逻辑。

2. **简化主组件**：使用该组件可以大幅减少主组件（如 `QueryEditor.vue`）的代码量，提高可维护性。

3. **统一状态管理**：内部统一管理数据源和Schema的状态，减少状态管理的复杂度。

4. **统一错误处理**：集中处理数据加载和刷新过程中的错误，提供一致的用户体验。

5. **可复用性**：可以在查询编辑器、数据分析、数据可视化等多个模块中复用。

## 依赖

- Vue 3
- Pinia
- Ant Design Vue
- @/components/datasource/DataSourceSelector.vue
- @/components/query/MetadataExplorer.vue

## 示例

在查询编辑器页面中使用：

```vue
<template>
  <div class="grid grid-cols-12 gap-6">
    <!-- 左侧面板 -->
    <div class="col-span-3">
      <DataSourcePanel
        v-model:activePanel="leftPanel"
        @data-source-change="handleDataSourceChange"
        @schema-change="handleSchemaSelection"
        @table-select="handleTableSelect"
        @column-select="handleColumnSelect"
        @insert-table="insertTableName"
        @insert-column="insertColumnName"
      >
        <template #saved-queries>
          <!-- 自定义已保存查询列表 -->
        </template>
      </DataSourcePanel>
    </div>
    
    <!-- 右侧编辑器和结果区域 -->
    <div class="col-span-9">
      <!-- 编辑器和结果内容 -->
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import DataSourcePanel from '@/components/query/datasource/DataSourcePanel.vue'

// 状态
const leftPanel = ref('metadata')

// 处理函数
const handleDataSourceChange = (dsId, dataSource) => {
  // 处理数据源变更
}

const handleSchemaSelection = (schema) => {
  // 处理Schema变更
}

// 其他处理函数...
</script>
``` 