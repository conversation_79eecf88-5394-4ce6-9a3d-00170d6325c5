<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useDataSourceStore } from '@/stores/datasource'
import type { ExtendedTableMetadata, TableMetadata, ColumnMetadata } from '@/types/metadata'
import { NIcon, NEmpty, NSpin, NDivider, NAlert } from 'naive-ui'
import { NScrollbar, NTree, NSpace } from 'naive-ui'
import { getApiUrl } from '@/services/apiUtils'
import { SearchOutline as SearchIcon } from '@vicons/ionicons5'
import { http } from '@/utils/http'
import instance from "@/utils/axios";

interface Props {
  tables: ExtendedTableMetadata[]
  loading?: boolean
  error?: { type: string; message: string } | null
  noSchema?: boolean
  dataSourceId?: string
  schemaId?: string
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  error: null,
  noSchema: false,
  dataSourceId: '',
  schemaId: ''
})

const emit = defineEmits<{
  (e: 'tables-loaded', tables: ExtendedTableMetadata[]): void
  (e: 'table-select', table: TableMetadata): void
  (e: 'column-select', column: ColumnMetadata, table: TableMetadata): void
}>()

// 本地状态
const searchQuery = ref('')
const expandedKeys = ref<string[]>([])
const selectedTableKey = ref<string | null>(null)
const selectedColumnKey = ref<string | null>(null)
const isLoadingColumns = ref<Record<string, boolean>>({})
const tableColumnsMap = ref<Map<string, ColumnMetadata[]>>(new Map())
const dataSourceStore = useDataSourceStore()

// 过滤后的表格数据
const filteredTables = computed(() => {
  if (!searchQuery.value || !props.tables) return props.tables

  const query = searchQuery.value.toLowerCase()
  return props.tables.filter(table => {
    // 先检查表名是否匹配
    if (table.name?.toLowerCase().includes(query)) return true

    // 再检查表描述是否匹配
    if (table.description?.toLowerCase().includes(query)) return true

    // 检查已加载的列是否匹配
    const columns = tableColumnsMap.value.get(table.name || '')
    if (columns) {
      return columns.some(col =>
        col.name?.toLowerCase().includes(query) ||
        col.description?.toLowerCase().includes(query) ||
        col.type?.toLowerCase().includes(query)
      )
    }

    return false
  })
})

// 构造树形结构
const treeData = computed(() => {
  return filteredTables.value.map(table => {
    const tableKey = `table:${table.name}`
    const columns = tableColumnsMap.value.get(table.name || '') || []

    return {
      key: tableKey,
      label: table.name,
      isLeaf: false,
      prefix: () => h(NIcon, null, { default: () => h('span', { class: 'i-mdi-table mr-1' }) }),
      suffix: () => table.description ? h('span', { class: 'text-xs text-gray-400 ml-2' }, table.description) : null,
      children: columns.map(column => {
        const columnKey = `column:${table.name}:${column.name}`
        return {
          key: columnKey,
          label: column.name,
          isLeaf: true,
          prefix: () => h(NIcon, null, { default: () => h('span', { class: 'i-mdi-table-column mr-1' }) }),
          suffix: () => h('span', { class: 'text-xs text-gray-400 ml-2' }, [
            column.type ? h('span', { class: 'text-gray-500' }, column.type) : null,
            column.description ? h('span', { class: 'ml-1' }, column.description) : null
          ])
        }
      })
    }
  })
})

// 加载表的列数据
const loadTableColumns = async (tableName: string, force = false) => {
  if (!props.dataSourceId || !tableName || (!props.schemaId && !props.noSchema)) {
    console.warn('TableViewer - loadTableColumns: 缺少必要的参数')
    return
  }

  // 检查是否已加载，避免重复请求
  if (!force && tableColumnsMap.value.has(tableName) && tableColumnsMap.value.get(tableName)?.length) {
    console.log(`TableViewer - loadTableColumns: 已有表 ${tableName} 的列数据，跳过加载`)
    return
  }

  // 如果正在加载，跳过
  if (isLoadingColumns.value[tableName]) {
    console.log(`TableViewer - loadTableColumns: 表 ${tableName} 的列数据正在加载，跳过`)
    return
  }

  try {
    isLoadingColumns.value = { ...isLoadingColumns.value, [tableName]: true }

    let schemaId = props.schemaId

    // 如果没有schemaId但有noSchema标记，尝试使用其他方式构建URL
    if (!schemaId && props.noSchema) {
      // 尝试从数据源获取默认schema
      try {
        const dataSource = await dataSourceStore.getDataSourceById(props.dataSourceId)
        if (dataSource) {
          if (dataSource.database) {
            schemaId = dataSource.database
          } else if (dataSource.databaseName) {
            schemaId = dataSource.databaseName
          } else if (dataSource.name) {
            schemaId = dataSource.name.toLowerCase().replace(/\s+/g, '_')
          } else {
            schemaId = `db_${props.dataSourceId.substring(0, 8)}`
          }
        }
      } catch (err) {
        console.error('TableViewer - loadTableColumns: 获取数据源详情失败:', err)
        schemaId = `db_${props.dataSourceId.substring(0, 8)}`
      }
    }

    const baseUrl = getApiUrl()
    const apiPrefix = baseUrl.endsWith('/api') ? '' : '/api'
    const url = `${baseUrl}${apiPrefix}/metadata/schemas/${schemaId}/tables/${tableName}/columns`

    console.log(`TableViewer - loadTableColumns: 加载表 ${tableName} 的列数据, URL:`, url)

    const response = await instance.get(url)

    const data = response.data

    if (!data.success) {
      throw new Error(data.message || '加载列数据失败')
    }

    const columns = data.data || []
    if (Array.isArray(columns)) {
      console.log(`TableViewer - loadTableColumns: 表 ${tableName} 成功加载 ${columns.length} 个列`)
      tableColumnsMap.value.set(tableName, columns)
    } else {
      console.warn(`TableViewer - loadTableColumns: 表 ${tableName} 的列数据格式不正确`)
      tableColumnsMap.value.set(tableName, [])
    }
  } catch (err) {
    console.error(`TableViewer - loadTableColumns: 加载表 ${tableName} 的列数据失败:`, err)
    tableColumnsMap.value.set(tableName, [])
  } finally {
    isLoadingColumns.value = { ...isLoadingColumns.value, [tableName]: false }
  }
}

// 处理树节点展开
const handleTreeExpand = (keys: string[]) => {
  expandedKeys.value = keys

  // 当展开表节点时，加载其列数据
  const newlyExpandedKeys = keys.filter(key => !expandedKeys.value.includes(key))
  for (const key of newlyExpandedKeys) {
    if (key.startsWith('table:')) {
      const tableName = key.substring(6) // 去除 'table:' 前缀
      loadTableColumns(tableName)
    }
  }
}

// 处理节点选择
const handleTreeSelect = (keys: string[], option: { node: any }) => {
  if (!keys.length) return

  const key = keys[0]

  if (key.startsWith('table:')) {
    const tableName = key.substring(6)
    selectedTableKey.value = key
    selectedColumnKey.value = null

    // 查找对应的表对象
    const table = props.tables.find(t => t.name === tableName)
    if (table) {
      emit('table-select', table)
    }

    // 如果还没有加载列信息，加载它
    if (!tableColumnsMap.value.has(tableName) || !tableColumnsMap.value.get(tableName)?.length) {
      loadTableColumns(tableName)
    }
  } else if (key.startsWith('column:')) {
    selectedColumnKey.value = key

    // 解析列键以获取表名和列名
    const [, tableName, columnName] = key.split(':')

    // 查找对应的表和列
    const table = props.tables.find(t => t.name === tableName)
    const columns = tableColumnsMap.value.get(tableName) || []
    const column = columns.find(c => c.name === columnName)

    if (table && column) {
      emit('column-select', column, table)
    }
  }
}

// 当tables变化时，通知父组件
watch(() => props.tables, (newTables) => {
  if (newTables && newTables.length > 0) {
    emit('tables-loaded', newTables)
  }
}, { immediate: true })

// 当dataSourceId或schemaId变化时，清空已选择的状态
watch([() => props.dataSourceId, () => props.schemaId], () => {
  selectedTableKey.value = null
  selectedColumnKey.value = null
  expandedKeys.value = []
  // 保留已加载的列数据，以防再次需要相同数据源/schema
})
</script>

<template>
  <div class="table-viewer h-full flex flex-col">
    <!-- 搜索栏 -->
    <div v-if="tables && tables.length > 0" class="mb-2 flex">
      <div class="relative w-full">
        <input
          v-model="searchQuery"
          type="text"
          :placeholder="searchQuery ? '' : '搜索表名或列名...'"
          class="w-full pl-8 pr-2 py-1 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
        <div class="absolute left-2 top-1/2 transform -translate-y-1/2">
          <NIcon>
            <SearchIcon />
          </NIcon>
        </div>
      </div>
    </div>

    <!-- 错误提示 -->
    <NAlert v-if="error" :type="error.type" :title="error.type === 'error' ? '错误' : '提示'" class="mb-2">
      {{ error.message }}
    </NAlert>

    <!-- 加载中状态 -->
    <NSpin v-if="loading" size="medium" class="w-full h-32 flex items-center justify-center">
      <template #description>加载表数据中...</template>
    </NSpin>

    <!-- 空状态 -->
    <NEmpty v-else-if="!tables || tables.length === 0" description="暂无数据表" class="my-4" size="large" />

    <!-- 表格树 -->
    <NScrollbar v-else style="max-height: calc(100vh - 180px);" trigger="hover" class="flex-1">
      <NTree
        :data="treeData"
        block-line
        :expanded-keys="expandedKeys"
        :selected-keys="selectedTableKey ? [selectedTableKey] : (selectedColumnKey ? [selectedColumnKey] : [])"
        selectable
        @update:expanded-keys="handleTreeExpand"
        @update:selected-keys="handleTreeSelect"
      />
    </NScrollbar>
  </div>
</template>

<style scoped>
.table-viewer {
  width: 100%;
}
</style>
