<script setup lang="ts">
import { ref, computed, onMounted, reactive } from 'vue'
import { watch } from '@/plugins/vue-types-fix'
import type {
  TableMetadata,
  ColumnMetadata,
  ExtendedTableMetadata,
  TablesResponse,
  ColumnsResponse,
  TableMetadataArray,
  ExtendedTableMetadataArray,
  ColumnMetadataArray,
  MetadataState
} from '@/types/metadata'
import type { DataSource } from '@/types/datasource'
import { useDataSourceStore } from '@/stores/datasource'
import { dataSourceService } from '@/services/datasource'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { useRoute } from 'vue-router'
import { getMetadataApiUrl, getApiUrl } from '@/services/apiUtils'
import { formatDate } from '@/utils/date'
import { getApiBasePath, getMetadataApiPath } from '@/utils/config'
import axios from 'axios'
import { http } from '@/utils/http'
import instance from "@/utils/axios";

interface Props {
  dataSourceId?: string
  selectedSchema?: string
  searchKeyword?: string
  schema?: string
}

const props = defineProps<Props>()

const emit = defineEmits<{
  (e: 'tablesLoaded', tables: ExtendedTableMetadata[]): void
  (e: 'error', error: Error): void
  (e: 'table-select', table: TableMetadata): void
  (e: 'column-select', column: ColumnMetadata, table: TableMetadata): void
  (e: 'insert-table', tableName: string): void
  (e: 'insert-column', columnName: string): void
  (e: 'tables-loaded', tables: ExtendedTableMetadata[]): void
  (e: 'columnsLoaded', tableName: string): void
  (e: 'noSchema', noSchema: boolean): void
  (e: 'schema-selected', schemaId: string): void
}>()

// 数据源存储
const dataSourceStore = useDataSourceStore()

interface ErrorMessage {
  type: 'error' | 'warning' | 'info'
  message: string
}

interface State {
  loading: boolean
  error: ErrorMessage | null
  tables: ExtendedTableMetadata[]
  expandedTables: string[]
  searchTerm: string
  highlightedTable: string | null
  schemas: string[]
  noSchema: boolean
  isFirstLoad: boolean // 标记是否是首次加载数据源
}

const state = reactive<State>({
  loading: false,
  error: null,
  tables: [],
  expandedTables: [],
  searchTerm: '',
  highlightedTable: null,
  schemas: [],
  noSchema: false,
  isFirstLoad: true // 初始化为true，表示首次加载
})

// 获取数据源的元数据
const getMetadataForDataSource = (dataSourceId: string) => {
  // 查找对应数据源的元数据
  const dataSource = dataSourceStore.dataSources.find((ds: any) => ds.id === dataSourceId)
  return dataSource?.metadata
}

// 加载元数据
const loadMetadata = async (dataSourceId: string) => {
  if (!dataSourceId) return

  // 防止重复加载
  if (state.loading) {
    console.log('MetadataExplorer - 正在加载中，跳过重复请求');
    return;
  }

  console.log('MetadataExplorer - 开始加载元数据，数据源ID:', dataSourceId)
  state.loading = true
  state.error = null

  try {
    // 首先检查store中是否已有schemas数据
    const cachedSchemas = dataSourceStore.metadataState.schemas.get(dataSourceId);
    console.log('MetadataExplorer - 检查store中是否已有schemas数据:', cachedSchemas ? `有${cachedSchemas.length}个schemas` : '无数据');

    // 如果已有schema数据，则使用现有数据
    if (cachedSchemas && cachedSchemas.length >= 0) {
      state.schemas = cachedSchemas;

      // 无schema情况的处理
      if (cachedSchemas.length === 0) {
        console.log('MetadataExplorer - 数据源没有可用的schema（从store确认）');
        state.noSchema = true;
        emit('noSchema', true);

        // 检查是否已有表数据
        const cachedTables = dataSourceStore.getTablesFromCache(dataSourceId);
        if (cachedTables && cachedTables.length > 0) {
          console.log('MetadataExplorer - 使用store中缓存的表数据');
          state.tables = cachedTables.map((table: any) => ({
            ...table,
            isUpdating: false,
            lastUpdated: new Date().toISOString(),
            error: undefined
          }));
          emit('tablesLoaded', state.tables);
        } else {
          // 如果尚无表数据，需要加载
          console.log('MetadataExplorer - 无schema数据源，需要加载表数据');
          await loadTablesWithoutSchema(dataSourceId);
        }
      }

      // 处理完成
      return;
    }

    // 如果store中没有schemas数据，使用store中API获取
    console.log('MetadataExplorer - store中无schemas数据，需要获取');

    // 尝试使用store的方法获取schemas
    if (typeof dataSourceStore.getSchemas === 'function') {
      // 直接使用store获取，避免直接调用API造成重复请求
      console.log('MetadataExplorer - 通过 dataSourceStore.getSchemas 获取数据');
      await dataSourceStore.getSchemas(dataSourceId);

      // 从store获取获取到的schemas
      const schemas = dataSourceStore.metadataState.schemas.get(dataSourceId) || [];
      console.log('MetadataExplorer - 通过store.getSchemas获取到schemas:', schemas.length);
      state.schemas = schemas;

      // 如果没有schema，设置标识并加载表结构
      if (schemas.length === 0) {
        console.log('MetadataExplorer - 数据源没有可用的schema，直接加载表结构');
        state.noSchema = true;
        emit('noSchema', true);
        await loadTablesWithoutSchema(dataSourceId);
      }
    } else {
      // 回退到直接API调用（不推荐）
      console.log('MetadataExplorer - 找不到store.getSchemas方法，回退到直接API调用');
      const schemasUrl = getMetadataApiUrl('schemas', { dataSourceId }) + `?_t=${Date.now()}`;

      try {
        console.log('MetadataExplorer - 直接请求schemas: ' + schemasUrl);
        const schemasResponse = await instance.get(schemasUrl);

        if (!schemasResponse || schemasResponse.status !== 200) {
          throw new Error(`获取schemas失败: ${schemasResponse?.statusText || '未知错误'}`);
        }

        const schemasData = schemasResponse.data;
        if (!schemasData.success || !Array.isArray(schemasData.data)) {
          throw new Error('获取数据库schemas失败');
        }

        // 保存获取到的schemas
        state.schemas = schemasData.data;

        // 处理无schema的情况
        if (schemasData.data.length === 0) {
          console.log('MetadataExplorer - 数据源没有可用的schema（从API确认），直接加载表结构');
          state.noSchema = true;
          emit('noSchema', true);
          await loadTablesWithoutSchema(dataSourceId);
        }
      } catch (err) {
        console.error('MetadataExplorer - 直接获取schemas失败:', err);
        throw err;
      }
    }
  } catch (err) {
    console.error('MetadataExplorer - 加载元数据失败:', err);
    state.error = handleError(err)
  } finally {
    state.loading = false;
    // 首次加载完成后，将isFirstLoad设置为false
    state.isFirstLoad = false;
    console.log('MetadataExplorer - 元数据加载流程完成，首次加载状态已重置');
  }
}

// 监听searchKeyword，更新searchTerm
watch(() => props.searchKeyword, (newKeyword: string | undefined) => {
  if (newKeyword !== undefined) {
    state.searchTerm = newKeyword
  }
}, { immediate: true })

// 监听选中的数据源变化
watch(() => props.dataSourceId, async (newId: string, oldId: string) => {
  // 避免首次加载或相同值重复触发
  if (!newId || newId === oldId) {
    return;
  }

  console.log('MetadataExplorer - 数据源ID变更:', newId);
  console.log('MetadataExplorer - 当前选中的schema:', props.selectedSchema || props.schema);

  // 重置状态
  state.tables = [];
  state.expandedTables = [];
  state.error = null;
  state.noSchema = false; // 重置无schema标记

  // 如果是首次加载，保持isFirstLoad为true，否则设置为false
  // 这样我们可以区分首次加载和后续加载

  // 防止重复加载，首先检查是否已经在加载中
  if (state.loading) {
    console.log('MetadataExplorer - 正在加载中，跳过重复请求');
    return;
  }

  // 直接从存储中获取schemas数据
  try {
    console.log('MetadataExplorer - 通过store获取schemas数据，newId =', newId);
    state.loading = true;

    // 使用store方法加载 schemas
    if (typeof dataSourceStore.getSchemas === 'function') {
      // 仅当缓存中没有数据时才调用API
      const cachedSchemas = dataSourceStore.metadataState.schemas.get(newId);
      if (!cachedSchemas || cachedSchemas.length === 0) {
        console.log('MetadataExplorer - 缓存中无schemas数据，将调用API获取');
        await dataSourceStore.getSchemas(newId);
      } else {
        console.log(`MetadataExplorer - 使用缓存的schemas数据 (${cachedSchemas.length} schemas)`);
      }
    }

    // 获取新数据源的schemas并记录详细信息
    const newSchemas = dataSourceStore.metadataState.schemas.get(newId) || [];
    console.log('MetadataExplorer - 新数据源的schemas详情:', JSON.stringify(newSchemas));

    // 优先使用新数据源的第一个schema
    if (newSchemas.length > 0) {
      const firstSchema = newSchemas[0];
      const schemaId = firstSchema.id || firstSchema.value || firstSchema.name;
      console.log(`MetadataExplorer - 自动使用新数据源的第一个schema: ${schemaId}`);

      // 发出事件通知父组件，已选择新的schema
      // 这一步是修复的关键，确保父组件知道我们选择了新的schema
      emit('schema-selected', schemaId);

      // 使用缓存的表数据（如果有）
      const cachedTables = dataSourceStore.getTablesFromCache(newId, schemaId);
      if (cachedTables && cachedTables.length > 0) {
        console.log(`MetadataExplorer - 使用缓存的表数据 (${cachedTables.length} 张表)`);
        state.tables = cachedTables.map((table: TableMetadata): ExtendedTableMetadata => ({
          ...table,
          isUpdating: false,
          lastUpdated: new Date().toISOString(),
          error: undefined
        }));
        emit('tablesLoaded', state.tables);
      } else {
        // 如果没有缓存，直接使用schemaId加载表结构，不依赖props中的schema值
        // 这是关键修复点：直接使用我们刚刚确定的schemaId，而不是等待props更新
        console.log(`MetadataExplorer - 无缓存数据，将直接使用schemaId: ${schemaId}加载表结构`);
        await loadTables(schemaId);
      }
    } else if (newSchemas.length === 0) {
      // 如果没有schemas，设置无schema状态并加载表结构
      console.log('MetadataExplorer - 新数据源无schemas，尝试无schema模式加载');
      state.noSchema = true;
      emit('noSchema', true);
      await loadTablesWithoutSchema(newId);
    }
  } catch (error) {
    console.error('MetadataExplorer - 自动加载数据失败:', error);
    state.error = handleError(error);
  } finally {
    state.loading = false;
    // 数据源变更加载完成后，将isFirstLoad设置为false
    state.isFirstLoad = false;
  }
}, { immediate: true }) // 改为true，确保在组件挂载时就加载数据

// 监听Schema变化，自动加载表结构
watch(() => [props.selectedSchema, props.schema], async (newValues: string[], oldValues: string[]) => {
  const [newSelectedSchema, newSchema] = newValues;
  const [oldSelectedSchema, oldSchema] = oldValues;
  const schemaToUse = newSelectedSchema || newSchema;
  const oldSchemaToUse = oldSelectedSchema || oldSchema;

  // 避免相同值重复触发
  if (schemaToUse === oldSchemaToUse || !schemaToUse) {
    return;
  }

  if (props.dataSourceId && schemaToUse) {
    console.log('MetadataExplorer - Schema变更, 自动加载表结构:', schemaToUse);

    // 防止重复加载，检查是否已经在加载中
    if (state.loading) {
      console.log('MetadataExplorer - 正在加载中，跳过重复请求');
      return;
    }

    try {
      state.loading = true;
      state.error = null;

      // 首先检查是否已有需要的数据
      const cachedTables = dataSourceStore.getTablesFromCache(props.dataSourceId, schemaToUse);
      if (cachedTables && cachedTables.length > 0) {
        console.log(`MetadataExplorer - 使用缓存的表数据 (${cachedTables.length} 张表)`);
        state.tables = cachedTables.map((table: TableMetadata): ExtendedTableMetadata => ({
          ...table,
          isUpdating: false,
          lastUpdated: new Date().toISOString(),
          error: undefined
        }));
        emit('tablesLoaded', state.tables);
      } else {
        // 无缓存时才请求新数据
        await loadTables(schemaToUse);
      }
    } catch (error) {
      console.error('MetadataExplorer - 加载表结构出错:', error);
      state.error = handleError(error);
    } finally {
      state.loading = false;
    }
  }
}, { immediate: false }) // 保持false，避免与数据源监听器重复触发

// 辅助函数：安全发送请求并处理各种错误情况
const safeRequest = async (url: string, options: any = {}): Promise<any> => {
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`MetadataExplorer - safeRequest[${requestId}]: 开始请求 ${url}`);

  try {
    // 设置超时，避免请求长时间挂起
    const timeoutMs = options.timeout || 30000; // 默认30秒超时

    // 创建并发送请求
    const response = await instance.get(url, {
      ...options,
      timeout: timeoutMs
    });

    console.log(`MetadataExplorer - safeRequest[${requestId}]: 请求成功`);
    return response;
  } catch (err: any) {
    // 处理错误并记录
    console.error(`MetadataExplorer - safeRequest[${requestId}]: 请求失败:`, err);

    // 记录错误类型和详情
    if (err.message) {
      console.error(`MetadataExplorer - safeRequest[${requestId}]: 错误信息: ${err.message}`);
    }

    if (err.response) {
      console.error(`MetadataExplorer - safeRequest[${requestId}]: 服务器响应:`, {
        status: err.response.status,
        statusText: err.response.statusText
      });
    }

    // 返回一个默认响应对象，避免后续处理中断
    return {
      data: { data: [] },
      status: 200,
      statusText: '请求出错，返回默认空数据'
    };
  }
};

// 加载表数据
const loadTables = async (schemaId: string | undefined) => {
  if (!schemaId) {
    console.error('MetadataExplorer - loadTables: schemaId 为空，无法加载表数据');
    return;
  }

  // 确保 schemaId 是有效的字符串
  if (typeof schemaId !== 'string' || schemaId === '{schemaId}') {
    console.error(`MetadataExplorer - loadTables: 无效的 schemaId: ${schemaId}, 类型: ${typeof schemaId}`);
    state.error = {
      type: 'error',
      message: '无效的模式标识符，请重新选择架构'
    };
    return;
  }

  console.log(`MetadataExplorer - loadTables: 使用schemaID=${schemaId}加载表结构`);

  // 防止重复加载
  if (state.loading) {
    console.log('MetadataExplorer - loadTables: 正在加载中，跳过重复请求');
    return;
  }

  try {
    state.loading = true;
    state.error = null;

    // 1. 从store获取完整schema对象以确保有真正的ID
    if (props.dataSourceId) {
      const schemas = dataSourceStore.metadataState?.schemas?.get(props.dataSourceId) || [];
      console.log(`MetadataExplorer - loadTables: 当前数据源(${props.dataSourceId})下可用的schemas:`, JSON.stringify(schemas));

      const schemaObj = schemas.find((s: any) => s.value === schemaId || s.id === schemaId || s.name === schemaId);

      if (schemaObj && schemaObj.id && schemaObj.id !== schemaId) {
        console.log(`MetadataExplorer - loadTables: 找到完整schema对象，使用实际ID=${schemaObj.id}替代${schemaId}`);
        schemaId = schemaObj.id;
      } else if (schemaObj) {
        console.log(`MetadataExplorer - loadTables: 找到匹配的schema对象:`, JSON.stringify(schemaObj));
      } else {
        console.log(`MetadataExplorer - loadTables: 警告! 在当前数据源中未找到匹配的schema对象，继续使用原始ID=${schemaId}`);
      }
    }

    // 2. 强制使用固定格式的URL结构
    const baseUrl = getApiUrl();
    const apiPrefix = baseUrl.endsWith('/api') ? '' : '/api';
    const apiUrl = `${baseUrl}${apiPrefix}/metadata/schemas/${schemaId}/tables`;

    console.log(`MetadataExplorer - loadTables: 加载表数据, URL: ${apiUrl}, schema ID: ${schemaId}`);

    // 3. 添加时间戳，方便跟踪请求
    const requestTime = new Date().toISOString();
    console.log(`MetadataExplorer - loadTables: 开始发送请求 [${requestTime}]`);

    try {
      // 使用安全请求函数替代直接的axios调用
      const response = await safeRequest(apiUrl);

      // 4. 记录详细的响应信息
      console.log(`MetadataExplorer - loadTables: 收到响应 [${new Date().toISOString()}]`, {
        status: response?.status,
        statusText: response?.statusText,
        hasData: !!response?.data,
        dataType: response?.data ? typeof response.data : 'undefined',
        isSuccess: response?.data?.success
      });

      // 5. 验证响应数据的存在性 - 降低验证严格度
      if (!response || !response.data) {
        console.warn('MetadataExplorer - loadTables: 响应或响应数据为空');
        // 不立即抛出错误，尝试继续处理
      }

      // 分析响应格式并适配不同的格式类型
      let tablesList: TableMetadata[] = [];
      console.log('MetadataExplorer - loadTables: 原始响应数据:', response?.data);

      // 处理可能的各种响应格式
      if (response?.data) {
        const responseData = response.data;

        // 6. 详细记录响应数据，但对格式更宽容
        console.log(`MetadataExplorer - loadTables: 响应数据详情:`, {
          success: responseData?.success,
          message: responseData?.message,
          hasData: !!responseData?.data,
          dataType: responseData?.data ? (Array.isArray(responseData.data) ? 'Array' : typeof responseData.data) : 'undefined',
          dataLength: responseData?.data ? (Array.isArray(responseData.data) ? responseData.data.length : '非数组') : '无数据'
        });

        // 情况1: 标准格式 {success: true, data: [...]}
        if (responseData?.data && Array.isArray(responseData.data)) {
          console.log('MetadataExplorer - loadTables: 检测到标准响应格式，使用data数组');
          tablesList = responseData.data;
        }
        // 情况2: 直接返回数组 [...]
        else if (Array.isArray(responseData)) {
          console.log('MetadataExplorer - loadTables: 检测到数组响应格式，直接使用');
          tablesList = responseData;
        }
        // 情况3: 返回单个对象 {...}
        else if (typeof responseData === 'object' && responseData !== null && !Array.isArray(responseData) && !responseData.data) {
          console.log('MetadataExplorer - loadTables: 检测到单个对象响应格式，转换为数组');
          // 排除明确是错误响应的情况
          if (!responseData.error && !responseData.success === false) {
            tablesList = [responseData as TableMetadata];
          }
        }
        // 情况4: 特殊格式，尝试解析status/statusText等字段
        else if (responseData.status !== undefined) {
          console.log('MetadataExplorer - loadTables: 检测到特殊响应格式，尝试解析');
          // 根据status判断是否成功
          if (responseData.status === 200 || responseData.status === 'OK' || responseData.status === true) {
            if (responseData.result && Array.isArray(responseData.result)) {
              tablesList = responseData.result;
            } else if (responseData.tables && Array.isArray(responseData.tables)) {
              tablesList = responseData.tables;
            }
          }
        }

        // 尝试检测可能包含表信息的其他字段
        if (tablesList.length === 0) {
          console.log('MetadataExplorer - loadTables: 尝试识别可能的表数据字段');

          // 遍历响应对象的顶级字段，寻找可能的数组
          Object.keys(responseData).forEach(key => {
            if (Array.isArray(responseData[key]) && responseData[key].length > 0 && tablesList.length === 0) {
              // 判断该数组是否包含表的典型字段
              const firstItem = responseData[key][0];
              if (firstItem && (firstItem.name || firstItem.tableName)) {
                console.log(`MetadataExplorer - loadTables: 在字段 ${key} 中找到可能的表数据`);
                tablesList = responseData[key];
              }
            }
          });
        }
      }

      // 确保tablesList是有效数组
      if (!Array.isArray(tablesList)) {
        console.warn('MetadataExplorer - loadTables: 转换后的tablesList不是数组，重置为空数组');
        tablesList = [];
      }

      // 9. 处理表数据
      state.tables = tablesList.map((table: TableMetadata): ExtendedTableMetadata => ({
        ...table,
        isUpdating: false,
        lastUpdated: new Date().toISOString(),
        error: undefined
      }));

      console.log(`MetadataExplorer - loadTables: 成功加载 ${state.tables.length} 张表`);
      emit('tablesLoaded', state.tables);

      // 10. 保存到缓存
      if (state.tables.length > 0 && props.dataSourceId && typeof dataSourceStore.saveTablesToCache === 'function') {
        console.log(`MetadataExplorer - loadTables: 将${state.tables.length}张表保存到缓存`);
        dataSourceStore.saveTablesToCache(props.dataSourceId, state.tables, schemaId);
      }
    } catch (apiError) {
      console.error(`MetadataExplorer - loadTables: API请求失败 [${new Date().toISOString()}]`, apiError);

      // 记录更多API错误详情
      console.error('MetadataExplorer - loadTables: API错误详情:', {
        message: apiError instanceof Error ? apiError.message : String(apiError),
        url: apiUrl,
        schemaId,
        dataSourceId: props.dataSourceId
      });

      // 尝试恢复和容错：使用缓存或空数组
      const cachedTables = props.dataSourceId ?
        dataSourceStore.getTablesFromCache(props.dataSourceId, schemaId) : null;

      if (cachedTables && cachedTables.length > 0) {
        console.log(`MetadataExplorer - loadTables: API失败，回退使用缓存数据 (${cachedTables.length}张表)`);
        state.tables = cachedTables.map((table: TableMetadata): ExtendedTableMetadata => ({
          ...table,
          isUpdating: false,
          lastUpdated: new Date().toISOString(),
          error: undefined
        }));
        emit('tablesLoaded', state.tables);
        return; // 使用缓存数据成功，提前返回
      }

      // 无缓存可用，抛出错误继续向上传递
      throw apiError;
    }
  } catch (error) {
    console.error('MetadataExplorer - loadTables: 加载表数据失败:', error);
    console.error('MetadataExplorer - loadTables: 错误详情:', {
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : '无堆栈信息',
      schemaId
    });
    state.error = handleError(error)
  } finally {
    state.loading = false;
    // 表结构加载完成后，将isFirstLoad设置为false
    state.isFirstLoad = false;
  }
};

// 计算属性：过滤后的表格
const filteredTables = computed(() => {
  const searchTerm = state.searchTerm.toLowerCase();
  return state.tables.filter((table: TableMetadata) =>
    table.name.toLowerCase().includes(searchTerm) ||
    table.comment?.toLowerCase().includes(searchTerm)
  );
});

// 展开/折叠表格
const toggleTable = (tableId: string) => {
  const expandedTables = state.expandedTables;
  if (expandedTables.includes(tableId)) {
    expandedTables.splice(expandedTables.indexOf(tableId), 1);
  } else {
    expandedTables.push(tableId);

    // 表格被展开时，自动加载列信息
    const table = state.tables.find((t: TableMetadata) => t.name === tableId);
    if (table && (!table.columns || table.columns.length === 0)) {
      console.log(`表格 ${tableId} 被展开，自动加载列信息`);
      loadColumns(tableId);
    }
  }
};

// 获取表的字段信息
const loadColumns = async (tableName: string) => {
  if (!props.dataSourceId || !tableName) {
    console.error('MetadataExplorer - loadColumns: 缺少必要参数，需要数据源ID和表名');
    return;
  }

  try {
    state.loading = true;
    state.error = null;

    // 1. 从 state.tables 中找到对应表的信息
    const table = state.tables.find((t: TableMetadata) => t.name === tableName);
    if (!table || !table.id) {
      throw new Error(`Table ${tableName} not found or missing ID`);
    }

    // 2. 使用表的 ID 构建正确的 API URL
    const url = getMetadataApiUrl('columns', { tableId: table.id });
    console.log('MetadataExplorer - loadColumns: 从 URL 加载列:', url);

    // 使用安全请求函数发送请求
    const response = await safeRequest(url);

    console.log('MetadataExplorer - loadColumns: 收到响应数据:', response?.data);

    // 分析响应格式并适配不同的响应类型
    let columnsList: ColumnMetadata[] = [];

    if (response?.data) {
      const responseData = response.data;

      // 详细记录响应数据
      console.log(`MetadataExplorer - loadColumns: 响应数据详情:`, {
        success: responseData?.success,
        message: responseData?.message,
        hasData: !!responseData?.data,
        dataType: responseData?.data ? (Array.isArray(responseData.data) ? 'Array' : typeof responseData.data) : 'undefined',
        dataLength: responseData?.data ? (Array.isArray(responseData.data) ? responseData.data.length : '非数组') : '无数据'
      });

      // 情况1: 标准格式 {success: true, data: [...]}
      if (responseData?.data && Array.isArray(responseData.data)) {
        console.log('MetadataExplorer - loadColumns: 检测到标准响应格式，使用data数组');
        columnsList = responseData.data;
      }
      // 情况2: 直接返回数组 [...]
      else if (Array.isArray(responseData)) {
        console.log('MetadataExplorer - loadColumns: 检测到数组响应格式，直接使用');
        columnsList = responseData;
      }
      // 情况3: 返回单个对象 {...}
      else if (typeof responseData === 'object' && responseData !== null && !Array.isArray(responseData) && !responseData.data) {
        console.log('MetadataExplorer - loadColumns: 检测到单个对象响应格式，转换为数组');
        if (!responseData.error && responseData.success !== false) {
          columnsList = [responseData as ColumnMetadata];
        }
      }

      // 尝试检测可能包含列信息的其他字段
      if (columnsList.length === 0) {
        console.log('MetadataExplorer - loadColumns: 尝试识别可能的列数据字段');

        // 遍历响应对象的顶级字段，寻找可能的数组
        Object.keys(responseData).forEach(key => {
          if (Array.isArray(responseData[key]) && responseData[key].length > 0 && columnsList.length === 0) {
            // 判断该数组是否包含列的典型字段
            const firstItem = responseData[key][0];
            if (firstItem && (firstItem.name || firstItem.columnName)) {
              console.log(`MetadataExplorer - loadColumns: 在字段 ${key} 中找到可能的列数据`);
              columnsList = responseData[key];
            }
          }
        });
      }
    }

    // 确保columnsList是有效数组
    if (!Array.isArray(columnsList)) {
      console.warn('MetadataExplorer - loadColumns: 转换后的columnsList不是数组，重置为空数组');
      columnsList = [];
    }

    // 3. 更新表的列信息
    const tableIndex = state.tables.findIndex((t: TableMetadata) => t.name === tableName);
    if (tableIndex !== -1) {
      state.tables[tableIndex] = {
        ...state.tables[tableIndex],
        columns: columnsList.map((col: ColumnMetadata) => ({
          ...col,
          isUpdating: false,
          lastUpdated: new Date().toISOString()
        }))
      };
    }

    console.log(`MetadataExplorer - loadColumns: 成功加载 ${columnsList.length} 列数据`);
    emit('columnsLoaded', tableName);
  } catch (error) {
    console.error('MetadataExplorer - loadColumns: 加载列数据出错:', error);
    let errorMessage = '加载列数据失败';
    if (error instanceof Error) {
      errorMessage = error.message;
    }
    console.error('MetadataExplorer - loadColumns: 错误详情:', {
      message: errorMessage,
      tableName
    });
    state.error = handleError(error);
  } finally {
    state.loading = false;
  }
};

// 组件挂载时初始化
onMounted(() => {
  console.log('MetadataExplorer组件已挂载');

  // 如果有数据源ID，先尝试加载schemas
  if (props.dataSourceId) {
    console.log(`MetadataExplorer - 挂载时已有数据源ID=${props.dataSourceId}，尝试加载数据`);

    // 如果有预先选定的schema，使用它
    if (props.selectedSchema || props.schema) {
      const schemaToUse = props.selectedSchema || props.schema;
      console.log(`MetadataExplorer - 挂载时已有schema=${schemaToUse}，直接加载表结构`);
      loadTables(schemaToUse as string);
    } else {
      // 否则，加载元数据以获取schemas和第一个schema的表
      console.log(`MetadataExplorer - 挂载时无schema，尝试加载元数据`);
      loadMetadata(props.dataSourceId);
    }
  }
})

// 获取元数据
const fetchMetadata = async () => {
  if (!props.dataSourceId) {
    console.error('数据源ID为空，无法获取元数据')
    return
  }

  if (!props.schema) {
    console.error('MetadataExplorer: Schema为空，无法获取表元数据')
    state.error = {
      type: 'error',
      message: '请选择Schema以查看表'
    }
    return
  }

  state.loading = true
  state.error = null

  try {
    console.log(`MetadataExplorer: 开始获取元数据, 数据源ID: ${props.dataSourceId}, Schema: ${props.schema}`)

    const tablesData = await dataSourceService.getTableMetadata(props.dataSourceId, props.schema)

    if (Array.isArray(tablesData) && tablesData.length > 0) {
      console.log(`MetadataExplorer: 成功获取到${tablesData.length}个表的元数据`)
      state.tables = tablesData.map(table => ({
        ...table,
        isUpdating: false,
        lastUpdated: new Date().toISOString()
      }))
      emit('tablesLoaded', state.tables)
    } else if (tablesData && !Array.isArray(tablesData)) {
      // 处理单个表的情况
      state.tables = [{
        ...tablesData,
        isUpdating: false,
        lastUpdated: new Date().toISOString()
      }]
      emit('tablesLoaded', state.tables)
    } else {
      console.warn('MetadataExplorer: 获取到的表元数据为空')
      state.error = {
        type: 'error',
        message: '未获取到表元数据，请检查数据库连接和权限'
      }
    }
  } catch (err) {
    console.error('MetadataExplorer: 获取元数据失败:', err)
    state.error = handleError(err)
  } finally {
    state.loading = false
  }
}

// 获取表名
const getTableName = (table: TableMetadata): string => {
  return table.name;
};

// 获取表显示名称
const getTableDisplayName = (table: TableMetadata): string => {
  return getTableName(table);
}

// 获取列类型显示
const formatColumnType = (column: ColumnMetadata | undefined): string => {
  if (!column?.columnType) {
    return '未知类型';
  }
  return column.columnType.toUpperCase();
};

// 向外暴露加载表结构方法
const canLoadTablesWithoutSchema = (): boolean => {
  // 检查是否单Schema
  return state.noSchema === true || (state.schemas.length === 0 && Boolean(props.dataSourceId));
};

// 向外暴露loadTables方法
const publicLoadTables = (schemaId: string) => {
  if (state.loading) {
    console.log('MetadataExplorer - 正在加载中，跳过重复请求');
    return;
  }
  return loadTables(schemaId);
};

// 向外暴露loadTablesWithoutSchema方法
const publicLoadTablesWithoutSchema = (dataSourceId: string) => {
  if (state.loading) {
    console.log('MetadataExplorer - 正在加载中，跳过重复请求');
    return;
  }
  return loadTablesWithoutSchema(dataSourceId || props.dataSourceId);
};

// 对外暴露方法
defineExpose({
  loadTables: publicLoadTables,
  loadTablesWithoutSchema: publicLoadTablesWithoutSchema,
  canLoadTablesWithoutSchema
});

// 获取列的图标
const getColumnIcon = (column: ColumnMetadata): string => {
  if (column.isPrimaryKey) return 'fas fa-key text-yellow-500';
  if (column.isIndexed) return 'fas fa-link text-blue-500';
  if (column.dataType === 'VARCHAR') return 'fas fa-font text-gray-400';
  if (column.dataType === 'INT' || column.dataType === 'BIGINT') return 'fas fa-hashtag text-gray-400';
  if (column.dataType === 'DATETIME' || column.dataType === 'TIMESTAMP') return 'fas fa-calendar text-gray-400';
  return 'fas fa-circle-dot text-gray-400';
};

// 获取列的标签
const getColumnBadges = (column: ColumnMetadata): { text: string, classes: string }[] => {
  const badges: { text: string, classes: string }[] = [];

  if (column.isPrimaryKey) {
    badges.push({ text: 'PK', classes: 'bg-yellow-100 text-yellow-800' });
  }
  if (column.isIndexed) {
    badges.push({ text: 'IDX', classes: 'bg-blue-100 text-blue-800' });
  }
  if (column.isUnique) {
    badges.push({ text: 'UQ', classes: 'bg-purple-100 text-purple-800' });
  }
  if (!column.isNullable) {
    badges.push({ text: 'REQ', classes: 'bg-red-100 text-red-800' });
  }

  return badges;
};

// 获取列的描述信息
const getColumnDescription = (column: ColumnMetadata): string => {
  const parts: string[] = [];

  if (column.columnType) {
    parts.push(column.columnType);
  }

  if (!column.isNullable) {
    parts.push('NOT NULL');
  }

  if (column.defaultValue) {
    parts.push(`DEFAULT ${column.defaultValue}`);
  }

  if (column.description) {
    parts.push(column.description);
  }

  return parts.join(' ');
};

const refreshTable = async (tableName: string) => {
  const table = state.tables.find((t: ExtendedTableMetadata) => t.name === tableName)
  if (!table) return

  if (table.isUpdating) return

  table.isUpdating = true

  try {
    console.log(`MetadataExplorer - refreshTable: 开始刷新表 ${tableName} 的结构`);
    await loadColumns(tableName)
    table.lastUpdated = new Date().toISOString()

    console.log(`MetadataExplorer - refreshTable: 成功刷新表 ${tableName} 的结构`);
    // 成功刷新的提示
    message.success(`表 ${tableName} 的结构已更新`);
  } catch (error) {
    console.error('MetadataExplorer - refreshTable: 刷新表结构失败:', error)

    // 提供更友好的错误消息
    let errorMessage = '刷新表结构失败';
    if (error instanceof Error) {
      errorMessage = `刷新失败: ${error.message.includes('未知错误') ? '无法连接到服务器' : error.message}`;
    }

    message.error(errorMessage)
  } finally {
    // 获取最新的表对象，避免闭包问题
    const currentTable = state.tables.find((t: ExtendedTableMetadata) => t.name === tableName)
    if (currentTable) {
      currentTable.isUpdating = false
    }
  }
}

function highlightTable(tableId: string | null) {
  state.highlightedTable = tableId;
}

function formatLastUpdated(date: string): string {
  return formatDate(new Date(date));
}

function clearError() {
  state.error = null;
}

// 处理错误的辅助函数
const handleError = (error: unknown): ErrorMessage => {
  // 记录原始错误对象
  console.error('MetadataExplorer - handleError: 原始错误:', error);

  // 分析错误类型，返回用户友好的消息
  let message = '';
  let type: 'error' | 'warning' | 'info' = 'error';

  if (error instanceof Error) {
    // 判断是否是网络错误
    if (error.message.includes('Network Error') || error.message.includes('网络')) {
      message = '网络连接错误，请检查网络后重试';
    }
    // 判断是否是权限错误
    else if (error.message.includes('401') || error.message.includes('403') ||
             error.message.includes('权限') || error.message.includes('认证')) {
      message = '权限验证失败，请重新登录';
      type = 'warning';
    }
    // 判断是否是超时
    else if (error.message.includes('timeout') || error.message.includes('超时')) {
      message = '请求超时，服务器可能繁忙，请稍后重试';
      type = 'warning';
    }
    // 判断是否是数据格式错误
    else if (error.message.includes('解析') || error.message.includes('格式') ||
             error.message.includes('JSON') || error.message.includes('数据')) {
      message = '数据格式错误，请联系管理员';
    }
    // 其他已知错误消息
    else {
      message = error.message;
    }
  } else {
    // 未知错误
    message = String(error) || '未知错误';
  }

  // 确保错误消息不为空
  if (!message || message.trim() === '') {
    message = '发生未知错误，请刷新页面重试';
  }

  return {
    type,
    message
  };
}

// 处理表格选择
const handleTableSelect = (table: TableMetadata) => {
  emit('table-select', table);
};

// 处理列选择
const handleColumnSelect = (column: ColumnMetadata, table: TableMetadata) => {
  emit('column-select', column, table);
};

// 向编辑器插入表名
const insertTableName = (tableName: string) => {
  emit('insert-table', tableName);
};

// 向编辑器插入列名
const insertColumnName = (columnName: string) => {
  emit('insert-column', columnName);
};

// 刷新元数据
const refreshMetadata = async () => {
  if (props.dataSourceId) {
    await fetchMetadata();
  }
};

// 清除搜索
const clearSearch = () => {
  state.searchTerm = '';
};

// 展开所有表格
const expandAllTables = () => {
  if (filteredTables.value.length > 0) {
    state.expandedTables = filteredTables.value.map((table: TableMetadata) => table.name);
  }
};

// 折叠所有表格
const collapseAllTables = () => {
  state.expandedTables = [];
};

// 过滤字段
const filterColumns = (columns: ColumnMetadata[] | undefined, term: string): ColumnMetadata[] => {
  if (!columns || !Array.isArray(columns)) return [];
  if (!term) return columns;

  const lowerTerm = term.toLowerCase();
  return columns.filter(column => {
    if (!column) return false;
    return (
      (column.name && column.name.toLowerCase().includes(lowerTerm)) ||
      (column.columnType && column.columnType.toLowerCase().includes(lowerTerm)) ||
      (column.description && column.description.toLowerCase().includes(lowerTerm))
    );
  });
};

// 格式化列大小
const formatColumnSize = (column: ColumnMetadata | undefined): string => {
  if (!column) return '';

  if (column.characterLength) {
    return `(${column.characterLength})`;
  } else if (column.numericPrecision) {
    return column.numericScale
      ? `(${column.numericPrecision},${column.numericScale})`
      : `(${column.numericPrecision})`;
  }
  return '';
};

// 加载无Schema情况下的表数据
const loadTablesWithoutSchema = async (dataSourceId: string) => {
  if (!dataSourceId) {
    console.error('MetadataExplorer - loadTablesWithoutSchema: 数据源ID为空，无法加载表数据');
    return;
  }

  console.log(`MetadataExplorer - loadTablesWithoutSchema: 开始无Schema模式加载表数据, 数据源ID: ${dataSourceId}`);

  // 首先检查是否已有缓存数据，避免重复请求
  const cachedTables = dataSourceStore.getTablesFromCache(dataSourceId);
  if (cachedTables && cachedTables.length > 0) {
    console.log(`MetadataExplorer - loadTablesWithoutSchema: 使用缓存的表数据 (${cachedTables.length} 张表)`);

    state.tables = cachedTables.map((table: TableMetadata): ExtendedTableMetadata => ({
      ...table,
      isUpdating: false,
      lastUpdated: new Date().toISOString(),
      error: undefined
    }));

    emit('tablesLoaded', state.tables);
    return; // 已有缓存，直接返回
  }

  // 防止重复请求
  if (state.loading) {
    console.log('MetadataExplorer - loadTablesWithoutSchema: 正在加载中，跳过重复请求');
    return;
  }

  try {
    state.loading = true;
    state.error = null;

    // 尝试获取当前数据源信息以确定schema ID
    let schemaToUse = '';
    let dataSource = null;

    try {
      // 1. 首先尝试从dataSourceStore.dataSources中找到数据源
      dataSource = dataSourceStore.dataSources.find((ds: DataSource) => ds.id === dataSourceId);
      console.log(`MetadataExplorer - loadTablesWithoutSchema: 从store中${dataSource ? '找到' : '未找到'}数据源`);

      if (!dataSource) {
        // 如果在列表中没找到，尝试通过API获取数据源详情
        console.log('MetadataExplorer - loadTablesWithoutSchema: 在列表中未找到数据源，尝试通过API获取');
        try {
          dataSource = await dataSourceStore.getDataSourceById(dataSourceId);
          console.log(`MetadataExplorer - loadTablesWithoutSchema: 通过API${dataSource ? '成功' : '未能'}获取数据源详情`);
        } catch (dsError) {
          console.error('MetadataExplorer - loadTablesWithoutSchema: 通过API获取数据源详情失败:', dsError);
        }
      }

      if (dataSource) {
        console.log(`MetadataExplorer - loadTablesWithoutSchema: 数据源详情:`, {
          id: dataSource.id,
          name: dataSource.name,
          database: dataSource.database,
          databaseName: dataSource.databaseName,
          type: dataSource.type
        });

        // 2. 优先使用数据源的database字段
        if (dataSource.database) {
          schemaToUse = dataSource.database;
          console.log(`MetadataExplorer - loadTablesWithoutSchema: 使用数据源的database字段 "${schemaToUse}" 作为schemaId`);
        }
        // 其次尝试使用databaseName字段
        else if (dataSource.databaseName) {
          schemaToUse = dataSource.databaseName;
          console.log(`MetadataExplorer - loadTablesWithoutSchema: 使用数据源的databaseName字段 "${schemaToUse}" 作为schemaId`);
        }
        // 最后尝试使用name字段
        else if (dataSource.name) {
          schemaToUse = dataSource.name.toLowerCase().replace(/\s+/g, '_');
          console.log(`MetadataExplorer - loadTablesWithoutSchema: 使用数据源名称派生的值 "${schemaToUse}" 作为schemaId`);
        }
      }

      // 如果所有尝试都失败，使用一个基于数据源ID的值
      if (!schemaToUse) {
        schemaToUse = `db_${dataSourceId.substring(0, 8)}`;
        console.warn(`MetadataExplorer - loadTablesWithoutSchema: 未能确定数据库名称，使用基于数据源ID的值 "${schemaToUse}"`);
      }
    } catch (error) {
      // 如果获取数据源信息失败，记录错误并使用数据源ID的一部分作为schemaId
      console.error('MetadataExplorer - loadTablesWithoutSchema: 获取数据源详情失败:', error);
      schemaToUse = `db_${dataSourceId.substring(0, 8)}`;
      console.warn(`MetadataExplorer - loadTablesWithoutSchema: 获取数据源详情失败，使用基于ID的值 "${schemaToUse}"`);
    }

    // 3. 构建API请求URL，格式为 /api/metadata/schemas/{schemaId}/tables
    const baseUrl = getApiUrl();
    const apiPrefix = baseUrl.endsWith('/api') ? '' : '/api';
    const url = `${baseUrl}${apiPrefix}/metadata/schemas/${schemaToUse}/tables`;
    console.log(`MetadataExplorer - loadTablesWithoutSchema: 使用URL: ${url}, schemaId: "${schemaToUse}"`);

    // 4. 发送请求并记录详细信息
    const requestTime = new Date().toISOString();
    console.log(`MetadataExplorer - loadTablesWithoutSchema: 开始发送请求 [${requestTime}]`);

    const response = await safeRequest(url);

    console.log(`MetadataExplorer - loadTablesWithoutSchema: 收到响应 [${new Date().toISOString()}]`, {
      status: response?.status,
      statusText: response?.statusText,
      hasData: !!response?.data,
      dataType: response?.data ? typeof response.data : 'undefined',
      isSuccess: response?.data?.success
    });

    // 5. 验证响应 - 使用宽松验证
    if (!response || !response.data) {
      console.warn('MetadataExplorer - loadTablesWithoutSchema: 响应或响应数据为空');
      // 不立即抛出错误，继续尝试处理
    }

    // 分析响应并适配不同格式
    let tablesList: TableMetadata[] = [];
    console.log('MetadataExplorer - loadTablesWithoutSchema: 原始响应数据:', response?.data);

    // 处理各种可能的响应格式
    if (response?.data) {
      const responseData = response.data;

      // 6. 详细记录响应数据
      console.log(`MetadataExplorer - loadTablesWithoutSchema: 响应数据详情:`, {
        success: responseData?.success,
        message: responseData?.message,
        hasData: !!responseData?.data,
        dataType: responseData?.data ? (Array.isArray(responseData.data) ? 'Array' : typeof responseData.data) : 'undefined',
        dataLength: responseData?.data ? (Array.isArray(responseData.data) ? responseData.data.length : '非数组') : '无数据'
      });

      // 情况1: 标准格式 {success: true, data: [...]}
      if (responseData?.data && Array.isArray(responseData.data)) {
        console.log('MetadataExplorer - loadTablesWithoutSchema: 检测到标准响应格式，使用data数组');
        tablesList = responseData.data;
      }
      // 情况2: 直接返回数组 [...]
      else if (Array.isArray(responseData)) {
        console.log('MetadataExplorer - loadTablesWithoutSchema: 检测到数组响应格式，直接使用');
        tablesList = responseData;
      }
      // 情况3: 返回单个对象 {...}
      else if (typeof responseData === 'object' && responseData !== null && !Array.isArray(responseData) && !responseData.data) {
        console.log('MetadataExplorer - loadTablesWithoutSchema: 检测到单个对象响应格式，转换为数组');
        // 排除明确是错误响应的情况
        if (!responseData.error && !responseData.success === false) {
          tablesList = [responseData as TableMetadata];
        }
      }
      // 情况4: 特殊格式，尝试解析status/statusText等字段
      else if (responseData.status !== undefined) {
        console.log('MetadataExplorer - loadTablesWithoutSchema: 检测到特殊响应格式，尝试解析');
        // 根据status判断是否成功
        if (responseData.status === 200 || responseData.status === 'OK' || responseData.status === true) {
          if (responseData.result && Array.isArray(responseData.result)) {
            tablesList = responseData.result;
          } else if (responseData.tables && Array.isArray(responseData.tables)) {
            tablesList = responseData.tables;
          }
        }
      }

      // 尝试检测可能包含表信息的其他字段
      if (tablesList.length === 0) {
        console.log('MetadataExplorer - loadTablesWithoutSchema: 尝试识别可能的表数据字段');

        // 遍历响应对象的顶级字段，寻找可能的数组
        Object.keys(responseData).forEach(key => {
          if (Array.isArray(responseData[key]) && responseData[key].length > 0 && tablesList.length === 0) {
            // 判断该数组是否包含表的典型字段
            const firstItem = responseData[key][0];
            if (firstItem && (firstItem.name || firstItem.tableName)) {
              console.log(`MetadataExplorer - loadTablesWithoutSchema: 在字段 ${key} 中找到可能的表数据`);
              tablesList = responseData[key];
            }
          }
        });
      }
    }

    // 确保tablesList是有效数组
    if (!Array.isArray(tablesList)) {
      console.warn('MetadataExplorer - loadTablesWithoutSchema: 转换后的tablesList不是数组，重置为空数组');
      tablesList = [];
    }

    // 8. 处理和验证表数据
    if (tablesList.length > 0) {
      console.log(`MetadataExplorer - loadTablesWithoutSchema: 成功获取到 ${tablesList.length} 张表`);

      state.tables = tablesList.map((table: TableMetadata): ExtendedTableMetadata => ({
        ...table,
        isUpdating: false,
        lastUpdated: new Date().toISOString(),
        error: undefined
      }));

      // 9. 将表数据缓存到store
      if (typeof dataSourceStore.saveTablesToCache === 'function') {
        console.log(`MetadataExplorer - loadTablesWithoutSchema: 将${state.tables.length}张表保存到缓存`);
        dataSourceStore.saveTablesToCache(dataSourceId, state.tables);
      }

      emit('tablesLoaded', state.tables);
    } else {
      console.warn('MetadataExplorer - loadTablesWithoutSchema: 未获取到表数据');
      state.tables = [];
      state.error = {
        type: 'warning',
        message: '未获取到表数据，数据源可能为空'
      };
    }
  } catch (error) {
    // 统一处理所有错误
    console.error('MetadataExplorer - loadTablesWithoutSchema: 加载表数据出错:', error);
    console.error('MetadataExplorer - loadTablesWithoutSchema: 错误详情:', {
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : '无堆栈信息',
      dataSourceId
    });

    // 尝试从缓存恢复
    const cachedTables = dataSourceStore.getTablesFromCache(dataSourceId);
    if (cachedTables && cachedTables.length > 0) {
      console.log(`MetadataExplorer - loadTablesWithoutSchema: 加载失败，回退使用缓存数据 (${cachedTables.length}张表)`);
      state.tables = cachedTables.map((table: TableMetadata): ExtendedTableMetadata => ({
        ...table,
        isUpdating: false,
        lastUpdated: new Date().toISOString(),
        error: undefined
      }));
      emit('tablesLoaded', state.tables);
      return; // 使用缓存成功，提前返回
    }

    state.error = handleError(error);
  } finally {
    state.loading = false;
    // 无Schema表结构加载完成后，将isFirstLoad设置为false
    state.isFirstLoad = false;
  }
};
</script>

<template>
  <div class="metadata-explorer h-full flex flex-col">
    <!-- 未加载表结构时的显示 -->
    <div v-if="!state.tables.length && props.dataSourceId" class="flex-1 flex flex-col items-center justify-center p-4">
      <div class="text-center space-y-4">
        <i class="fas fa-database text-gray-400 text-4xl mb-2"></i>
        <p class="text-gray-500" v-if="state.loading">正在加载表结构...</p>
        <p class="text-gray-500" v-else-if="state.noSchema && !state.error">此数据源未配置Schema，正在直接加载所有表...</p>
        <p class="text-gray-500" v-else-if="state.error && !state.isFirstLoad">{{ state.error.message }}</p>
        <p class="text-gray-500" v-else-if="state.isFirstLoad">正在准备加载表结构...</p>
        <p class="text-gray-500" v-else>正在准备加载表结构...</p>
        <div v-if="state.loading" class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-500"></div>
        <button
          v-else-if="state.error"
          @click="loadMetadata(props.dataSourceId)"
          class="px-6 py-2 bg-indigo-500 text-white rounded-md hover:bg-indigo-600 flex items-center justify-center space-x-2 transition-colors"
        >
          <i class="fas fa-sync-alt mr-2"></i>
          <span>重试加载表结构</span>
        </button>
      </div>
    </div>

    <!-- 已加载表结构时的显示 -->
    <template v-else>
      <!-- 搜索框 -->
      <div class="p-3 border-b border-gray-200">
        <!-- 无Schema提示信息 -->
        <div v-if="state.noSchema" class="mb-3 p-2 bg-blue-50 text-blue-700 rounded-md flex items-center text-sm">
          <i class="fas fa-info-circle mr-2"></i>
          <span>此数据源未配置Schema，直接显示所有表</span>
        </div>

        <div class="relative">
          <input
            v-model="state.searchTerm"
            type="text"
            placeholder="搜索表或字段..."
            class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
          />
          <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
        </div>
      </div>

      <!-- 表格列表 -->
      <div class="flex-1 overflow-y-auto">
        <div v-if="state.loading" class="p-4 text-center text-gray-500">
          <div class="animate-pulse flex flex-col items-center space-y-4">
            <i class="fas fa-database text-4xl"></i>
            <div class="h-2 bg-gray-200 rounded w-3/4"></div>
            <div class="h-2 bg-gray-200 rounded w-1/2"></div>
          </div>
          <p class="mt-4">正在加载表信息...</p>
        </div>

        <div v-else-if="state.error" class="p-4 text-center text-red-500">
          <i class="fas fa-exclamation-circle text-4xl"></i>
          <p class="mt-2">{{ state.error.message }}</p>
          <button
            @click="state.noSchema ? loadTablesWithoutSchema(props.dataSourceId) : loadTables(props.selectedSchema)"
            class="mt-4 px-4 py-2 bg-red-100 text-red-700 rounded-md hover:bg-red-200"
          >
            <i class="fas fa-redo mr-2"></i>加载表结构
          </button>
        </div>

        <div v-else-if="!filteredTables.length && state.searchTerm" class="p-4 text-center text-gray-500">
          <i class="fas fa-search text-4xl mb-2"></i>
          <p>未找到匹配的表或字段</p>
        </div>

        <div v-else-if="filteredTables.length" class="divide-y divide-gray-200">
          <div v-for="table in filteredTables" :key="table.name" class="table-item" :class="{'animate-highlight': state.highlightedTable === table.name}">
            <!-- 表头部 -->
            <div
              @click="toggleTable(table.name)"
              class="p-3 hover:bg-gray-50 cursor-pointer flex items-center justify-between group transition-colors duration-150"
            >
              <div class="flex items-center space-x-2 flex-1 min-w-0">
                <div class="relative">
                  <i class="fas" :class="[
                    table.type === 'VIEW' ? 'fa-table-columns text-blue-500' :
                    table.type === 'MATERIALIZED_VIEW' ? 'fa-database text-purple-500' :
                    'fa-table text-gray-500'
                  ]"></i>
                  <div v-if="table.isUpdating" class="absolute -top-1 -right-1 w-2 h-2 bg-green-500 rounded-full animate-ping"></div>
                </div>
                <div class="flex-1 min-w-0">
                  <div class="flex items-center">
                    <span class="font-medium text-gray-900 truncate">{{ table.name }}</span>
                    <span v-if="table.type !== 'TABLE'"
                          class="ml-2 px-2 py-0.5 text-xs rounded-full transform transition-all duration-150 hover:scale-105"
                          :class="[
                    table.type === 'VIEW' ? 'bg-blue-100 text-blue-800' :
                    table.type === 'MATERIALIZED_VIEW' ? 'bg-purple-100 text-purple-800' :
                    'bg-gray-100 text-gray-800'
                  ]">
                      {{ table.type === 'MATERIALIZED_VIEW' ? 'MView' : table.type }}
                    </span>
                  </div>
                  <p v-if="table.comment" class="text-sm text-gray-500 truncate">
                    {{ table.comment }}
                  </p>
                  <div class="flex items-center space-x-2 text-xs text-gray-400 mt-1">
                    <span>
                      <i class="fas fa-columns mr-1"></i>
                      {{ table.columnsCount || 0 }} 列
                    </span>
                    <span v-if="table.lastUpdated" title="最后更新时间">
                      <i class="fas fa-clock mr-1"></i>
                      {{ formatLastUpdated(table.lastUpdated) }}
                    </span>
                  </div>
                </div>
              </div>
              <div class="flex items-center space-x-3">
                <div class="opacity-0 group-hover:opacity-100 transition-opacity">
                  <button
                    @click.stop="refreshTable(table.name)"
                    class="p-1 text-gray-400 hover:text-indigo-600 transition-colors"
                    title="刷新表结构"
                  >
                    <i class="fas fa-sync-alt"></i>
                  </button>
                </div>
                <i class="fas fa-chevron-right text-gray-400 transform transition-transform duration-200"
                  :class="{ 'rotate-90': state.expandedTables.includes(table.name) }"></i>
              </div>
            </div>

            <!-- 表字段列表 -->
            <div v-if="state.expandedTables.includes(table.name)" class="bg-gray-50 border-t border-gray-100">
              <div v-if="!table.columns || table.columns.length === 0" class="p-3 text-center text-gray-500 text-sm">
                <i class="fas fa-spinner fa-spin mr-2"></i>
                加载字段信息...
              </div>
              <div v-else class="divide-y divide-gray-100">
                <div
                  v-for="column in filterColumns(table.columns, state.searchTerm)"
                  :key="column?.id || Math.random().toString(36)"
                  class="p-2 pl-8 hover:bg-gray-100 flex items-center justify-between group"
                >
                  <div class="flex items-center space-x-2 flex-1 min-w-0">
                    <i :class="getColumnIcon(column)"></i>

                    <div class="flex-1 min-w-0">
                      <div class="flex items-center">
                        <span class="font-medium text-gray-900">{{ column?.name || '未知字段' }}</span>
                        <span
                          v-for="badge in getColumnBadges(column)"
                          :key="badge.text"
                          class="ml-2 px-1.5 py-0.5 text-xs rounded"
                          :class="badge.classes"
                        >
                          {{ badge.text }}
                        </span>
                      </div>
                      <div class="text-sm text-gray-500 flex items-center space-x-2">
                        <span>{{ formatColumnType(column) }}</span>
                        <span v-if="column && !column.isNullable" class="text-red-500">*</span>
                        <span v-if="column?.description" class="text-gray-400 truncate">{{ column.description }}</span>
                      </div>
                    </div>
                  </div>

                  <!-- 字段操作按钮 -->
                  <div class="opacity-0 group-hover:opacity-100 transition-opacity flex items-center space-x-2">
                    <button
                      v-if="column?.name"
                      @click="insertColumnName(`${table.name}.${column.name}`)"
                      class="p-1 text-gray-500 hover:text-indigo-600"
                      title="插入字段"
                    >
                      <i class="fas fa-plus"></i>
                    </button>
                    <button
                      v-if="column?.description"
                      class="p-1 text-gray-500 hover:text-indigo-600"
                      :title="column.description"
                    >
                      <i class="fas fa-info-circle"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<style scoped>
.metadata-explorer {
  font-size: 14px;
}

.table-item {
  position: relative;
}

@keyframes highlight {
  0% {
    background-color: rgba(99, 102, 241, 0.1);
  }
  100% {
    background-color: transparent;
  }
}

.animate-highlight {
  animation: highlight 2s ease-out;
}
</style>

<!--
单元测试：Schema功能测试
1. 测试用例1：数据源带有schema参数
   - 选择一个数据源，确保schema参数正确传递
   - 验证表列表是否按schema正确过滤
   - 验证点击表时，字段加载是否带有schema参数

2. 测试用例2：表的字段加载
   - 选择一个schema，展开某个表
   - 验证是否成功加载字段信息
   - 确认字段类型显示正确

3. 测试用例3：搜索功能
   - 在搜索框输入关键字
   - 验证过滤后的表是否来自当前选中的schema
   - 验证是否能搜索到表中的字段

4. 测试用例4：数据源切换
   - 从一个schema切换到另一个schema
   - 验证表列表是否正确更新
   - 验证之前展开的表是否正确折叠
-->
