<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { watch } from '@vue/runtime-core'
import { useDataSourceStore } from '@/stores/datasource'
import { getMetadataApiUrl } from '@/services/apiUtils'
import { http } from '@/utils/http'
import type { DataSource } from '@/types/datasource'
import instance from "@/utils/axios";

interface Props {
  dataSourceId?: string
  selectedSchema?: string
}

const props = defineProps<Props>()

const emit = defineEmits<{
  (e: 'schema-selected', schemaId: string): void
  (e: 'loading-change', loading: boolean): void
  (e: 'error', error: { type: string, message: string }): void
  (e: 'no-schema'): void
}>()

// 数据源存储
const dataSourceStore = useDataSourceStore()

// 组件状态
const loading = ref(false)
const error = ref<{ type: string; message: string } | null>(null)
const schemas = ref<any[]>([])
const selectedSchemaId = ref<string | undefined>(props.selectedSchema)
const noSchemaAvailable = ref(false)

// 监听外部selectedSchema变化
watch(() => props.selectedSchema, (newSchema: string | undefined) => {
  if (newSchema && newSchema !== selectedSchemaId.value) {
    selectedSchemaId.value = newSchema
  }
}, { immediate: true })

// 监听内部selectedSchemaId变化
watch(() => selectedSchemaId.value, (newSchemaId: string | undefined) => {
  if (newSchemaId) {
    emit('schema-selected', newSchemaId)
  }
})

// 加载schemas
const loadSchemas = async (dataSourceId: string) => {
  if (!dataSourceId || loading.value) return

  // 开始加载schemas
  loading.value = true
  emit('loading-change', true)
  error.value = null

  try {
    // 首先检查store中是否已有schemas数据
    const cachedSchemas = dataSourceStore.metadataState.schemas.get(dataSourceId)


    // 如果已有schema数据，则使用现有数据
    if (cachedSchemas && cachedSchemas.length >= 0) {
      console.log('【调试】从缓存获取的原始schemas:', JSON.stringify(cachedSchemas, null, 2));
      schemas.value = cachedSchemas

      // 无schema情况的处理
      if (cachedSchemas.length === 0) {

        noSchemaAvailable.value = true
        emit('no-schema')
      }

      // 如果只有一个schema，自动选中
      if (cachedSchemas.length === 1) {
        selectedSchemaId.value = cachedSchemas[0].value
        console.log('【调试】自动选中的schema:', cachedSchemas[0]);
        console.log('【调试】自动选中的schemaId:', selectedSchemaId.value);
      }

      // 添加日志，记录加载的schemas详情
      console.log('从缓存加载的schemas详细信息:');
      cachedSchemas.forEach((schema, index) => {
        console.log(`  schema[${index}]:`, schema);
        console.log(`    id=${schema.id || '无'}, value=${schema.value || '无'}, name=${schema.name || '无'}`);
        // 检查是否有__proto__上的id属性
        const protoId = Object.getPrototypeOf(schema).id;
        if (protoId) {
          console.log(`    __proto__.id=${protoId}`);
        }
      });

      return
    }

    // 如果store中没有schemas数据，使用store中API获取


    // 优先使用store的方法获取schemas
    if (typeof dataSourceStore.getSchemas === 'function') {

      await dataSourceStore.getSchemas(dataSourceId)

      // 从store获取获取到的schemas
      const newSchemas = dataSourceStore.metadataState.schemas.get(dataSourceId) || []
      console.log('【调试】通过API获取的原始schemas:', JSON.stringify(newSchemas, null, 2));

      schemas.value = newSchemas

      // 处理无schema情况
      if (newSchemas.length === 0) {

        noSchemaAvailable.value = true
        emit('no-schema')
      }

      // 如果只有一个schema，自动选中
      if (newSchemas.length === 1) {
        selectedSchemaId.value = newSchemas[0].value
      }

      // 添加日志，记录通过API加载的schemas详细信息
      console.log('通过API加载的schemas详细信息:');
      newSchemas.forEach((schema, index) => {
        console.log(`  schema[${index}]:`, schema);
        console.log(`    id=${schema.id || '无'}, value=${schema.value || '无'}, name=${schema.name || '无'}`);
        // 检查是否有__proto__上的id属性
        const protoId = Object.getPrototypeOf(schema).id;
        if (protoId) {
          console.log(`    __proto__.id=${protoId}`);
        }
      });

    } else {
      // 回退到直接API调用（不推荐）
      console.log('【调试】找不到store.getSchemas方法，回退到直接API调用');
      const schemasUrl = getMetadataApiUrl('schemas', { dataSourceId }) + `?_t=${Date.now()}`
      console.log('【调试】直接请求schemas的URL:', schemasUrl);

      try {
        console.log('【调试】开始发送instance.get请求获取schemas');
        // 使用instance.get替代fetch
        const schemasResponse = await instance.get(schemasUrl);
        // 处理响应数据
        const schemasData = schemasResponse.data;
        console.log('【调试】获取到的schemas响应:', schemasData);

        if (!schemasData.success || !Array.isArray(schemasData.data)) {
          throw new Error('获取数据库schemas失败')
        }

        // 保存获取到的schemas
        schemas.value = schemasData.data
        console.log('【调试】保存schemas数据到组件状态:', schemas.value);

        // 处理无schema的情况
        if (schemasData.data.length === 0) {
          console.log('【调试】当前数据源没有schemas');
          noSchemaAvailable.value = true
          emit('no-schema')
        }

        // 如果只有一个schema，自动选中
        if (schemasData.data.length === 1) {
          // 需要处理API返回的原始数据结构
          const schema = schemasData.data[0];
          // 从日志来看，API返回的schema可能是字符串或者带有value字段的对象
          selectedSchemaId.value = typeof schema === 'string' ? schema : schema.value
          console.log('【调试】自动选中唯一的schema:', selectedSchemaId.value);
        }
      } catch (err) {
        console.error('【调试】直接获取schemas失败:', err);
        throw err
      }
    }
  } catch (err) {

    const errorMsg = err instanceof Error ? err.message : String(err)
    error.value = {
      type: 'error',
      message: errorMsg
    }
    emit('error', { type: 'error', message: errorMsg })
  } finally {
    loading.value = false
    emit('loading-change', false)

  }
}

// 为父组件提供加载指定数据源的schemas的方法
const loadSchemasForDataSource = async (dataSourceId: string) => {
  if (!dataSourceId) return

  // 重置状态
  selectedSchemaId.value = undefined
  schemas.value = []
  error.value = null
  noSchemaAvailable.value = false

  // 加载新数据源的schemas
  await loadSchemas(dataSourceId)
}

// 刷新schemas
const refreshSchemas = async () => {
  if (props.dataSourceId) {
    await loadSchemas(props.dataSourceId)
  }
}

// 清除错误
const clearError = () => {
  error.value = null
}

// 重置选中的schema
const resetSchemaSelection = () => {
  selectedSchemaId.value = undefined
}

// 格式化schema名称，用于显示
const formatSchemaName = (schema: any): string => {
  if (!schema) return '未知Schema';

  // 添加调试日志，查看实际接收到的数据
  console.log('formatSchemaName实际接收到的schema对象(简化版):', schema);

  // 根据日志，可能是字符串或对象
  if (typeof schema === 'string') {
    return schema;
  }

  // 显示名称和ID
  const schemaName = schema.name || '未命名Schema';
  const schemaId = schema.id || schema.value || '';

  // 如果ID过长，截断显示
  const displayId = schemaId.length > 15 ? schemaId.substring(0, 12) + '...' : schemaId;

  // 返回格式: 名称 [ID]
  return `${schemaName} [${displayId}]`;
}

// 添加日志函数，跟踪选中的schema
const logSelectedSchema = () => {
  if (!selectedSchemaId.value) return;

  // 找到选中的schema对象
  const selectedSchema = schemas.value.find(s => s.value === selectedSchemaId.value);
  console.log('选中的schema:', JSON.stringify(selectedSchema, null, 2));

  // 检查所有schemas的ID信息
  console.log('所有schemas ID信息:');
  schemas.value.forEach((schema, index) => {
    console.log(`  schema[${index}]: id=${schema.id || '无'}, value=${schema.value || '无'}, name=${schema.name || '无'}`);
  });
}

// 对外暴露方法
defineExpose({
  loadSchemas,
  loadSchemasForDataSource,
  refreshSchemas,
  resetSchemaSelection,
  clearError,
  updateSelection: (schemaId: string) => {
    console.log(`SchemaSelector - 收到更新选择的请求, schemaId=${schemaId}`);
    if (!schemaId) return;
    
    // 检查此schema是否存在于当前schemas列表中
    const exists = schemas.value.some((schema: any) => 
      schema.id === schemaId || schema.value === schemaId || schema.name === schemaId
    );
    
    if (exists) {
      console.log(`SchemaSelector - schema ${schemaId} 存在，更新选择`);
      selectedSchemaId.value = schemaId;
      // 不发出事件，避免循环
    } else {
      console.log(`SchemaSelector - schema ${schemaId} 不存在于当前列表中，忽略请求`);
    }
  }
})
</script>

<template>
  <div class="schema-selector">
    <!-- 加载状态 -->
    <div v-if="loading" class="w-full py-2 px-4 flex items-center justify-center">
      <div class="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-indigo-500 mr-2"></div>
      <span class="text-sm text-gray-500">加载中...</span>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="w-full py-2 px-4 bg-red-50 text-red-700 rounded-md flex items-center justify-between text-sm">
      <div class="flex items-center">
        <i class="fas fa-exclamation-circle mr-2"></i>
        <span>{{ error.message }}</span>
      </div>
      <button
        @click="refreshSchemas"
        class="ml-2 p-1 hover:bg-red-100 rounded-full transition-colors"
        title="重试"
      >
        <i class="fas fa-sync-alt"></i>
      </button>
    </div>

    <!-- 无Schema情况 -->
    <div v-else-if="noSchemaAvailable" class="w-full py-2 px-4 bg-blue-50 text-blue-700 rounded-md flex items-center text-sm">
      <i class="fas fa-info-circle mr-2"></i>
      <span>此数据源未配置Schema，将直接显示所有表</span>
    </div>

    <!-- Schema选择器 -->
    <div v-else class="w-full relative">
      <select
        v-if="schemas.length > 0"
        v-model="selectedSchemaId"
        class="w-full pl-3 pr-10 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 appearance-none"
        @change="logSelectedSchema"
      >
        <option disabled value="">请选择Schema</option>
        <option
          v-for="(schema, index) in schemas"
          :key="schema.value"
          :value="schema.value"
          @mouseover="() => console.log(`schema[${index}]详情:`, schema)"
        >
          {{ formatSchemaName(schema) }}
        </option>
      </select>
      <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
        <i class="fas fa-chevron-down text-gray-400"></i>
      </div>

      <!-- 无可用Schema情况 -->
      <div v-if="schemas.length === 0" class="w-full py-2 px-4 bg-gray-50 text-gray-500 rounded-md flex items-center text-sm">
        <i class="fas fa-database mr-2"></i>
        <span>没有可用的Schema</span>
      </div>
    </div>
  </div>
</template>

<style scoped>
.schema-selector {
  width: 100%;
}
</style>
