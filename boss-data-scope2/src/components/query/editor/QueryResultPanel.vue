<script setup lang="ts">
import {computed, onUnmounted, ref, watch} from 'vue'
import type {QueryResult} from '@/types/query'
import QueryResults from '@/components/query/QueryResults.vue'

// 定义组件属性
const props = defineProps({
  // 查询结果
  results: {
    type: Object as () => QueryResult | null,
    default: null
  },
  // 是否正在执行查询
  isExecuting: {
    type: Boolean,
    default: false
  },
  // 查询错误
  queryError: {
    type: String,
    default: null
  },
  // 执行时间（毫秒）
  executionTime: {
    type: Number,
    default: 0
  },
  // 是否显示结果面板
  isVisible: {
    type: Boolean,
    default: false
  },
  // 查询ID
  queryId: {
    type: String,
    default: null
  }
})

// 定义事件
const emit = defineEmits<{
  (e: 'close'): void
  (e: 'resize', height: number): void
  (e: 'export', format: string): void
}>()

// 结果面板高度控制
const resizing = ref(false)
const startY = ref(0)
const startHeight = ref(0)
const panelHeight = ref(400) // 默认高度

// 计算属性：格式化的执行时间
const formattedExecutionTime = computed(() => {
  if (props.executionTime < 1000) {
    return `${props.executionTime}ms`
  } else {
    return `${(props.executionTime / 1000).toFixed(2)}s`
  }
})

// 监视结果面板可见性变化
watch(() => props.isVisible, (newValue) => {
  if (newValue) {
    panelHeight.value = 400 // 重置为默认高度
  }
})

// 处理调整大小开始
const handleResizeStart = (event: MouseEvent) => {
  resizing.value = true
  startY.value = event.clientY
  startHeight.value = panelHeight.value
  document.body.style.cursor = 'ns-resize'

  // 添加临时事件监听器
  document.addEventListener('mousemove', handleResizeMove)
  document.addEventListener('mouseup', handleResizeEnd)
}

// 处理调整大小移动
const handleResizeMove = (event: MouseEvent) => {
  if (!resizing.value) return

  const diff = startY.value - event.clientY
  const newHeight = startHeight.value + diff

  // 限制最小和最大高度
  if (newHeight >= 200 && newHeight <= window.innerHeight * 0.8) {
    panelHeight.value = newHeight
    emit('resize', newHeight)
  }
}

// 处理调整大小结束
const handleResizeEnd = () => {
  resizing.value = false
  document.body.style.cursor = ''

  // 移除临时事件监听器
  document.removeEventListener('mousemove', handleResizeMove)
  document.removeEventListener('mouseup', handleResizeEnd)
}

// 关闭结果面板
const closeResultPanel = () => {
  emit('close')
}

// 处理导出
const handleExport = (format: string) => {
  emit('export', format)
}

// 组件卸载时移除监听器
onUnmounted(() => {
  document.removeEventListener('mousemove', handleResizeMove)
  document.removeEventListener('mouseup', handleResizeEnd)
})
</script>

<template>
  <div v-if="isVisible" class="bg-white border-t border-gray-200 shadow-inner" :style="{ height: `${panelHeight}px` }">
    <!-- 调整大小的拖动条 -->
    <div
        class="h-1 w-full bg-gray-200 hover:bg-indigo-300 cursor-ns-resize transition-colors"
      @mousedown="handleResizeStart"
    ></div>

    <!-- 结果面板头部 -->
    <div class="px-4 py-2 flex justify-between items-center border-b border-gray-200">
      <div class="flex items-center">
        <h3 class="text-md font-medium text-gray-900">查询结果</h3>
      </div>
    </div>

    <!-- 结果内容 -->
    <div class="h-[calc(100%-45px)] overflow-auto">
      <!-- 加载中状态 -->
      <div v-if="isExecuting" class="h-full flex items-center justify-center">
        <div class="text-center">
          <div class="inline-block animate-spin rounded-full h-8 w-8 border-4 border-indigo-200 border-t-indigo-500"></div>
          <p class="mt-2 text-gray-600">执行查询中...</p>
        </div>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="queryError" class="h-full flex items-center justify-center p-4 bg-red-50">
        <div class="max-w-lg text-center">
          <div class="text-red-500 text-5xl mb-3">
            <i class="fas fa-exclamation-circle"></i>
          </div>
          <h3 class="text-lg font-medium text-red-800 mb-2">查询执行失败</h3>
          <pre class="text-sm text-red-700 bg-red-100 p-3 rounded overflow-auto max-h-40">{{ queryError }}</pre>
        </div>
      </div>

      <!-- 无结果状态 -->
      <div v-else-if="!results" class="h-full flex items-center justify-center">
        <div class="text-center text-gray-500">
          <div class="text-5xl mb-3">
            <i class="fas fa-database"></i>
          </div>
          <p>执行查询，查看结果</p>
        </div>
      </div>

      <!-- 查询结果 -->
      <QueryResults
        v-else
        :results="results"
        :is-loading="isExecuting"
        :error="queryError"
        :query-id="$props.queryId || ''"
      />
    </div>
  </div>
</template>

<style scoped>
/* 导出下拉按钮样式 */
.dropdown-button {
  padding-right: 24px !important;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.25rem center;
  background-size: 1em 1em;
}
</style>
