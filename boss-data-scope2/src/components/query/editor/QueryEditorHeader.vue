<template>
  <div class="page-header mb-6">
    <!-- 错误提示区域 -->
    <div v-if="errorMessage" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mb-4 flex items-center justify-between">
      <div class="flex items-center">
        <i class="fas fa-exclamation-circle mr-2"></i>
        <span>{{ errorMessage }}</span>
      </div>
      <button @click="clearError" class="text-red-700 hover:text-red-900">
        <i class="fas fa-times"></i>
      </button>
    </div>
    <div class="flex justify-between items-center">
      <h1 class="text-2xl font-bold text-gray-900">
        {{ isEditing ? '编辑查询' : '新增查询' }}
      </h1>
      <!-- 操作按钮组 -->
      <div class="flex items-center space-x-2 ml-auto">
        <button
          @click="returnToList"
          class="px-4 py-2 rounded-md flex items-center text-gray-600 border border-gray-300 hover:bg-gray-100 transition-colors"
        >
          <i class="fas fa-arrow-left mr-2"></i>
          返回列表
        </button>

        <button
          class="px-4 py-2 rounded-md text-gray-600 border border-gray-300 hover:bg-gray-100 transition-colors"
          @click="saveQuery"
        >
          <i class="fas fa-save mr-2"></i>
          保存
        </button>

        <button
          class="px-4 py-2 rounded-md bg-green-600 text-white hover:bg-green-700 transition-colors"
          @click="publishQuery"
        >
          <i class="fas fa-upload mr-2"></i>
          发布新版本
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

/**
 * 组件属性
 */
interface Props {
  /** 查询ID */
  queryId?: string | null;
  /** 错误消息 */
  errorMessage?: string | null;
}

/**
 * 组件事件
 */
interface Emits {
  /** 清除错误消息 */
  (e: 'clear-error'): void;
  /** 返回列表 */
  (e: 'return-to-list'): void;
  /** 保存查询 */
  (e: 'save-query'): void;
  /** 发布查询 */
  (e: 'publish-query'): void;
}

const props = withDefaults(defineProps<Props>(), {
  queryId: null,
  errorMessage: null
});

const emit = defineEmits<Emits>();

/**
 * 判断当前是编辑还是新增
 */
const isEditing = computed(() => !!props.queryId);

/**
 * 清除错误消息
 */
const clearError = () => {
  emit('clear-error');
};

/**
 * 返回列表
 */
const returnToList = () => {
  emit('return-to-list');
};

/**
 * 保存查询
 */
const saveQuery = () => {
  emit('save-query');
};

/**
 * 发布查询
 */
const publishQuery = () => {
  emit('publish-query');
};
</script>
