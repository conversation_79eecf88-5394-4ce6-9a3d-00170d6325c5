<template>
  <div class="bg-white shadow rounded-lg mb-6">
    <div class="p-3">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
        <!-- 查询名称 -->
        <div>
          <label for="queryName" class="block text-sm font-medium text-gray-700 mb-1">
            查询名称 <span class="text-red-500">*</span>
          </label>
          <input 
            id="queryName"
            :value="modelValue"
            @input="handleInput"
            type="text"
            class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm force-border py-2 px-3"
            placeholder="请输入查询名称"
          />
        </div>
        
        <!-- 版本信息 - 使用下拉选择框 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">
            版本信息
          </label>
          <div class="relative">
            <select 
              v-model="selectedVersionModel"
              disabled
              class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-100 text-gray-700 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm pr-20 cursor-not-allowed force-border"
            >
              <option 
                v-for="version in availableVersions" 
                :key="version" 
                :value="version"
              >
                {{ version }}
              </option>
            </select>
            <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-8 text-gray-500">
              <i class="fas fa-code-branch mr-1"></i>
            </div>
            <!-- 新增版本按钮已移除 -->
            
            <!-- 添加静态版本显示，解决初始版本信息不显示问题 -->
            <div v-if="availableVersions.length === 0 || (availableVersions.length === 1 && !availableVersions[0])" class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-700">
              V1
            </div>
          </div>
        </div>
        
        <!-- 版本状态 - 直接显示内容 -->
        <div v-if="queryId" class="flex items-center h-8">
          <span 
            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
            :class="{
              'bg-blue-100 text-blue-800': versionStatus === 'DRAFT',
              'bg-green-100 text-green-800': versionStatus === 'PUBLISHED' && isActiveVersion,
              'bg-indigo-100 text-indigo-800': versionStatus === 'PUBLISHED' && !isActiveVersion,
              'bg-gray-100 text-gray-800': versionStatus === 'DEPRECATED'
            }"
          >
            <span 
              class="w-2 h-2 rounded-full mr-1.5"
              :class="{
                'bg-blue-400': versionStatus === 'DRAFT',
                'bg-green-400': versionStatus === 'PUBLISHED' && isActiveVersion,
                'bg-indigo-400': versionStatus === 'PUBLISHED' && !isActiveVersion,
                'bg-gray-400': versionStatus === 'DEPRECATED'
              }"
            ></span>
            {{ versionStatusText }}
          </span>
          <span v-if="versionStatus === 'PUBLISHED'" class="ml-2 text-xs text-gray-500">
            发布于 {{ formatDate(publishedAt) }}
          </span>
          <span v-if="versionStatus === 'DRAFT'" class="ml-2 text-xs text-gray-500">
            最后编辑于 {{ formatDate(lastEditedAt) }}
          </span>
          <span v-if="versionStatus === 'DEPRECATED'" class="ml-2 text-xs text-gray-500">
            废弃于 {{ formatDate(deprecatedAt) }}
          </span>
        </div>
        <!-- 新增查询时的版本状态显示 -->
        <div v-else class="flex items-center h-8">
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            <span class="w-2 h-2 rounded-full mr-1.5 bg-blue-400"></span>
            草稿
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

/**
 * 组件属性
 */
interface Props {
  /** 查询名称 */
  modelValue: string;
  /** 查询ID */
  queryId?: string | null;
  /** 版本状态 */
  versionStatus: 'DRAFT' | 'PUBLISHED' | 'DEPRECATED';
  /** 是否为当前活动版本 */
  isActiveVersion: boolean;
  /** 可用版本列表 */
  availableVersions: string[];
  /** 当前选中的版本 */
  selectedVersion: string;
  /** 发布时间 */
  publishedAt?: string | null;
  /** 最后编辑时间 */
  lastEditedAt?: string | null;
  /** 废弃时间 */
  deprecatedAt?: string | null;
}

/**
 * 组件事件
 */
interface Emits {
  /** 更新查询名称 */
  (e: 'update:modelValue', value: string): void;
  /** 更新选中的版本 */
  (e: 'update:selectedVersion', value: string): void;
  /** 创建新版本 */
  (e: 'create-new-version'): void;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  queryId: null,
  versionStatus: 'DRAFT',
  isActiveVersion: false,
  availableVersions: () => [],
  selectedVersion: 'V1',
  publishedAt: null,
  lastEditedAt: null,
  deprecatedAt: null
});

const emit = defineEmits<Emits>();

/**
 * 计算属性：版本状态文本
 */
const versionStatusText = computed(() => {
  if (props.versionStatus === 'DRAFT') {
    return '草稿';
  } else if (props.versionStatus === 'PUBLISHED') {
    return props.isActiveVersion ? '当前版本' : '已发布';
  } else if (props.versionStatus === 'DEPRECATED') {
    return '已废弃';
  }
  return props.versionStatus; // 默认返回原始状态值
});

/**
 * 计算属性：当前选中的版本 (v-model)
 */
const selectedVersionModel = computed({
  get: () => props.selectedVersion,
  set: (value) => emit('update:selectedVersion', value)
});

/**
 * 创建新版本
 */
const createNewVersion = () => {
  emit('create-new-version');
};

/**
 * 格式化日期
 */
const formatDate = (dateString: string | number | Date | undefined | null) => {
  if (!dateString) return '未知时间';
  const date = new Date(dateString);
  return date.toLocaleDateString();
};

/**
 * 处理输入事件
 */
const handleInput = (e: Event) => {
  const target = e.target as HTMLInputElement;
  emit('update:modelValue', target.value);
};
</script>

<style scoped>
.force-border {
  border: 1px solid #d1d5db !important;
}
</style>