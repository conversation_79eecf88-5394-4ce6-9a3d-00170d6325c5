const handleQuerySelected = async (queryId: string) => {
  console.log('QuerySelectorEnhanced: 开始处理查询选择:', queryId);
  if (!queryId) {
    console.log('QuerySelectorEnhanced: 查询ID为空，重置状态');
    emit('update:queryId', '');
    emit('update:queryVersion', '');
    emit('update:queryName', '');
    emit('update:queryDescription', '');
    emit('update:queryType', '');
    emit('update:querySql', '');
    emit('update:queryParams', []);
    return;
  }

  try {
    console.log('QuerySelectorEnhanced: 开始加载查询详情:', queryId);
    const response = await instance.get(`/api/queries/${queryId}`);
    console.log('QuerySelectorEnhanced: 查询详情响应:', response.data);

    if (response.data && response.data.success) {
      const queryData = response.data.data;
      console.log('QuerySelectorEnhanced: 获取到查询数据:', queryData);

      // 更新查询基本信息
      emit('update:queryId', queryId);
      emit('update:queryName', queryData.name || '');
      emit('update:queryDescription', queryData.description || '');
      emit('update:queryType', queryData.type || '');
      emit('update:querySql', queryData.sql || '');
      emit('update:queryParams', queryData.params || []);

      // 获取并更新版本信息
      console.log('QuerySelectorEnhanced: 开始加载查询版本:', queryId);
      try {
        const versionResponse = await instance.get(`/api/queries/${queryId}/versions`);
        console.log('QuerySelectorEnhanced: 查询版本响应:', versionResponse.data);

        if (versionResponse.data && versionResponse.data.success) {
          const versions = versionResponse.data.data || [];
          console.log('QuerySelectorEnhanced: 获取到版本列表:', versions);

          if (versions.length > 0) {
            try {
              // 找到最新版本
              const latestVersion = versions.find(v => v.isLatest) || versions[0];
              console.log('QuerySelectorEnhanced: 找到最新版本:', latestVersion);

              // 构造完整的版本信息对象，确保所有必要的属性都存在
              const versionInfo = {
                id: latestVersion.id || queryId,
                currentVersion: {
                  id: latestVersion.id || queryId,
                  versionNumber: latestVersion.versionNumber || 1,
                  isLatest: latestVersion.isLatest || true,
                  status: latestVersion.status || 'ACTIVE'
                }
              };
              console.log('QuerySelectorEnhanced: 构造的版本信息:', versionInfo);

              // 更新版本信息
              emit('update:queryVersion', versionInfo);
              console.log('QuerySelectorEnhanced: 已更新版本信息');
            } catch (error) {
              console.error('QuerySelectorEnhanced: 处理版本信息时出错:', error);
              // 使用查询ID作为版本ID
              const defaultVersion = createDefaultVersion(queryId);
              emit('update:queryVersion', defaultVersion);
              console.log('QuerySelectorEnhanced: 发生错误，设置默认版本:', defaultVersion);
            }
          } else {
            console.log('QuerySelectorEnhanced: 未找到版本信息，使用默认版本');
            // 使用查询ID作为版本ID
            const defaultVersion = createDefaultVersion(queryId);
            emit('update:queryVersion', defaultVersion);
            console.log('QuerySelectorEnhanced: 已设置默认版本:', defaultVersion);
          }
        } else {
          console.warn('QuerySelectorEnhanced: 获取版本信息失败:', versionResponse.data);
          // 使用查询ID作为版本ID
          const defaultVersion = createDefaultVersion(queryId);
          emit('update:queryVersion', defaultVersion);
          console.log('QuerySelectorEnhanced: 已设置默认版本:', defaultVersion);
        }
      } catch (error) {
        console.error('QuerySelectorEnhanced: 加载版本列表时出错:', error);
        // 使用查询ID作为版本ID
        const defaultVersion = createDefaultVersion(queryId);
        emit('update:queryVersion', defaultVersion);
        console.log('QuerySelectorEnhanced: 加载出错，设置默认版本:', defaultVersion);
      }
    } else {
      console.warn('QuerySelectorEnhanced: 获取查询详情失败:', response.data);
      messageStore.error('获取查询详情失败');
    }
  } catch (error) {
    console.error('QuerySelectorEnhanced: 处理查询选择时出错:', error);
    messageStore.error('加载查询信息失败');
  }
};

// 添加帮助函数创建默认版本信息
const createDefaultVersion = (queryId: string) => {
  return {
    id: queryId,
    currentVersion: {
      id: queryId,
      versionNumber: 1,
      isLatest: true,
      status: 'ACTIVE'
    }
  };
};
