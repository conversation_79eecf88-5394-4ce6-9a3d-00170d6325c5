<!-- 查询列表通用组件 -->
<template>
  <div class="query-list-container">
    <!-- 加载状态 -->
    <div v-if="loading" class="p-10 text-center">
      <i class="fas fa-circle-notch fa-spin text-indigo-500 text-3xl mb-4"></i>
      <p class="text-gray-500">正在加载查询服务列表...</p>
    </div>
    
    <!-- 空状态 -->
    <div v-else-if="queries.length === 0" class="p-10 text-center">
      <div class="rounded-full bg-gray-100 h-16 w-16 flex items-center justify-center mx-auto mb-4">
        <i class="fas fa-search text-gray-400 text-2xl"></i>
      </div>
      <h3 class="text-sm font-medium text-gray-900">暂无查询服务</h3>
      <p class="mt-1 text-sm text-gray-500">
        {{ hasFilters ? '没有符合筛选条件的查询服务' : '暂无数据' }}
      </p>
      <div v-if="!hideCreateButton" class="mt-6">
        <button 
          @click="$emit('create-query')"
          class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          <i class="fas fa-plus mr-2"></i>
          新建查询
        </button>
      </div>
    </div>
    
    <!-- 数据表格 -->
    <div v-else class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              名称
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              数据源
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              查询类型
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              服务状态
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              当前版本
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              创建时间
            </th>
            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              操作
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr v-for="query in queries" :key="query.id" @click="$emit('view-query', query)" class="hover:bg-gray-50 cursor-pointer">
            <td class="px-6 py-4 whitespace-nowrap" @click.stop>
              <div class="flex items-center">
                <div class="flex-shrink-0 h-10 w-10 flex items-center justify-center rounded-md bg-indigo-100 text-indigo-600">
                  <i class="fas fa-database"></i>
                </div>
                <div class="ml-4">
                  <div class="text-sm font-medium text-indigo-600 hover:text-indigo-800 cursor-pointer" @click="$emit('view-query', query)">
                    {{ query.name || '未命名查询' }}
                  </div>
                  <div v-if="query.description" class="text-sm text-gray-500 max-w-md truncate">
                    {{ query.description }}
                  </div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap" @click.stop>
              <div class="text-sm text-gray-900">{{ query.dataSourceName || '未指定' }}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap" @click.stop>
              <div class="flex items-center">
                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-indigo-100 text-indigo-800">
                  {{ query.queryType === 'SQL' ? 'SQL' : '自然语言' }}
                </span>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap" @click.stop>
              <div class="flex items-center">
                <span 
                  class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                  :class="{
                    'bg-blue-100 text-blue-800': query.serviceStatus && query.serviceStatus.toUpperCase() === 'ENABLED',
                    'bg-gray-100 text-gray-800': !query.serviceStatus || query.serviceStatus.toUpperCase() !== 'ENABLED'
                  }"
                >
                  {{ query.serviceStatus && query.serviceStatus.toUpperCase() === 'ENABLED' ? '已启用' : '已禁用' }}
                </span>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap" @click.stop>
              <div v-if="query.currentVersionId || query.currentVersion" class="flex items-center">
                <span class="inline-flex items-center mr-1">
                  <i class="fas fa-code-branch text-blue-500"></i>
                </span>
                <span v-if="query.currentVersion" class="px-2 py-0.5 rounded bg-indigo-100 text-indigo-800 font-medium">
                  V{{ query.currentVersion.versionNumber }}
                </span>
                <span v-else-if="query.versionNumber" class="px-2 py-0.5 rounded bg-blue-100 text-blue-800 font-medium">
                  V{{ query.versionNumber }}
                  <span v-if="query.versionStatus" class="ml-1 text-xs">
                    ({{ getVersionStatusText(query.versionStatus) }})
                  </span>
                </span>
                <span v-else-if="query.currentVersionId" class="px-2 py-0.5 rounded bg-blue-100 text-blue-800 font-medium">
                  V{{ formatVersionId(query.currentVersionId) }}
                </span>
                <span v-else class="text-gray-400">-</span>
              </div>
              <div v-else class="text-xs text-gray-400">-</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" @click.stop>
              {{ formatDate(query.createdAt) }}
              <div v-if="query.lastExecutedAt" class="text-xs text-gray-500 mt-1">
                <span class="font-medium">上次执行:</span> {{ formatRelativeDate(query.lastExecutedAt) }}
              </div>
              <div v-if="query.resultCount" class="text-xs text-gray-500 mt-1">
                <span class="font-medium">结果行数:</span> {{ formatNumber(query.resultCount) }}
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium" @click.stop>
              <button 
                @click.stop="$emit('view-query', query)"
                class="text-blue-600 hover:text-blue-900 mx-1"
                title="查看详情"
              >
                <i class="fas fa-eye"></i>
              </button>
              <button 
                @click.stop="$emit('edit-query', query)"
                class="text-indigo-600 hover:text-indigo-900 mx-1"
                title="编辑查询"
              >
                <i class="fas fa-edit"></i>
              </button>
              <button 
                @click.stop="$emit('toggle-status', query)"
                :class="[
                  'mx-1',
                  query.serviceStatus && query.serviceStatus.toUpperCase() === 'ENABLED' ? 'text-gray-600 hover:text-gray-900' : 'text-green-600 hover:text-green-900'
                ]"
                :title="query.serviceStatus && query.serviceStatus.toUpperCase() === 'ENABLED' ? '禁用' : '启用'"
              >
                <i :class="[
                  query.serviceStatus && query.serviceStatus.toUpperCase() === 'ENABLED' ? 'fas fa-pause-circle' : 'fas fa-play-circle'
                ]"></i>
              </button>
              <button 
                @click.stop="$emit('delete-query', query)"
                class="text-red-600 hover:text-red-900 mx-1"
                title="删除"
              >
                <i class="fas fa-trash-alt"></i>
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    
    <!-- 分页 -->
    <div v-if="totalItems > 0" class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
      <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
        <div>
          <p class="text-sm text-gray-700">
            显示
            <span class="font-medium">{{ (currentPage - 1) * pageSize + 1 }}</span>
            至
            <span class="font-medium">{{ Math.min(currentPage * pageSize, totalItems) }}</span>
            条，共
            <span class="font-medium">{{ totalItems }}</span>
            条记录
          </p>
        </div>
        <div>
          <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
            <!-- 上一页 -->
            <button
              @click="$emit('page-change', currentPage - 1)"
              :disabled="currentPage === 1"
              :class="[
                'relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium',
                currentPage === 1 ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-50'
              ]"
            >
              <i class="fas fa-chevron-left"></i>
            </button>
            
            <!-- 页码 -->
            <template v-for="page in pageNumbers" :key="page">
              <button
                v-if="page !== '...'"
                @click="$emit('page-change', page)"
                :class="[
                  'relative inline-flex items-center px-4 py-2 border',
                  page === currentPage
                    ? 'bg-indigo-50 border-indigo-500 text-indigo-600 z-10'
                    : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                ]"
              >
                {{ page }}
              </button>
              <span
                v-else
                class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm text-gray-700"
              >
                {{ page }}
              </span>
            </template>
            
            <!-- 下一页 -->
            <button
              @click="$emit('page-change', currentPage + 1)"
              :disabled="currentPage === totalPages"
              :class="[
                'relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium',
                currentPage === totalPages ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-50'
              ]"
            >
              <i class="fas fa-chevron-right"></i>
            </button>
          </nav>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

// 组件属性
const props = defineProps({
  // 查询列表数据
  queries: {
    type: Array,
    required: true,
    default: () => []
  },
  // 是否正在加载
  loading: {
    type: Boolean,
    default: false
  },
  // 当前页码
  currentPage: {
    type: Number,
    default: 1
  },
  // 每页显示数量
  pageSize: {
    type: Number,
    default: 10
  },
  // 总记录数
  totalItems: {
    type: Number,
    default: 0
  },
  // 是否有筛选条件
  hasFilters: {
    type: Boolean,
    default: false
  },
  // 是否隐藏创建按钮
  hideCreateButton: {
    type: Boolean,
    default: false
  }
})

// 组件事件
const emit = defineEmits([
  'view-query',
  'edit-query',
  'delete-query',
  'toggle-status',
  'create-query',
  'page-change'
])

// 计算总页数
const totalPages = computed(() => {
  return Math.ceil(props.totalItems / props.pageSize)
})

// 计算页码数组
const pageNumbers = computed(() => {
  if (totalPages.value <= 7) {
    return Array.from({ length: totalPages.value }, (_, i) => i + 1)
  }
  
  let pages = []
  const current = props.currentPage
  
  pages.push(1)
  
  if (current > 3) {
    pages.push('...')
  }
  
  // 当前页附近的页码
  for (let i = Math.max(2, current - 1); i <= Math.min(totalPages.value - 1, current + 1); i++) {
    pages.push(i)
  }
  
  if (current < totalPages.value - 2) {
    pages.push('...')
  }
  
  if (totalPages.value > 1) {
    pages.push(totalPages.value)
  }
  
  return pages
})

// 格式化版本状态文本
const getVersionStatusText = (status) => {
  switch (status) {
    case 'DRAFT':
      return '草稿'
    case 'PUBLISHED':
      return '已发布'
    case 'DEPRECATED':
      return '已废弃'
    default:
      return status
  }
}

// 格式化版本ID (简单提取版本号)
const formatVersionId = (versionId) => {
  if (!versionId) return '?'
  // 尝试从版本ID中提取版本号
  const match = versionId.match(/\d+$/)
  return match ? match[0] : '?'
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '未知'
  
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 格式化相对日期
const formatRelativeDate = (dateString) => {
  if (!dateString) return '未知'
  
  const date = new Date(dateString)
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)
  
  if (diffInSeconds < 60) {
    return `${diffInSeconds}秒前`
  }
  
  const diffInMinutes = Math.floor(diffInSeconds / 60)
  if (diffInMinutes < 60) {
    return `${diffInMinutes}分钟前`
  }
  
  const diffInHours = Math.floor(diffInMinutes / 60)
  if (diffInHours < 24) {
    return `${diffInHours}小时前`
  }
  
  const diffInDays = Math.floor(diffInHours / 24)
  if (diffInDays < 30) {
    return `${diffInDays}天前`
  }
  
  return formatDate(dateString)
}

// 格式化数字
const formatNumber = (num) => {
  if (num === undefined || num === null) return '-'
  return new Intl.NumberFormat('zh-CN').format(num)
}
</script>

<style scoped>
.query-list-container {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  overflow: hidden;
}
</style> 