<template>
  <div class="chart-config-panel">
    <a-form :model="chartConfig" layout="vertical">
      <!-- 图表类型选择 -->
      <a-form-item label="图表类型" required>
        <a-select
          v-model:value="chartConfig.chartType"
          @change="handleChartTypeChange"
          placeholder="请选择图表类型"
        >
          <a-select-option value="line">折线图</a-select-option>
          <a-select-option value="bar">柱状图</a-select-option>
          <a-select-option value="pie">饼图</a-select-option>
          <a-select-option value="scatter">散点图</a-select-option>
        </a-select>
      </a-form-item>

      <!-- 图表标题配置 -->
      <a-form-item label="图表标题">
        <a-input
          v-model:value="chartConfig.title"
          placeholder="请输入图表标题"
        />
      </a-form-item>

      <!-- 根据图表类型显示不同的字段映射选项 -->
      <div class="field-mapping">
        <h3 class="field-mapping-title">字段映射</h3>

        <!-- 折线图和柱状图的字段映射 -->
        <template v-if="['line', 'bar'].includes(chartConfig.chartType)">
          <a-form-item label="分类字段" required>
            <a-input
              v-model:value="chartConfig.categoryField"
              placeholder="请输入分类字段名称"
            />
          </a-form-item>
          <a-form-item label="数值字段" required>
            <a-input
              v-model:value="chartConfig.valueField"
              placeholder="请输入数值字段名称"
            />
          </a-form-item>
        </template>

        <!-- 饼图的字段映射 -->
        <template v-if="chartConfig.chartType === 'pie'">
          <a-form-item label="名称字段" required>
            <a-input
              v-model:value="chartConfig.nameField"
              placeholder="请输入名称字段名称"
            />
          </a-form-item>
          <a-form-item label="数值字段" required>
            <a-input
              v-model:value="chartConfig.valueField"
              placeholder="请输入数值字段名称"
            />
          </a-form-item>
        </template>

        <!-- 散点图的字段映射 -->
        <template v-if="chartConfig.chartType === 'scatter'">
          <a-form-item label="X轴字段" required>
            <a-input
              v-model:value="chartConfig.xField"
              placeholder="请输入X轴字段名称"
            />
          </a-form-item>
          <a-form-item label="Y轴字段" required>
            <a-input
              v-model:value="chartConfig.yField"
              placeholder="请输入Y轴字段名称"
            />
          </a-form-item>
          <a-form-item label="大小字段">
            <a-input
              v-model:value="chartConfig.sizeField"
              placeholder="请输入大小字段名称（可选）"
            />
          </a-form-item>
        </template>
      </div>

      <!-- 样式设置 -->
      <div class="style-settings">
        <h3 class="style-settings-title">样式设置</h3>
        <a-form-item label="高度">
          <a-input-number
            v-model:value="chartConfig.height"
            :min="200"
            :max="800"
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item label="自定义样式 (JSON)">
          <a-textarea
            v-model:value="styleJsonString"
            placeholder="请输入JSON格式的样式配置"
            :rows="4"
            @change="handleStyleJsonChange"
          />
          <div v-if="styleJsonError" class="error-text">
            {{ styleJsonError }}
          </div>
        </a-form-item>
      </div>

      <!-- 数据源设置 -->
      <div class="datasource-settings">
        <h3 class="datasource-settings-title">数据源设置</h3>
        <a-form-item label="数据源URL" required>
          <a-input
            v-model:value="chartConfig.dataUrl"
            placeholder="请输入数据源URL"
          />
        </a-form-item>
        <a-form-item label="查询参数 (JSON)">
          <a-textarea
            v-model:value="queryParamsJsonString"
            placeholder="请输入JSON格式的查询参数"
            :rows="3"
            @change="handleQueryParamsJsonChange"
          />
          <div v-if="queryParamsJsonError" class="error-text">
            {{ queryParamsJsonError }}
          </div>
        </a-form-item>
        <a-form-item label="请求头 (JSON)">
          <a-textarea
            v-model:value="headersJsonString"
            placeholder="请输入JSON格式的请求头"
            :rows="3"
            @change="handleHeadersJsonChange"
          />
          <div v-if="headersJsonError" class="error-text">
            {{ headersJsonError }}
          </div>
        </a-form-item>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <a-button type="primary" @click="handleSave">保存配置</a-button>
        <a-button style="margin-left: 10px" @click="handlePreview">预览图表</a-button>
      </div>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, onMounted, watch } from 'vue';
import { message } from 'ant-design-vue';

// 定义事件
const emit = defineEmits(['update:config', 'preview', 'save', 'validate']);

// 定义组件Props
const props = defineProps({
  config: {
    type: Object,
    default: () => ({
      chartType: 'line',
      title: '',
      categoryField: '',
      valueField: '',
      nameField: '',
      xField: '',
      yField: '',
      sizeField: '',
      height: 400,
      style: {},
      dataUrl: '',
      queryParams: {},
      headers: {}
    })
  }
});

// 创建图表配置的响应式状态
const chartConfig = reactive({
  chartType: props.config.chartType || 'line',
  title: props.config.title || '',
  categoryField: props.config.categoryField || '',
  valueField: props.config.valueField || '',
  nameField: props.config.nameField || '',
  xField: props.config.xField || '',
  yField: props.config.yField || '',
  sizeField: props.config.sizeField || '',
  height: props.config.height || 400,
  style: { ...(props.config.style || {}) },
  dataUrl: props.config.dataUrl || '',
  queryParams: { ...(props.config.queryParams || {}) },
  headers: { ...(props.config.headers || {}) }
});

// JSON字符串处理
const styleJsonString = ref(JSON.stringify(chartConfig.style, null, 2));
const queryParamsJsonString = ref(JSON.stringify(chartConfig.queryParams, null, 2));
const headersJsonString = ref(JSON.stringify(chartConfig.headers, null, 2));

// 错误状态
const styleJsonError = ref('');
const queryParamsJsonError = ref('');
const headersJsonError = ref('');

// 监听props.config的变化
watch(
  () => props.config,
  (newConfig) => {
    Object.assign(chartConfig, {
      chartType: newConfig.chartType || 'line',
      title: newConfig.title || '',
      categoryField: newConfig.categoryField || '',
      valueField: newConfig.valueField || '',
      nameField: newConfig.nameField || '',
      xField: newConfig.xField || '',
      yField: newConfig.yField || '',
      sizeField: newConfig.sizeField || '',
      height: newConfig.height || 400,
      style: { ...(newConfig.style || {}) },
      dataUrl: newConfig.dataUrl || '',
      queryParams: { ...(newConfig.queryParams || {}) },
      headers: { ...(newConfig.headers || {}) }
    });
    
    styleJsonString.value = JSON.stringify(chartConfig.style, null, 2);
    queryParamsJsonString.value = JSON.stringify(chartConfig.queryParams, null, 2);
    headersJsonString.value = JSON.stringify(chartConfig.headers, null, 2);
  },
  { deep: true }
);

// 监听图表配置更新
watch(
  chartConfig,
  () => {
    emitConfigUpdate();
  },
  { deep: true }
);

// 组件挂载时初始化
onMounted(() => {
  // 初始化图表配置
  emitConfigUpdate();
});

// 图表类型变更处理
const handleChartTypeChange = () => {
  emitConfigUpdate();
};

// JSON处理函数
const handleStyleJsonChange = () => {
  try {
    const parsedJson = JSON.parse(styleJsonString.value);
    chartConfig.style = parsedJson;
    styleJsonError.value = '';
  } catch (error) {
    styleJsonError.value = '无效的JSON格式';
  }
};

const handleQueryParamsJsonChange = () => {
  try {
    const parsedJson = JSON.parse(queryParamsJsonString.value);
    chartConfig.queryParams = parsedJson;
    queryParamsJsonError.value = '';
  } catch (error) {
    queryParamsJsonError.value = '无效的JSON格式';
  }
};

const handleHeadersJsonChange = () => {
  try {
    const parsedJson = JSON.parse(headersJsonString.value);
    chartConfig.headers = parsedJson;
    headersJsonError.value = '';
  } catch (error) {
    headersJsonError.value = '无效的JSON格式';
  }
};

// 验证配置是否完整
const validateConfig = (): { valid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (!chartConfig.chartType) {
    errors.push('请选择图表类型');
  }

  if (!chartConfig.dataUrl) {
    errors.push('请输入数据源URL');
  }

  // 根据图表类型验证必填字段
  if (['line', 'bar'].includes(chartConfig.chartType)) {
    if (!chartConfig.categoryField) {
      errors.push('请输入分类字段名称');
    }
    if (!chartConfig.valueField) {
      errors.push('请输入数值字段名称');
    }
  } else if (chartConfig.chartType === 'pie') {
    if (!chartConfig.nameField) {
      errors.push('请输入名称字段名称');
    }
    if (!chartConfig.valueField) {
      errors.push('请输入数值字段名称');
    }
  } else if (chartConfig.chartType === 'scatter') {
    if (!chartConfig.xField) {
      errors.push('请输入X轴字段名称');
    }
    if (!chartConfig.yField) {
      errors.push('请输入Y轴字段名称');
    }
  }

  return {
    valid: errors.length === 0,
    errors
  };
};

// 更新配置
const emitConfigUpdate = () => {
  emit('update:config', { ...chartConfig });
};

// 保存配置
const handleSave = () => {
  const validation = validateConfig();
  emit('validate', validation);

  if (!validation.valid) {
    message.error(validation.errors.join(', '));
    return;
  }

  emit('save', { ...chartConfig });
  message.success('配置已保存');
};

// 预览图表
const handlePreview = () => {
  const validation = validateConfig();
  emit('validate', validation);

  if (!validation.valid) {
    message.error(validation.errors.join(', '));
    return;
  }

  emit('preview', { ...chartConfig });
};
</script>

<style scoped>
.chart-config-panel {
  padding: 16px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.field-mapping, .style-settings, .datasource-settings {
  margin-top: 24px;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  background-color: #fafafa;
}

.field-mapping-title, .style-settings-title, .datasource-settings-title {
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.error-text {
  color: #ff4d4f;
  font-size: 12px;
  margin-top: 4px;
}

.action-buttons {
  margin-top: 24px;
  display: flex;
  justify-content: flex-end;
}
</style>