<template>
  <div>
    <!-- 保存管理组件 - 无UI -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useIntegrationStore } from '@/stores/integration';
import type { IntegrationType, TableConfig, ChartConfig, QueryParam, IntegrationData } from '@/types/unified-integration';
// 导入message服务，删除messageStore
import { message } from '@/services/message';

// 定义组件属性
const props = defineProps<{
  integration: IntegrationData;
  queryParams: QueryParam[];
  validationManagerRef: any;
  tableConfig: any;
  chartConfig: any;
}>();

// 定义事件
const emit = defineEmits<{
  (e: 'save-started'): void;
  (e: 'save-completed', success: boolean, id?: string): void;
  (e: 'id-updated', id: string): void;
}>();

// 本地状态
const router = useRouter();
const integrationStore = useIntegrationStore();
const saveLoading = ref(false);

/**
 * 获取用于保存的集成对象
 */
const getIntegrationObj = () => {
  const versionIdValue = typeof props.integration.versionId === 'object' 
    ? props.integration.versionId.id
    : props.integration.versionId;
  
  // 构建保存数据对象
  return {
    id: props.integration.id,
    name: props.integration.name,
    description: props.integration.description,
    type: props.integration.type,
    status: props.integration.status,
    dataSourceId: props.integration.dataSourceId,
    queryId: props.integration.queryId,
    versionId: versionIdValue,
    queryParams: props.queryParams,
    tableConfig: props.integration.type === 'TABLE' || props.integration.type === 'SIMPLE_TABLE' 
      ? props.integration.tableConfig 
      : undefined,
    chartConfig: props.integration.type === 'CHART' 
      ? props.integration.chartConfig 
      : undefined,
    meta: props.integration.meta || {}
  };
};

/**
 * 保存集成
 */
const saveIntegration = async () => {
  saveLoading.value = true;
  emit('save-started');
  console.log('[集成保存] 开始保存集成数据');
  
  // 表单验证
  const valid = props.validationManagerRef ? await props.validationManagerRef.validateFormWithFeedback() : false;
  if (!valid) {
    // 尝试获取验证错误的详细信息
    const validationErrors = props.validationManagerRef ? 
      props.validationManagerRef.getValidationErrors?.() || {} : {};
    
    // 处理特定类型的错误
    if (validationErrors.tableColumns) {
      message.error(`表格配置验证错误: ${validationErrors.tableColumns}`, 5000);
    } else if (validationErrors.requiredFields) {
      message.error(`必填项验证失败: ${validationErrors.requiredFields}`, 5000);
    } else {
      message.error({
        content: '表单验证失败',
        description: '请检查必填项和输入格式，确保所有必要字段已填写',
        duration: 5000
      });
    }
    
    saveLoading.value = false;
    emit('save-completed', false);
    return;
  }

  const integrationObj = getIntegrationObj();
  console.log('[集成保存] 准备保存的数据对象:', JSON.stringify(integrationObj));
  
  try {
    if (props.integration.id) {
      // 更新现有集成
      console.log(`[集成保存] 更新现有集成 (ID: ${props.integration.id})`);
      const updatedIntegration = await integrationStore.updateIntegration(props.integration.id, integrationObj);
      console.log('[集成保存] 更新操作API响应:', updatedIntegration);
      
      // 保存到localStorage，键名使用集成ID前缀
      const storageKey = `integration_config_${props.integration.id}`;
      const storageData = {
        queryParams: props.queryParams,
        tableConfig: props.tableConfig, 
        chartConfig: props.chartConfig,
        lastSaved: new Date().toISOString()
      };
      
      localStorage.setItem(storageKey, JSON.stringify(storageData));
      console.log('[集成保存] 保存配置到localStorage, key:', storageKey);
      
      // 成功后清除验证错误状态
      if (props.validationManagerRef && props.validationManagerRef.clearValidationErrors) {
        props.validationManagerRef.clearValidationErrors();
      }
      
      // 集成更新成功消息 - 这是唯一显示成功消息的地方
      message.success({
      content: '集成更新成功',
      description: '您的集成配置已成功保存到服务器',
      duration: 5000,
      allowDuplicate: true
    });
      
      emit('save-completed', true, props.integration.id);
    } else {
      // 创建新集成
      console.log('[集成保存] 创建新集成');
      const newId = await integrationStore.createIntegration(integrationObj);
      console.log('[集成保存] 创建操作API响应, 新ID:', newId);
      
      if (!newId) {
        throw new Error('创建集成失败，服务器未返回ID');
      }
      
      // 获取实际的ID字符串
      const integrationId = typeof newId === 'object' && newId !== null ? newId.id : newId;
      if (!integrationId) {
        throw new Error('创建集成失败，无法获取有效的集成ID');
      }
      
      // 创建后也保存到localStorage
      const storageKey = `integration_config_${integrationId}`;
      const storageData = {
        queryParams: props.queryParams,
        tableConfig: props.tableConfig, 
        chartConfig: props.chartConfig,
        lastSaved: new Date().toISOString()
      };
      
      localStorage.setItem(storageKey, JSON.stringify(storageData));
      console.log('[集成保存] 保存配置到localStorage, key:', storageKey);
      
      // 通知父组件更新ID
      emit('id-updated', integrationId);
      
      // 成功后清除验证错误状态
      if (props.validationManagerRef && props.validationManagerRef.clearValidationErrors) {
        props.validationManagerRef.clearValidationErrors();
      }
      
      // 集成创建成功消息 - 这是唯一显示成功消息的地方
      message.success({
      content: '集成创建成功',
      description: '新的集成已成功创建并保存到服务器',
      duration: 5000,
      allowDuplicate: true
    });
      
      // 导航到编辑页 - 使用正确的ID
      console.log('[集成保存] 导航到编辑页, 路径:', `/integration/edit/${integrationId}`);
      router.push({ path: `/integration/edit/${integrationId}` });
      
      emit('save-completed', true, integrationId);
    }
  } catch (e: any) {
    // 错误处理增强
    console.error('[集成保存] 保存集成失败:', e);
    
    // 尝试提取更有用的错误信息
    let errorMessage = '保存集成失败';
    
    if (e.message) {
      errorMessage += `: ${e.message}`;
    }
    
    if (e.response) {
      // 如果是Axios错误对象，尝试提取状态码和后端返回的错误消息
      const status = e.response.status;
      const serverMessage = e.response.data && e.response.data.message;
      
      console.error('[集成保存] HTTP错误:', status, serverMessage);
      
      if (status && serverMessage) {
        errorMessage = `保存失败 (${status}): ${serverMessage}`;
      } else if (status) {
        errorMessage = `保存失败，服务器返回: ${status}`;
      }
    }
    
    // 显示错误消息
    message.error(errorMessage);
    
    // 保存到临时存储，以便恢复
    try {
      const tempKey = `integration_temp_${new Date().getTime()}`;
      const tempData = {
        ...integrationObj,
        queryParams: props.queryParams,
        tableConfig: props.tableConfig,
        chartConfig: props.chartConfig,
        saveError: errorMessage,
        timestamp: new Date().toISOString()
      };
      
      localStorage.setItem(tempKey, JSON.stringify(tempData));
      console.log('[集成保存] 错误时保存到临时存储:', tempKey);
    } catch (storageError) {
      console.error('[集成保存] 临时存储失败:', storageError);
    }
    
    emit('save-completed', false);
  } finally {
    saveLoading.value = false;
    console.log('[集成保存] 保存操作完成');
  }
};

// 导出组件方法
defineExpose({
  saveIntegration,
  getIntegrationObj,
  saveLoading
});
</script>