<template>
  <div class="bg-white shadow rounded-lg overflow-hidden">
    <div class="px-4 py-5 bg-gray-50 border-b border-gray-200 text-sm font-medium text-gray-900">
      集成类型选择
    </div>
    <div class="p-6">
      <div class="grid grid-cols-1 gap-6">
        <!-- 集成类型选择 -->
        <div>
          <label for="integration-type" class="block text-sm font-medium text-gray-700 mb-1">
            集成类型 <span class="text-red-500">*</span>
          </label>
          <select
            id="integration-type"
            v-model="selectedType"
            class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm force-border"
            @change="handleTypeChange"
          >
            <option v-for="option in filteredOptions" :key="option.value" :value="option.value" :disabled="option.disabled">
              {{ option.label }} {{ option.disabled ? '(即将上线)' : '' }}
            </option>
          </select>
          <p v-if="error" class="mt-1 text-sm text-red-600">
            {{ error }}
          </p>
        </div>

        <!-- 类型说明 -->
        <div class="mt-2">
          <div v-if="selectedType === 'FORM'" class="bg-blue-50 p-4 rounded-md">
            <div class="flex">
              <div class="flex-shrink-0">
                <i class="fas fa-info-circle text-blue-400"></i>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-blue-800">表单集成说明</h3>
                <div class="mt-2 text-sm text-blue-700">
                  <p>表单集成可用于创建数据输入表单，用户填写后的数据将保存到数据源中。适用于数据采集、问卷调查等场景。</p>
                </div>
              </div>
            </div>
          </div>

          <div v-if="selectedType === 'TABLE'" class="bg-green-50 p-4 rounded-md">
            <div class="flex">
              <div class="flex-shrink-0">
                <i class="fas fa-info-circle text-green-400"></i>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-green-800">表格集成说明</h3>
                <div class="mt-2 text-sm text-green-700">
                  <p>表格集成可用于展示和管理数据，支持分页、排序和过滤功能。适用于数据展示、后台管理等场景。</p>
                </div>
              </div>
            </div>
          </div>

          <div v-if="selectedType === 'CHART'" class="bg-purple-50 p-4 rounded-md">
            <div class="flex">
              <div class="flex-shrink-0">
                <i class="fas fa-info-circle text-purple-400"></i>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-purple-800">图表集成说明</h3>
                <div class="mt-2 text-sm text-purple-700">
                  <p>图表集成可用于数据可视化，支持线图、柱状图、饼图和散点图等多种图表类型。适用于数据分析、报表等场景。</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// defineProps和defineEmits是编译器宏，不需要导入
import { ref, watch, computed } from 'vue';
import { INTEGRATION_TYPES } from '@/config/integrationConfigs';

// 使用从配置文件导入的集成类型选项
const integrationTypeOptions = INTEGRATION_TYPES;

// 过滤显示的选项
const filteredOptions = computed(() => {
  return integrationTypeOptions.map(option => ({
    ...option,
    // 确保在UI上显示disabled状态
    disabled: !!option.disabled
  }));
});

const props = defineProps<{
  modelValue: string;
  validationTriggered?: boolean;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void;
  (e: 'change', value: string): void;
  (e: 'validation', valid: boolean, error: string): void;
}>();

// 本地状态
const selectedType = ref(props.modelValue || '');
const error = ref('');

// 处理类型变更
const handleTypeChange = () => {
  // 检查选择的类型是否被禁用
  const selected = integrationTypeOptions.find(opt => opt.value === selectedType.value);
  if (selected?.disabled) {
    error.value = `${selected.label}当前不可用，即将上线`;
    return;
  }

  // 更新父组件的值
  emit('update:modelValue', selectedType.value);

  // 触发change事件
  emit('change', selectedType.value);

  // 清除错误
  validateType();
};

// 验证类型选择
const validateType = () => {
  // 检查是否选择了有效类型
  if (!selectedType.value) {
    error.value = '请选择集成类型';
    emit('validation', false, error.value);
    return false;
  }

  // 检查选择的类型是否被禁用
  const selected = integrationTypeOptions.find(opt => opt.value === selectedType.value);
  if (selected?.disabled) {
    error.value = `${selected.label}当前不可用，即将上线`;
    emit('validation', false, error.value);
    return false;
  }

  // 验证通过
  error.value = '';
  emit('validation', true, '');
  return true;
};

// 监听父组件值变化
watch(() => props.modelValue, (newVal) => {
  selectedType.value = newVal;
});

// 监听验证触发器
watch(() => props.validationTriggered, (newVal) => {
  if (newVal) {
    validateType();
  }
});
</script>

<style scoped>
.force-border {
  border: 1px solid #d1d5db !important;
}
</style>