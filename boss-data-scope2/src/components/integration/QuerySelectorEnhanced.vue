<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { queryService } from '@/services/query';
import { useQueryStore } from '@/stores/query';
import { message } from '@/services/message';
import QueryParamsConfig from './QueryParamsConfig.vue';
import QueryPreview from './QueryPreview.vue';
import type { QueryHistoryParams, QueryType } from '@/types/query';

// 定义查询项目类型
interface QueryItem {
  id: string;
  name: string;
  description?: string;
  type?: string;
  dataSourceId?: string;
  sql?: string;
  parameters?: Array<{
    name: string;
    defaultValue?: any;
    required?: boolean;
  }>;
}

// 组件属性
const props = defineProps<{
  modelValue: string;
  label?: string;
  placeholder?: string;
  error?: string;
  required?: boolean;
  disabled?: boolean;
  showPreview?: boolean; // 是否显示预览功能
  dataSourceId?: string; // 新增数据源ID属性，用于级联查询
}>();

// 组件事件
const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void;
  (e: 'selected', id: string, data: any): void;
  (e: 'paramsChange', params: Record<string, any>): void;
}>();

// Store
const queryStore = useQueryStore();

// 状态
const queries = ref<Array<QueryItem>>([]);
const loading = ref(false);
const searchText = ref('');
const selectedQueryId = ref(props.modelValue || '');

// 参数配置相关
const showParamsConfig = ref(false);
const paramValues = ref<Record<string, any>>({});
const paramsValid = ref(true);

// 预览相关
const showPreview = ref(false);
const previewPageSize = ref(10);

// 下拉菜单相关
const isDropdownOpen = ref(false);
const dropdownRef = ref<HTMLElement | null>(null);
const selectContainerRef = ref<HTMLElement | null>(null);

// 计算下拉菜单是否应该向上展开
const shouldDropUp = computed(() => {
  if (selectContainerRef.value) {
    const rect = selectContainerRef.value.getBoundingClientRect();
    const windowHeight = window.innerHeight;
    const dropDownHeight = 250; // 假设下拉菜单的最大高度
    const dropUpThreshold = 100; // 假设下拉菜单向上展开的阈值

    return rect.bottom + dropDownHeight > windowHeight - dropUpThreshold;
  }
  return false;
});

// 计算属性
const filteredQueries = computed<Array<QueryItem>>(() => {
  let result = queries.value;

  // 先过滤数据源
  if (props.dataSourceId) {
    result = result.filter(query => query.dataSourceId === props.dataSourceId);
  }

  if (!searchText.value) {
    // 无需过滤名称，因为我们已经在加载时确保所有查询都有名称
    return result;
  }

  const searchLower = searchText.value.toLowerCase();
  return result.filter(query => {
    return query.name.toLowerCase().includes(searchLower) ||
           (query.description && query.description.toLowerCase().includes(searchLower)) ||
           query.id.toLowerCase().includes(searchLower);
  });
});

// 监听selectedQueryId变化
watch(selectedQueryId, (newValue) => {
  emit('update:modelValue', newValue);

  // 查找选中的查询数据
  const selectedQuery = queries.value.find(q => q.id === newValue);
  if (selectedQuery) {
    emit('selected', newValue, selectedQuery);
  }

  // 重置参数值
  if (newValue) {
    paramValues.value = {};
  }
});

// 监听modelValue变化
watch(() => props.modelValue, (newValue) => {
  selectedQueryId.value = newValue;
});

// 监听dataSourceId变化，重新加载查询
watch(() => props.dataSourceId, (newValue, oldValue) => {
  console.log(`QuerySelectorEnhanced: 数据源ID变更: ${oldValue} -> ${newValue}`);

  if (newValue) {
    // 如果数据源ID变更，重新加载查询列表
    loadQueries();

    // 如果当前选择的查询不属于新的数据源，则清空选择
    if (selectedQueryId.value) {
      const currentQuery = queries.value.find(q => q.id === selectedQueryId.value);
      if (currentQuery && currentQuery.dataSourceId !== newValue) {
        console.log(`QuerySelectorEnhanced: 已选择的查询(${selectedQueryId.value})不属于新数据源(${newValue})，清空选择`);
        selectedQueryId.value = '';
        emit('update:modelValue', '');
      }
    }
  }
}, { immediate: false });

// 处理点击外部关闭下拉菜单
const handleClickOutside = (event: MouseEvent) => {
  // 检查点击是否在选择器或下拉菜单之外
  if (selectContainerRef.value && !selectContainerRef.value.contains(event.target as Node) &&
      dropdownRef.value && !dropdownRef.value.contains(event.target as Node)) {
    isDropdownOpen.value = false;
  }
};

// 生命周期钩子
onMounted(async () => {
  await loadQueries();
  document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});

// 加载查询列表
const loadQueries = async () => {
  loading.value = true;

  try {
    console.log('QuerySelectorEnhanced: 开始获取查询列表数据...');

    // 构造查询参数，如果有数据源ID则包含
    const params: QueryHistoryParams = {
      page: 1,
      size: 100,
      queryType: 'SQL' as QueryType
    };

    if (props.dataSourceId) {
      params.dataSourceId = props.dataSourceId;
      console.log(`QuerySelectorEnhanced: 使用数据源ID筛选查询: ${props.dataSourceId}`);
    }

    // 直接使用queryService而不是queryStore
    const result = await queryService.getQueries(params);

    console.log('QuerySelectorEnhanced: 获取到的查询列表数据:', result);

    if (result && result.data) {
      // 确保我们正确处理API返回的分页结果，取出items
      const queryItems = result.data?.items || [];

      queries.value = queryItems.map((query: any) => {
        console.log('QuerySelectorEnhanced: 处理查询数据:', query.id, query.name, query.dataSourceId);
        return {
          id: query.id,
          name: query.name || `查询 ${query.id}`, // 确保始终有名称
          description: query.description,
          type: query.queryType || query.type,
          dataSourceId: query.dataSourceId || query.datasource_id, // 兼容两种命名风格
          sql: query.sql,
          parameters: query.parameters || []
        };
      });

      console.log('QuerySelectorEnhanced: 处理后的查询列表:', queries.value);
      console.log(`QuerySelectorEnhanced: 与数据源 ${props.dataSourceId} 关联的查询数量:`,
        props.dataSourceId ? queries.value.filter(q => q.dataSourceId === props.dataSourceId).length : queries.value.length);
    }
  } catch (error) {
    console.error('加载查询列表失败', error);
    message.error({
      content: '加载查询列表失败',
      description: '无法从服务器获取查询列表，请检查网络连接或稍后重试',
      duration: 5000
    });
  } finally {
    loading.value = false;
  }
};

// 刷新查询列表
const refreshQueries = async (event?: Event) => {
  if (event) {
    event.stopPropagation();
  }
  await loadQueries();
};

// 查询选择变更处理
const handleQueryChange = (event: Event) => {
  const target = event.target as HTMLSelectElement;
  selectedQueryId.value = target.value;
};

// 切换下拉菜单状态
const toggleDropdown = (event?: Event) => {
  if (event) {
    event.stopPropagation();
  }

  if (!props.disabled && !loading.value) {
    // 切换下拉菜单状态
    isDropdownOpen.value = !isDropdownOpen.value;

    // 如果打开了下拉菜单，则立即检测位置
    if (isDropdownOpen.value && selectContainerRef.value) {
      // 在下一个微任务中执行，确保DOM已经更新
      setTimeout(() => {
        // 重新计算位置信息
        const rect = selectContainerRef.value!.getBoundingClientRect();
        const windowHeight = window.innerHeight;
        const dropDownHeight = 250; // 下拉菜单的最大高度

        // 如果下拉菜单会超出视口底部，则向上展开
        if (rect.bottom + dropDownHeight > windowHeight - 100) {
          console.log('QuerySelectorEnhanced: 下拉菜单将向上展开');
        } else {
          console.log('QuerySelectorEnhanced: 下拉菜单将向下展开');
        }
      }, 0);
    }
  }
};

// 选择查询 - 修复类型定义
const selectQuery = (queryId: string): void => {
  selectedQueryId.value = queryId;
  isDropdownOpen.value = false;

  // 查找选择的查询对象
  const selectedQuery = queries.value.find(q => q.id === queryId);
  if (selectedQuery && selectedQuery.parameters && selectedQuery.parameters.length > 0) {
    // 设置查询参数
    paramValues.value = selectedQuery.parameters.reduce((acc: Record<string, any>, param: any) => {
      acc[param.name] = param.defaultValue || '';
      return acc;
    }, {});

    showParamsConfig.value = true;
  }
};

// 处理参数变化
const handleParamsChange = (values: Record<string, any>) => {
  paramValues.value = values;
  emit('paramsChange', values);
};

// 处理参数验证状态变化
const handleParamsValidChange = (valid: boolean) => {
  paramsValid.value = valid;
};

// 切换预览显示
const togglePreview = () => {
  showPreview.value = !showPreview.value;
};

// 加载预览数据
const loadPreviewData = async (refresh: boolean = false) => {
  if (refresh) {
    await loadQueries();
  }

  // 预览数据加载逻辑
  if (selectedQueryId.value && props.showPreview) {
    showPreview.value = true;
  }
};
</script>

<template>
  <div class="query-selector-container">
    <!-- 主选择器区域 -->
    <div class="query-selector">
      <!-- 标签 -->
      <label v-if="props.label" :for="'query-selector-' + (Math.random().toString(36).substring(2))" class="block text-sm font-medium text-gray-700 mb-1">
        {{ props.label }}
        <span v-if="props.required" class="text-red-500">*</span>
      </label>

      <!-- 选择器输入区域 -->
      <div class="relative">
        <!-- 选择框 -->
        <div
          class="block w-full rounded-md border-gray-300 shadow-sm sm:text-sm cursor-pointer force-border px-3 py-2 select-container"
          :class="{ 'border-red-300': props.error, 'bg-gray-50': props.disabled || loading, 'active': isDropdownOpen, 'border-indigo-500 ring-1 ring-indigo-500': isDropdownOpen }"
          @click="toggleDropdown"
          ref="selectContainerRef"
          style="height: 38px;"
        >
          <div class="flex items-center justify-between w-full">
            <div class="truncate flex-grow">
              {{ filteredQueries.find(q => q.id === selectedQueryId)?.name || (selectedQueryId ? `查询 (ID: ${selectedQueryId})` : props.placeholder || '请选择数据查询') }}
            </div>
            <div class="flex items-center ml-2 flex-shrink-0">
              <div v-if="loading">
                <i class="fas fa-circle-notch fa-spin text-gray-400"></i>
              </div>
              <button
                v-else
                type="button"
                class="text-gray-400 hover:text-gray-500 focus:outline-none p-1 mr-1"
                @click.stop="refreshQueries"
                title="刷新查询列表"
              >
                <i class="fas fa-sync-alt"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- 下拉菜单 -->
        <div
          v-if="isDropdownOpen"
          ref="dropdownRef"
          class="dropdown-container"
          :class="{'dropdown-up': shouldDropUp}"
        >
          <!-- 搜索输入框 -->
          <div class="p-2 border-b border-gray-200">
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i class="fas fa-search text-gray-400 text-sm"></i>
              </div>
              <input
                v-model="searchText"
                type="text"
                class="block w-full pl-10 pr-3 py-1.5 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                :placeholder="searchText ? '' : '搜索查询...'"
                @input="isDropdownOpen = true"
              />
            </div>
          </div>

          <!-- 内容区域 -->
          <div v-if="loading" class="p-3 text-sm text-gray-500 text-center">
            <i class="fas fa-circle-notch fa-spin mr-1"></i> 加载中...
          </div>
          <div v-else-if="filteredQueries.length === 0" class="p-3 text-sm text-gray-500 text-center">
            <template v-if="searchText">
              没有找到匹配的查询
            </template>
            <template v-else-if="props.dataSourceId && queries.length > 0">
              当前数据源下没有可用查询
            </template>
            <template v-else>
              没有可用的查询
            </template>
          </div>
          <ul v-else class="py-1 list-none m-0">
            <li
              v-for="query in filteredQueries"
              :key="query.id"
              class="dropdown-item"
              :class="{ 'bg-indigo-50 text-indigo-700 font-medium': query.id === selectedQueryId }"
              @click="selectQuery(query.id)"
            >
              <div class="font-medium truncate item-title">{{ query.name }}</div>
              <div v-if="query.description" class="text-xs text-gray-500 truncate item-desc">{{ query.description }}</div>
            </li>
          </ul>
        </div>
      </div>

      <!-- 错误信息 -->
      <div v-if="props.error" class="mt-1 text-sm text-red-600">
        {{ props.error }}
      </div>

      <!-- 参数配置面板 - 移除标题和外框 -->
      <div v-if="showParamsConfig && selectedQueryId" class="mt-4">
        <QueryParamsConfig
          :query-id="selectedQueryId"
          v-model="paramValues"
          @valid="handleParamsValidChange"
        />
      </div>

      <!-- 预览面板 -->
      <div v-if="showPreview && selectedQueryId" class="mt-4">
        <div class="bg-white shadow sm:rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <div class="flex justify-between items-center mb-4">
              <h3 class="text-lg leading-6 font-medium text-gray-900">
                数据预览
              </h3>
              <button
                type="button"
                @click="togglePreview"
                class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-gray-700 bg-gray-100 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
              >
                <i class="fas fa-times mr-1"></i>
                关闭预览
              </button>
            </div>

            <div class="mt-4">
              <QueryPreview
                :query-id="selectedQueryId"
                :params="paramValues"
                :page-size="previewPageSize"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 查询选择器样式 */
.query-selector {
  margin-bottom: 0;
  position: relative;
  isolation: isolate;
}

.select-container {
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
  min-height: 38px; /* 保持与其他输入框一致的高度 */
  height: 38px; /* 固定高度 */
  appearance: none;
  background-color: #ffffff;
  background-image: none !important; /* 移除默认的下拉箭头 */
  display: flex;
  align-items: center;
  padding: 0.45rem 0.75rem 0.45rem 0.75rem; /* 左右内边距 */
  padding-right: 8px; /* 确保右侧图标有足够空间 */
}

.select-container:hover {
  border-color: #9ca3af;
}

.select-container.active,
.select-container:focus-within {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 1px rgba(99, 102, 241, 0.25);
}

/* 下拉列表样式优化 */
.dropdown-container {
  position: absolute;
  width: 100%;
  background-color: #fff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-radius: 0.375rem; /* 与输入框保持一致的圆角 */
  max-height: 250px; /* 减小最大高度 */
  overflow-y: auto;
  z-index: 9999; /* 提高z-index确保在最上层 */
  margin-top: 2px;
  top: 100%;
  left: 0;
  border: 1px solid #d1d5db;
  overscroll-behavior: contain; /* 防止滚动传播 */
  scrollbar-width: thin; /* Firefox 细滚动条 */
}

/* 添加向上展开的下拉菜单 */
.dropdown-container.dropdown-up {
  bottom: 100%;
  top: auto;
  margin-top: 0;
  margin-bottom: 2px;
}

/* 为Webkit浏览器添加自定义滚动条样式 */
.dropdown-container::-webkit-scrollbar {
  width: 6px;
}

.dropdown-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.dropdown-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.dropdown-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.dropdown-item {
  padding: 8px 12px; /* 减小内边距使每项更紧凑 */
  border-bottom: 1px solid #f3f4f6;
  transition: all 0.2s ease;
  display: block;
  width: 100%;
  clear: both;
  text-align: left;
  cursor: pointer;
  user-select: none;
  background-color: transparent;
  color: #374151;
  font-size: 0.95rem;
}

.dropdown-item:hover {
  background-color: #f9fafb;
}

.dropdown-item.bg-indigo-50 {
  background-color: #eef2ff;
  color: #4f46e5;
  font-weight: 500;
}

.dropdown-item.bg-indigo-50:hover {
  background-color: #e0e7ff;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.item-title {
  font-weight: 500;
  margin-bottom: 2px; /* 减小标题下方间距 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; /* 确保标题不换行 */
}

.item-desc {
  white-space: nowrap; /* 确保描述不换行 */
  overflow: hidden;
  text-overflow: ellipsis;
}

.query-selector-container {
  position: relative;
  width: 100%;
  z-index: 30; /* 提高整体组件的z-index */
}

.query-input {
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.5rem center;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem !important;
}
</style>