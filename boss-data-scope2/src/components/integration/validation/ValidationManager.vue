<template>
  <div>
    <!-- 验证管理组件 - 不显示UI -->
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { validateIntegrationForm, formatValidationErrors } from '@/components/integration/container/utils/integrationValidation';
import type { IntegrationData, QueryParam } from '@/types/unified-integration';

// 定义组件属性
const props = defineProps<{
  integration: IntegrationData;
  queryParams: QueryParam[];
  queryConditionsPanelRef: any | null;
  validationErrors: Record<string, string>;
}>();

// 定义事件
const emit = defineEmits<{
  (e: 'validation-status-changed', isValid: boolean): void;
  (e: 'show-validation-errors', show: boolean): void;
  (e: 'update-validation-errors', errors: Record<string, string>): void;
}>();

// 本地状态
const showValidationErrors = ref(false);

// 方法：验证整个集成表单并提供用户反馈
const validateFormWithFeedback = async () => {
  console.log('[ValidationManager] 开始验证表单');
  // 显示验证错误，便于用户查看
  showValidationErrors.value = true;
  emit('show-validation-errors', true);
  
  try {
    // 验证表单数据
    const validateParamsFunc = () => {
      try {
        console.log('[ValidationManager] 开始执行查询条件验证');
        const result = props.queryConditionsPanelRef 
          ? props.queryConditionsPanelRef.validateParams()
          : true;
        console.log('[ValidationManager] 查询条件验证结果:', result);
        
        // 记录错误详情
        if (!result && props.validationErrors) {
          console.log('[ValidationManager] 验证错误详情:', JSON.stringify(props.validationErrors));
        }
        
        return result;
      } catch (error) {
        console.error('[ValidationManager] 查询条件验证出错:', error);
        return false;
      }
    };
    
    console.log('[ValidationManager] 当前集成数据:', {
      type: props.integration.type,
      hasName: !!props.integration.name,
      hasDataSource: !!props.integration.dataSourceId,
      hasQuery: !!props.integration.queryId,
      hasVersion: !!props.integration.versionId,
      queryParamsCount: props.queryParams.length,
      hasTableConfig: props.integration.type === 'TABLE' && 
                      !!props.integration.tableConfig && 
                      !!props.integration.tableConfig.columns &&
                      props.integration.tableConfig.columns.length > 0,
      hasChartConfig: props.integration.type === 'CHART' && 
                     !!props.integration.chartConfig
    });
    
    const isValid = validateIntegrationForm(
      props.integration,
      props.queryParams,
      props.validationErrors,
      validateParamsFunc
    );
    
    console.log('[ValidationManager] 验证结果:', isValid, '错误详情:', props.validationErrors);
    
    if (!isValid) {
      // 滚动到第一个错误字段位置
      setTimeout(() => {
        const firstErrorElement = document.querySelector('.text-red-600');
        if (firstErrorElement) {
          firstErrorElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }, 100);
      
      // 显示错误消息
      const { mainError, detailMessage } = formatValidationErrors(props.validationErrors);
      
      // 不再使用ElNotification显示错误，由SaveManager统一处理错误提示
      // 仅在控制台记录错误信息
      console.log('[ValidationManager] 验证失败，错误详情:', props.validationErrors);
      emit('validation-status-changed', false);
      return false;
    }
    
    console.log('[ValidationManager] 验证通过');
    emit('validation-status-changed', true);
    return true;
  } catch (error) {
    console.error('[ValidationManager] 验证过程出错:', error);
    emit('validation-status-changed', false);
    return false;
  }
};

// 清除所有验证错误
const clearValidationErrors = () => {
  // 使用空对象重置错误
  const clearedErrors: Record<string, string> = {};
  emit('update-validation-errors', clearedErrors);
};

// 更新验证错误
const updateValidationErrors = (errors: Record<string, string>) => {
  emit('update-validation-errors', errors);
};

// 隐藏验证错误
const hideValidationErrors = () => {
  showValidationErrors.value = false;
  emit('show-validation-errors', false);
};

// 显示验证错误
const showErrors = () => {
  showValidationErrors.value = true;
  emit('show-validation-errors', true);
};

// 获取验证错误
const getValidationErrors = () => {
  return props.validationErrors;
};

// 导出组件方法
defineExpose({
  validateFormWithFeedback,
  clearValidationErrors,
  updateValidationErrors,
  hideValidationErrors,
  showErrors,
  getValidationErrors
});
</script>