<template>
  <div v-if="isVisible" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center" style="z-index: 9998 !important;">
    <div class="bg-white rounded-lg shadow-xl max-w-lg w-full">
      <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
        <h3 class="text-lg font-medium text-gray-900">{{ editMode ? '编辑枚举' : '新建枚举' }}</h3>
        <button @click="close" class="text-gray-400 hover:text-gray-500 focus:outline-none">
          <i class="fas fa-times"></i>
        </button>
      </div>
      
      <div class="px-6 py-4">
        <div class="space-y-4">
          <!-- 基本信息 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">枚举名称 <span class="text-red-500">*</span></label>
            <input 
              v-model="enumName" 
              class="uniform-height-input shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
              placeholder="枚举名称"
              maxlength="50"
              @input="validateEnumName"
            />
            <div v-if="enumNameError" class="text-red-500 text-xs mt-1">{{ enumNameError }}</div>
            <div class="text-gray-400 text-xs mt-1">最多50个字符，建议使用有意义的名称</div>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">枚举编码 <span class="text-red-500">*</span></label>
            <input 
              v-model="enumCode" 
              class="uniform-height-input shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
              placeholder="枚举编码"
              maxlength="30"
              @input="validateEnumCode"
            />
            <div v-if="enumCodeError" class="text-red-500 text-xs mt-1">{{ enumCodeError }}</div>
            <div class="text-gray-400 text-xs mt-1">最多30个字符，仅允许字母、数字和下划线，不能以数字开头</div>
          </div>
          
          <!-- 选项列表 -->
          <div>
            <div class="flex justify-between items-center mb-2">
              <label class="block text-sm font-medium text-gray-700">选项列表</label>
              <button 
                @click="addOption" 
                class="px-2 py-1 text-xs bg-blue-50 text-blue-700 rounded hover:bg-blue-100"
                title="添加选项"
              >
                <i class="fas fa-plus mr-1"></i> 添加选项
              </button>
            </div>
            
            <div class="options-wrapper">
              <div class="options-header flex space-x-2 text-xs text-gray-500 mb-2 px-2">
                <div class="flex-1">选项值</div>
                <div class="flex-1">选项标签</div>
                <div class="w-8"></div>
              </div>
              
              <div class="options-list-wrapper max-h-60 overflow-y-auto border border-gray-200 rounded-md">
                <draggable 
                  v-model="options" 
                  tag="div"
                  handle=".drag-handle"
                  :animation="150"
                  item-key="index"
                  class="options-list"
                  @change="onOptionDragChange"
                >
                  <template #item="{element: option, index}">
                    <div class="flex space-x-2 p-2 hover:bg-gray-50 border-b border-gray-100 last:border-b-0 option-row">
                      <div class="drag-handle flex items-center justify-center mr-1 cursor-move text-gray-400 hover:text-gray-600" title="拖拽排序">
                        <i class="fas fa-grip-vertical"></i>
                      </div>
                      <input 
                        v-model="option.value" 
                        class="uniform-height-input flex-1 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm border-gray-300 rounded-md"
                        placeholder="选项值"
                      />
                      <input 
                        v-model="option.label" 
                        class="uniform-height-input flex-1 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm border-gray-300 rounded-md"
                        placeholder="选项标签"
                      />
                      <button 
                        @click="removeOption(index)" 
                        class="remove-option-btn w-8"
                        title="删除选项"
                      >
                        <i class="fas fa-times"></i>
                      </button>
                    </div>
                  </template>
                </draggable>
              </div>
              
              <!-- 空状态提示 -->
              <div v-if="options.length === 0" class="text-center text-gray-500 p-4 border border-gray-200 rounded-md">
                请添加至少一个选项
              </div>
              
              <div class="options-footer flex justify-between items-center mt-2 text-xs text-gray-500">
                <div>
                  <span v-if="options.length > 0">共 {{ options.length }} 个选项</span>
                </div>
                <div class="flex space-x-2">
                  <span v-if="options.length > 1" class="text-gray-500">
                    <i class="fas fa-info-circle mr-1"></i> 拖拽选项可排序
                  </span>
                  <button 
                    v-if="options.length > 1"
                    @click="sortOptions" 
                    class="text-blue-600 hover:text-blue-800"
                    title="按选项值排序"
                  >
                    <i class="fas fa-sort-alpha-down mr-1"></i> 按值排序
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end space-x-3">
        <button 
          @click="close" 
          class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none"
        >
          取消
        </button>
        <button 
          @click="save" 
          class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none transition-opacity duration-200"
          :class="{ 'opacity-50 cursor-not-allowed': !enumName || !enumCode || isSaving || options.length === 0 || !isOptionsValid }"
          :disabled="!enumName || !enumCode || isSaving || options.length === 0 || !isOptionsValid"
        >
          <i v-if="isSaving" class="fas fa-spinner fa-spin mr-1"></i>
          {{ isSaving ? '保存中...' : '保存' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { message } from '@/services/message';
import draggable from 'vuedraggable';
import { convertOptionsToEnumeration, createEnumeration, updateEnumeration } from '@/api/enumerationService';

// 定义组件属性
const props = defineProps<{
  isVisible: boolean;
  projectCode: string;
  paramName?: string;
  enumData?: any; // 用于编辑模式传入的枚举数据
}>();

const emit = defineEmits(['close', 'save', 'update:isVisible']);

// 定义选项类型
interface OptionItem {
  label: string;
  value: string;
}

// 表单状态
const enumName = ref('');
const enumCode = ref('');
const options = ref<OptionItem[]>([]);
const enumNameError = ref('');
const enumCodeError = ref('');
const isSaving = ref(false);

// 计算是否是编辑模式
const editMode = computed(() => !!props.enumData);

// 使用活跃的message消息服务

// 监听对话框显示状态
watch(() => props.isVisible, (newVal: boolean) => {
  if (newVal) {
    if (props.enumData) {
      // 编辑模式：加载传入的枚举数据
      loadEnumData();
    } else {
      // 新建模式：生成默认值
      generateDefaults();
    }
    
    // 如果没有选项，添加一个空选项
    if (options.value.length === 0) {
      addOption();
    }
  }
});

// 生成默认值
const generateDefaults = () => {
  // 基于参数名生成枚举名称和编码
  if (props.paramName) {
    enumName.value = props.paramName + '枚举';
    enumCode.value = props.paramName
      .replace(/\s+/g, '_')
      .replace(/[^\w\s]/g, '')
      .toUpperCase() + '_ENUM';
  } else {
    enumName.value = '';
    enumCode.value = '';
  }
  
  // 清除验证错误
  enumNameError.value = '';
  enumCodeError.value = '';
};

// 加载枚举数据
const loadEnumData = () => {
  if (!props.enumData) return;
  
  enumName.value = props.enumData.name || '';
  enumCode.value = props.enumData.code || '';
  
  // 将枚举内容转换为选项格式
  options.value = (props.enumData.content || []).map((item: {key: string, value: string, hide?: boolean}) => ({
    label: item.value,
    value: item.key
  }));
  
  // 清除验证错误
  enumNameError.value = '';
  enumCodeError.value = '';
};

// 验证枚举名称
const validateEnumName = () => {
  if (!enumName.value) {
    enumNameError.value = '枚举名称不能为空';
    return false;
  }
  if (enumName.value.length > 50) {
    enumNameError.value = '枚举名称不能超过50个字符';
    return false;
  }
  enumNameError.value = '';
  return true;
};

// 验证枚举代码
const validateEnumCode = () => {
  if (!enumCode.value) {
    enumCodeError.value = '枚举代码不能为空';
    return false;
  }
  if (enumCode.value.length > 50) {
    enumCodeError.value = '枚举代码不能超过50个字符';
    return false;
  }
  
  // 枚举代码只能包含字母、数字和下划线，且不能以数字开头
  const codeRegex = /^[a-zA-Z_][a-zA-Z0-9_]*$/;
  if (!codeRegex.test(enumCode.value)) {
    enumCodeError.value = '枚举代码只能包含字母、数字和下划线，且不能以数字开头';
    return false;
  }
  
  enumCodeError.value = '';
  return true;
};

// 添加新选项
const addOption = () => {
  options.value.push({
    label: '',
    value: ''
  });
};

// 移除选项
const removeOption = (index: number) => {
  options.value.splice(index, 1);
};

// 处理选项拖拽排序变化
const onOptionDragChange = () => {
  message.success('选项顺序已更新');
};

// 按选项值排序
const sortOptions = () => {
  options.value.sort((a: OptionItem, b: OptionItem) => {
    const valueA = (a.value || '').toString().toLowerCase();
    const valueB = (b.value || '').toString().toLowerCase();
    return valueA.localeCompare(valueB, undefined, { numeric: true, sensitivity: 'base' });
  });
  message.success('选项已按值排序');
};

// 检查选项是否全部有效
const isOptionsValid = computed(() => {
  return options.value.every((option: OptionItem) => 
    option.value?.trim() !== '' && option.label?.trim() !== ''
  );
});

// 关闭对话框
const close = () => {
  emit('update:isVisible', false);
  emit('close');
  // 重置表单
  resetForm();
};

// 重置表单
const resetForm = () => {
  enumName.value = '';
  enumCode.value = '';
  options.value = [];
  enumNameError.value = '';
  enumCodeError.value = '';
};

// 保存枚举
const save = async () => {
  try {
    // 标记为正在保存
    isSaving.value = true;
    
    // 验证表单
    const nameValid = validateEnumName();
    const codeValid = validateEnumCode();
    
    if (!nameValid || !codeValid) {
      isSaving.value = false;
      return;
    }
    
    // 验证选项
    if (options.value.length === 0) {
      message.error('无法保存枚举：请至少添加一个选项才能创建枚举');
      isSaving.value = false;
      return;
    }
    
    if (!isOptionsValid.value) {
      message.error('选项数据不完整：请确保所有选项的值和标签字段都已填写');
      isSaving.value = false;
      return;
    }
    
    // 将选项转换为枚举格式
    const enumData = convertOptionsToEnumeration(
      options.value,
      enumName.value,
      enumCode.value,
      props.projectCode
    );
    
    let response;
    
    if (editMode.value && props.enumData?.id) {
      // 编辑模式：调用更新接口
      response = await updateEnumeration({
        ...enumData,
        id: props.enumData.id
      });
      
      // 发送保存成功事件
      emit('save', {
        enumId: response.id || props.enumData.id,
        enumName: enumName.value,
        enumCode: enumCode.value,
        options: options.value
      });
      
      // 显示成功消息
      message.success(`枚举 "${enumName.value}" 更新成功`);
    } else {
      // 创建模式：调用创建接口
      response = await createEnumeration(enumData);
      
      // 发送保存成功事件
      emit('save', {
        enumId: response.id,
        enumName: enumName.value,
        enumCode: enumCode.value,
        options: options.value
      });
      
      // 显示成功消息
      message.success(`枚举 "${enumName.value}" 创建成功`);
    }
    
    // 关闭对话框
    close();
  } catch (error: any) {
    console.error('保存枚举失败:', error);
    
    // 友好的错误提示
    if (error.code === '230409') {
      message.error(`枚举代码 "${enumCode.value}" 已存在，请修改后重试`);
    } else {
      message.error(`保存枚举失败: ${error.message || '未知错误'}`);
    }
    
    isSaving.value = false;
  }
};
</script>

<style scoped>
.uniform-height-input {
  height: 38px;
  min-height: 38px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  line-height: 1.25;
}

.options-list .remove-option-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 38px;
  min-width: 38px;
  padding: 0 10px;
  border-radius: 0.375rem;
  font-size: 14px;
  background-color: #fee2e2;
  color: #ef4444;
  border: 1px solid #fecaca;
  transition: background-color 0.2s;
}

.options-list .remove-option-btn:hover {
  background-color: #fecaca;
}
</style>