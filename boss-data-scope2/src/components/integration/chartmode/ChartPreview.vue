<template>
  <div class="chart-preview">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-state" :style="{ height: `${height}px` }">
      <a-skeleton :rows="5" active />
    </div>

    <!-- 错误提示 -->
    <div v-else-if="error" class="error-state" :style="{ height: `${height}px` }">
      <a-alert
        :message="error"
        type="error"
        show-icon
      />
    </div>

    <!-- 空状态 -->
    <div v-else-if="isEmpty" class="empty-state" :style="{ height: `${height}px` }">
      <a-empty description="暂无数据" />
    </div>

    <!-- 图表 -->
    <div v-else class="chart-container" :style="{ height: `${height}px` }">
      <v-chart
        :option="chartOption"
        autoresize
      />
    </div>
  </div>
</template>

<script setup lang="ts">
// @ts-ignore - 为了解决Vue API导入问题
import { ref, computed, onMounted, watch } from 'vue';
import VChart from 'vue-echarts';
import type { EChartsOption } from 'echarts';
import { getApiBaseUrl } from '@/services/query';
import instance from '@/utils/axios';
import { getQueryApiUrl } from '@/services/apiUtils';
import { message } from 'ant-design-vue';
import * as echarts from 'echarts/core';

// 组件 props
interface Props {
  loading?: boolean;
  data?: any[];
  chartType: string;
  title?: string;
  description?: string;
  height?: number;
  showLegend?: boolean;
  animation?: boolean;
  xField?: string;
  yField?: string;
  valueField?: string;
  categoryField?: string;
  nameField?: string;
  seriesField?: string;
  sizeField?: string;
  colorField?: string;
  theme?: string;
  queryId?: string;
  versionId?: string;
  params?: Record<string, any>;
  dataUrl?: string;
  queryParams?: Record<string, string>;
  headers?: Record<string, string>;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  data: () => [],
  title: '',
  description: '',
  height: 400,
  showLegend: true,
  animation: true,
  xField: '',
  yField: '',
  valueField: '',
  categoryField: '',
  nameField: '',
  seriesField: '',
  sizeField: '',
  colorField: '',
  theme: 'default',
  versionId: '',
  params: () => ({}),
  dataUrl: '',
  queryParams: () => ({}),
  headers: () => ({})
});

// 状态变量
const localLoading = ref(false);
const error = ref<string | null>(null);
const chartData = ref<any[]>([]);
const isEmpty = computed(() => !chartData.value || chartData.value.length === 0);

// 更新数据方法
const updateData = () => {
  if (props.data && props.data.length > 0) {
    chartData.value = props.data;
    error.value = null;
  }
};

// 从服务器加载数据
const loadChartData = async () => {
  // 如果有提供数据，则直接使用
  if (props.data && props.data.length > 0) {
    chartData.value = props.data;
    return;
  }

  // 如果没有queryId和dataUrl，则无法加载数据
  if (!props.queryId && !props.dataUrl) {
    error.value = '未提供数据源，无法加载图表数据';
    return;
  }

  localLoading.value = true;
  error.value = null;

  try {
    // 检查是否提供了版本ID
    if (!props.versionId) {
      error.value = '缺少必要的版本ID(versionId)参数';
      console.error('图表加载失败: 缺少versionId参数');
      return;
    }

    // 使用getQueryApiUrl获取正确的API路径
    const apiUrl = getQueryApiUrl('execute-version', { id: props.queryId, versionId: props.versionId });
    console.log(`执行查询版本请求: ${apiUrl}`);

    // 使用instance.post替代fetch
    const response = await instance.post(apiUrl, {
      parameters: props.params || {}
    });

    const result = response.data;

    if (!result.data || !result.data.rows || !Array.isArray(result.data.rows)) {
      // 如果数据格式不符合预期，尝试直接使用data字段
      if (result.data && Array.isArray(result.data)) {
        chartData.value = result.data;
        return;
      }

      // 如果result本身是数组，直接使用
      if (Array.isArray(result)) {
        chartData.value = result;
        return;
      }

      throw new Error('返回的数据格式不正确，需要包含data.rows数组');
    }

    // 正常数据格式处理
    chartData.value = result.data.rows;
  } catch (err: unknown) {
    console.error('加载图表数据失败:', err);
    error.value = err instanceof Error ? err.message : '加载图表数据失败';
    chartData.value = [];
  } finally {
    localLoading.value = false;
  }
};

// 检查字段是否存在于数据中
const checkFieldExists = (field: string): boolean => {
  if (!field || !chartData.value || chartData.value.length === 0) return false;
  return chartData.value.some(item => field in item);
};

// 监听 queryId 和 params 变化，重新加载数据
watch([() => props.queryId, () => props.params], () => {
  loadChartData();
}, { immediate: true, deep: true });

// 监听直接提供的数据
watch(() => props.data, (newData: any[]) => {
  if (newData && newData.length > 0) {
    chartData.value = newData;
    error.value = null;
  }
}, { deep: true });

// 组件挂载时加载数据
onMounted(() => {
  if (props.dataUrl) {
    fetchData();
  }
});

// 重新加载数据的方法（可在父组件中调用）
const reloadData = () => {
  loadChartData();
};

// 导出方法给父组件调用
defineExpose({
  reloadData,
  updateData
});

// 图表配置
const chartOption = computed<EChartsOption>(() => {
  const option: EChartsOption = {
    title: {
      text: props.title,
      subtext: props.description,
      left: 'center'
    },
    tooltip: {
      trigger: props.chartType === 'pie' ? 'item' : 'axis',
      axisPointer: {
        type: props.chartType === 'scatter' ? 'cross' : 'shadow'
      }
    },
    legend: {
      show: props.showLegend,
      bottom: '0%',
      left: 'center'
    },
    animation: props.animation,
    grid: {
      left: '3%',
      right: '4%',
      bottom: '12%',
      top: props.title ? '15%' : '8%',
      containLabel: true
    }
  };

  // 确保图表数据非空
  if (isEmpty.value) {
    return option;
  }

  // 根据图表类型生成不同的配置
  try {
    const chartTypeFormatted = props.chartType.toLowerCase();
    const isAreaChart = chartTypeFormatted === 'area';

    // 将 area 类型当作 line 类型处理，但添加 areaStyle
    const actualType = isAreaChart ? 'line' : chartTypeFormatted as 'bar' | 'line' | 'pie' | 'scatter';

    switch (actualType) {
      case 'bar':
      case 'line': {
        if (!props.xField || !checkFieldExists(props.xField)) {
          error.value = `X轴字段 "${props.xField}" 不存在于数据中`;
          console.error(error.value, '可用字段：', Object.keys(chartData.value[0] || {}));
          return option;
        }

        if (!props.yField || !checkFieldExists(props.yField)) {
          error.value = `Y轴字段 "${props.yField}" 不存在于数据中`;
          console.error(error.value, '可用字段：', Object.keys(chartData.value[0] || {}));
          return option;
        }

        option.xAxis = {
          type: 'category',
          data: chartData.value.map(item => item[props.xField] || 'N/A')
        };
        option.yAxis = {
          type: 'value'
        };
        option.series = [{
          name: props.yField,
          type: actualType,
          data: chartData.value.map(item => item[props.yField] || 0),
          ...(actualType === 'bar' ? {
            itemStyle: {
              borderRadius: 4
            }
          } : {}),
          ...(actualType === 'line' ? {
            smooth: true,
            ...(isAreaChart ? {areaStyle: {}} : {})
          } : {})
        }];
        break;
      }
      case 'pie': {
        // 检查值字段
        if (!props.valueField || !checkFieldExists(props.valueField)) {
          error.value = `值字段 "${props.valueField}" 不存在于数据中`;
          console.error(error.value, '可用字段：', Object.keys(chartData.value[0] || {}));
          return option;
        }

        // 检查名称字段 - 优先使用nameField，其次使用categoryField
        const nameField = props.nameField || props.categoryField;
        if (!nameField || !checkFieldExists(nameField)) {
          error.value = `名称字段 "${nameField}" 不存在于数据中`;
          console.error(error.value, '可用字段：', Object.keys(chartData.value[0] || {}));
          return option;
        }

        option.series = [{
          name: props.title || '数据',
          type: 'pie',
          radius: '50%',
          center: ['50%', '50%'],
          data: chartData.value.map(item => ({
            name: item[nameField] || 'N/A',
            value: item[props.valueField] || 0
          })),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          label: {
            formatter: '{b}: {c} ({d}%)'
          }
        }];
        break;
      }
      case 'scatter': {
        if (!props.xField || !checkFieldExists(props.xField)) {
          error.value = `X轴字段 "${props.xField}" 不存在于数据中`;
          console.error(error.value, '可用字段：', Object.keys(chartData.value[0] || {}));
          return option;
        }

        if (!props.yField || !checkFieldExists(props.yField)) {
          error.value = `Y轴字段 "${props.yField}" 不存在于数据中`;
          console.error(error.value, '可用字段：', Object.keys(chartData.value[0] || {}));
          return option;
        }

        // 设置X轴和Y轴为值类型而非类目类型
        option.xAxis = {
          type: 'value',
          name: props.xField,
          nameLocation: 'middle',
          nameGap: 30,
          scale: true
        };
        option.yAxis = {
          type: 'value',
          name: props.yField,
          nameLocation: 'middle',
          nameGap: 30,
          scale: true
        };

        // 检查是否有系列字段
        if (props.seriesField && checkFieldExists(props.seriesField)) {
          // 按系列字段分组
          const seriesGroups = new Map<any, any[]>();
          chartData.value.forEach(item => {
            const seriesValue = item[props.seriesField as string];
            if (!seriesGroups.has(seriesValue)) {
              seriesGroups.set(seriesValue, []);
            }
            seriesGroups.get(seriesValue)?.push(item);
          });

          // 生成多系列散点图
          option.series = Array.from(seriesGroups.entries()).map(([seriesName, items]) => ({
            name: seriesName || '未分组',
            type: 'scatter',
            data: items.map(item => [
              item[props.xField] || 0,
              item[props.yField] || 0,
              // 如果有大小映射字段
              props.sizeField && checkFieldExists(props.sizeField) ?
                item[props.sizeField] :
                undefined
            ]),
            symbolSize: (data: any) => {
              // 如果有第三维度数据作为大小映射
              if (data.length > 2 && data[2] !== undefined) {
                // 将大小值映射到合理的符号大小范围 (8-25)
                return 8 + (data[2] / 100) * 17;
              }
              return 12; // 默认大小
            }
          }));
        } else {
          // 单系列散点图
          option.series = [{
            type: 'scatter',
            name: props.title || '数据点',
            data: chartData.value.map(item => [
              item[props.xField] || 0,
              item[props.yField] || 0,
              // 如果有大小映射字段
              props.sizeField && checkFieldExists(props.sizeField) ?
                item[props.sizeField] :
                undefined
            ]),
            symbolSize: (data: any) => {
              // 如果有第三维度数据作为大小映射
              if (data.length > 2 && data[2] !== undefined) {
                // 将大小值映射到合理的符号大小范围 (8-25)
                return 8 + (data[2] / 100) * 17;
              }
              return 12; // 默认大小
            }
          }];
        }
        break;
      }
      default: {
        error.value = `不支持的图表类型: ${props.chartType}`;
        console.error(error.value);
        return option;
      }
    }
  } catch (err: unknown) {
    console.error('图表配置生成错误:', err);
    error.value = `图表配置生成错误: ${(err as Error).message || '未知错误'}`;
  }

  return option;
});

// 从URL获取数据的方法
const fetchData = async () => {
  if (!props.dataUrl) {
    error.value = '未提供数据URL，无法加载数据';
    return;
  }

  localLoading.value = true;
  error.value = null;

  try {
    // 构建完整URL，添加查询参数
    let url = props.dataUrl;
    if (props.queryParams && Object.keys(props.queryParams).length > 0) {
      const params = new URLSearchParams();
      for (const [key, value] of Object.entries(props.queryParams)) {
        params.append(key, value);
      }
      url += `?${params.toString()}`;
    }

    // 使用instance.get替代fetch，添加自定义headers
    const response = await instance.get(url, {
      headers: props.headers
    });

    const result = response.data;

    // 处理不同的响应格式
    if (Array.isArray(result)) {
      chartData.value = result;
    } else if (result.data && Array.isArray(result.data)) {
      chartData.value = result.data;
    } else if (result.data && result.data.rows && Array.isArray(result.data.rows)) {
      chartData.value = result.data.rows;
    } else {
      throw new Error('返回的数据格式不正确，无法解析');
    }
  } catch (err: unknown) {
    console.error('从URL加载数据失败:', err);
    error.value = err instanceof Error ? err.message : '加载数据失败';
    chartData.value = [];
  } finally {
    localLoading.value = false;
  }
};
</script>

<style scoped>
/* 添加一些样式代码 */
</style>
