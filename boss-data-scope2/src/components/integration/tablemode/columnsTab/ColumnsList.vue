<template>
  <div class="columns-list">
    <div class="columns-list-header">
      <h3>列配置</h3>
      <a-button type="primary" @click="$emit('add')">
        <template #icon><PlusOutlined /></template>
        添加列
      </a-button>
    </div>
    
    <div class="columns-list-content">
      <a-empty 
        v-if="columns.length === 0 && !loading" 
        description="暂无列配置"
      >
        <template #extra>
          <a-button type="primary" @click="$emit('add')">
            添加第一列
          </a-button>
        </template>
      </a-empty>
      
      <a-table
        v-else
        :columns="tableColumns"
        :data-source="columns"
        :pagination="false"
        :row-key="record => record.id"
        bordered
        size="small"
        :loading="loading"
        :customRow="customRow"
      >
        <template #bodyCell="{ column, record }: { column: any; record: TableColumn }">
          <template v-if="column.key === 'drag'">
            <MenuOutlined class="drag-handle" />
          </template>
          
          <template v-if="column.key === 'visible'">
            <a-switch 
              :checked="record.visible" 
              @change="(checked: boolean) => $emit('toggle-visibility', record, checked)" 
            />
          </template>
          
          <template v-if="column.key === 'displayType'">
            <a-tag :color="getDisplayTypeColor(record.displayType)">
              {{ formatDisplayType(record.displayType) }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'actions'">
            <div class="column-actions">
              <a-tooltip title="编辑">
                <EditOutlined class="action-icon" @click="$emit('edit', record)" />
              </a-tooltip>
              <a-tooltip title="删除">
                <a-popconfirm
                  title="确定要删除此列吗？"
                  ok-text="是"
                  cancel-text="否"
                  @confirm="$emit('delete', record)"
                >
                  <DeleteOutlined class="action-icon delete-icon" />
                </a-popconfirm>
              </a-tooltip>
            </div>
          </template>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted } from 'vue';
import { PlusOutlined, EditOutlined, DeleteOutlined, MenuOutlined } from '@ant-design/icons-vue';
import type { TableColumn } from '@/types/integration';
// @ts-ignore
import Sortable from 'sortablejs';

// 定义组件属性
const props = defineProps<{
  columns: TableColumn[];
  loading: boolean;
}>();

// 定义事件
const emit = defineEmits<{
  (e: 'add'): void;
  (e: 'edit', column: TableColumn): void;
  (e: 'delete', column: TableColumn): void;
  (e: 'reorder', columns: TableColumn[]): void;
  (e: 'toggle-visibility', column: TableColumn, visible: boolean): void;
}>();

// 列表列定义
const tableColumns = computed(() => [
  {
    title: '',
    key: 'drag',
    width: 50,
    align: 'center' as const,
  },
  {
    title: '字段名',
    dataIndex: 'field',
    key: 'field',
    width: '25%',
  },
  {
    title: '显示名称',
    dataIndex: 'label',
    key: 'label',
    width: '25%',
  },
  {
    title: '类型',
    dataIndex: 'displayType',
    key: 'displayType',
    width: '15%',
  },
  {
    title: '可见性',
    dataIndex: 'visible',
    key: 'visible',
    width: '15%',
    align: 'center' as const,
  },
  {
    title: '操作',
    key: 'actions',
    width: '15%',
    align: 'center' as const,
  },
]);

// 格式化显示类型
const formatDisplayType = (type: string): string => {
  const typeMap: Record<string, string> = {
    text: '文本',
    number: '数字',
    datetime: '日期时间',
    date: '日期',
    boolean: '布尔值',
    select: '下拉选择',
    tags: '标签',
    image: '图片',
    file: '文件',
    link: '链接',
  };
  
  return typeMap[type] || type;
};

// 获取显示类型对应的颜色
const getDisplayTypeColor = (type: string): string => {
  const colorMap: Record<string, string> = {
    text: 'blue',
    number: 'purple',
    datetime: 'orange',
    date: 'gold',
    boolean: 'green',
    select: 'cyan',
    tags: 'magenta',
    image: 'volcano',
    file: 'geekblue',
    link: 'lime',
  };
  
  return colorMap[type] || 'default';
};

// 拖拽排序设置
let sortable: any = null;

onMounted(() => {
  const el = document.querySelector('.ant-table-tbody');
  if (el) {
    sortable = new Sortable(el, {
      handle: '.drag-handle',
      animation: 150,
      ghostClass: 'sortable-ghost',
      onEnd: (evt: any) => {
        // 发出重新排序事件
        const { oldIndex, newIndex } = evt;
        if (oldIndex !== newIndex && oldIndex !== undefined && newIndex !== undefined) {
          const reorderedColumns = [...props.columns];
          
          // 移动元素
          const itemToMove = reorderedColumns.splice(oldIndex, 1)[0];
          reorderedColumns.splice(newIndex, 0, itemToMove);
          
          // 发出事件
          emit('reorder', reorderedColumns);
        }
      }
    });
  }
});

onUnmounted(() => {
  if (sortable) {
    sortable.destroy();
    sortable = null;
  }
});

// 自定义行属性
const customRow = (record: TableColumn) => {
  return {
    class: 'draggable-row',
  };
};
</script>

<style scoped>
.columns-list {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.columns-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.columns-list-header h3 {
  margin: 0;
}

.columns-list-content {
  flex: 1;
  overflow: auto;
}

.column-actions {
  display: flex;
  justify-content: center;
  align-items: center;
}

.action-icon {
  font-size: 16px;
  cursor: pointer;
  color: #1890ff;
  margin: 0 4px;
}

.delete-icon {
  color: #ff4d4f;
}

.drag-handle {
  cursor: move;
  color: #999;
}

.drag-handle:hover {
  color: #1890ff;
}

:deep(.draggable-row) {
  cursor: default;
}

:deep(.sortable-ghost) {
  background-color: #f0f7ff;
  opacity: 0.8;
}
</style>