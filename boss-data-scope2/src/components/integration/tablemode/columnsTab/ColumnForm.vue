<template>
  <div class="column-form">
    <div class="form-header">
      <h3>{{ isEdit ? '编辑列' : '添加列' }}</h3>
      <a-button @click="$emit('cancel')">
        <template #icon><CloseOutlined /></template>
      </a-button>
    </div>

    <a-form
      :model="formState"
      layout="vertical"
      @finish="onSubmit"
      :validate-status="validateInfos"
    >
      <a-form-item
        name="field"
        label="字段名称"
        v-bind="validateInfos.field"
      >
        <a-input
          v-model:value="formState.field"
          placeholder="请输入字段名称"
          :disabled="isEdit && !allowFieldEdit"
        />
      </a-form-item>

      <a-form-item
        name="label"
        label="显示名称"
        v-bind="validateInfos.label"
      >
        <a-input
          v-model:value="formState.label"
          placeholder="请输入显示名称"
        />
      </a-form-item>

      <a-form-item
        name="displayType"
        label="显示类型"
        v-bind="validateInfos.displayType"
      >
        <a-select
          v-model:value="formState.displayType"
          placeholder="请选择显示类型"
          :options="displayTypeOptions"
        />
      </a-form-item>

      <a-form-item
        name="description"
        label="描述"
      >
        <a-textarea
          v-model:value="formState.description"
          placeholder="请输入描述信息"
          :rows="3"
        />
      </a-form-item>

      <!-- 增加对齐方式配置 -->
      <a-form-item
        name="align"
        label="对齐方式"
      >
        <a-select
          v-model:value="formState.align"
          placeholder="请选择对齐方式"
          :options="alignOptions"
        />
      </a-form-item>

      <!-- 增加宽度配置 -->
      <a-form-item
        name="width"
        label="列宽度"
      >
        <a-input-number
          v-model:value="formState.width"
          placeholder="请输入宽度"
          :min="50"
          :max="500"
          style="width: 100%"
          addonAfter="px"
        />
      </a-form-item>

      <a-row :gutter="16">
        <!-- 增加可见性、排序和筛选选项 -->
        <a-col :span="8">
          <a-form-item
            name="visible"
            label="可见性"
          >
            <a-switch v-model:checked="formState.visible" />
          </a-form-item>
        </a-col>

        <a-col :span="8">
          <a-form-item
            name="sortable"
            label="可排序"
          >
            <a-switch v-model:checked="formState.sortable" />
          </a-form-item>
        </a-col>

        <a-col :span="8">
          <a-form-item
            name="filterable"
            label="可筛选"
          >
            <a-switch v-model:checked="formState.filterable" />
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 条件渲染特定类型的附加属性 -->
      <template v-if="formState.displayType === 'select' || formState.displayType === 'tags'">
        <a-divider>选项配置</a-divider>

        <div class="enum-config-section">
          <a-form-item
            name="enumConfig"
            label="选项来源"
          >
            <a-radio-group v-model:value="optionSource">
              <a-radio value="custom">自定义选项</a-radio>
              <a-radio value="enum">从枚举获取</a-radio>
            </a-radio-group>
          </a-form-item>

          <!-- 枚举选择区域 -->
          <div v-if="optionSource === 'enum'" class="enum-selector-area">
            <div class="enum-info">
              <template v-if="formState.enumId && formState.enumName">
                <div class="selected-enum">
                  <div class="enum-details">
                    <span class="enum-name">{{ formState.enumName }}</span>
                    <span class="enum-code">({{ formState.enumCode }})</span>
                  </div>
                  <a-button type="link" @click="$emit('open-enum-config')">
                    <template #icon><SettingOutlined /></template>
                    配置
                  </a-button>
                </div>
              </template>
              <a-button
                v-else
                type="primary"
                @click="$emit('open-enum-selector')"
              >
                <template #icon><LinkOutlined /></template>
                选择枚举
              </a-button>
            </div>
          </div>

          <!-- 自定义选项区域 -->
          <div v-if="optionSource === 'custom'" class="custom-options-area">
            <a-form-item
              name="options"
              label="选项列表"
            >
              <a-space
                direction="vertical"
                style="width: 100%"
                v-if="formState.options && formState.options.length > 0"
              >
                <a-row
                  v-for="(option, index) in formState.options"
                  :key="index"
                  :gutter="8"
                  style="margin-bottom: 8px;"
                >
                  <a-col :span="11">
                    <a-input
                      v-model:value="option.value"
                      placeholder="选项值"
                    />
                  </a-col>
                  <a-col :span="11">
                    <a-input
                      v-model:value="option.label"
                      placeholder="选项标签"
                    />
                  </a-col>
                  <a-col :span="2">
                    <a-button
                      danger
                      type="text"
                      @click="removeOption(index)"
                    >
                      <DeleteOutlined />
                    </a-button>
                  </a-col>
                </a-row>
              </a-space>

              <a-button
                type="dashed"
                block
                @click="addOption"
                style="margin-top: 8px;"
              >
                <PlusOutlined /> 添加选项
              </a-button>
            </a-form-item>
          </div>
        </div>
      </template>

      <!-- 日期时间类型的格式配置 -->
      <template v-if="formState.displayType === 'date' || formState.displayType === 'datetime'">
        <a-divider>格式设置</a-divider>
        <a-form-item
          name="format"
          label="日期格式"
        >
          <a-select
            v-model:value="formState.format"
            placeholder="请选择日期格式"
            :options="dateFormatOptions"
          />
        </a-form-item>
      </template>

      <!-- 数字类型的格式配置 -->
      <template v-if="formState.displayType === 'number'">
        <a-divider>数字格式</a-divider>
        <a-form-item
          name="format"
          label="数字格式"
        >
          <a-select
            v-model:value="formState.format"
            placeholder="请选择数字格式"
            :options="numberFormatOptions"
          />
        </a-form-item>
      </template>

      <!-- 链接类型的配置 -->
      <template v-if="formState.displayType === 'link'">
        <a-divider>链接设置</a-divider>
        <a-form-item
          name="urlTemplate"
          label="URL模板"
        >
          <a-input
            v-model:value="formState.urlTemplate"
            placeholder="例如: https://example.com/{id}"
          />
          <div class="text-sm text-gray-500 mt-1">
            使用 {fieldName} 语法引用其他字段值
          </div>
        </a-form-item>

        <a-form-item
          name="linkText"
          label="链接文本"
        >
          <a-input
            v-model:value="formState.linkText"
            placeholder="例如: 查看详情 或 {name}"
          />
        </a-form-item>
      </template>

      <div class="form-actions">
        <a-space>
          <a-button type="primary" html-type="submit" :loading="saving">
            {{ isEdit ? '保存' : '添加' }}
          </a-button>
          <a-button @click="$emit('cancel')">取消</a-button>
        </a-space>
      </div>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import { reactive, computed, watch, ref } from 'vue';
import { PlusOutlined, DeleteOutlined, CloseOutlined, SettingOutlined, LinkOutlined } from '@ant-design/icons-vue';
import { Form } from 'ant-design-vue';
import type { TableColumn, SelectOption } from '@/types/integration';
import { ColumnAlign } from '@/types/integration';

const useForm = Form.useForm;

// Props & Emits
const props = defineProps<{
  column?: TableColumn;
  existingFields?: string[];
  allowFieldEdit?: boolean;
  saving?: boolean;
}>();

const emit = defineEmits<{
  (e: 'save', column: TableColumn): void;
  (e: 'cancel'): void;
  (e: 'open-enum-selector'): void;
  (e: 'open-enum-config'): void;
}>();

// 选项来源控制
const optionSource = ref('custom');

// 判断是否为编辑模式
const isEdit = computed(() => !!props.column);

// 表单状态
const formState = reactive<TableColumn>({
  id: '',
  field: '',
  label: '',
  displayType: 'text',
  description: '',
  visible: true,
  align: ColumnAlign.LEFT,
  width: 150,
  sortable: false,
  filterable: false,
  options: [],
  format: ''
});

// 表单验证规则
const rules = {
  field: [
    { required: true, message: '请输入字段名称', trigger: 'blur' },
    { min: 2, message: '字段名称至少2个字符', trigger: 'blur' },
    {
      validator: (rule: any, value: string) => {
        if (!value || isEdit.value) {
          return Promise.resolve();
        }

        // 字段名唯一性验证
        const isDuplicate = props.existingFields?.includes(value);
        return isDuplicate
          ? Promise.reject(new Error('字段名已存在'))
          : Promise.resolve();
      },
      trigger: 'blur'
    }
  ],
  label: [
    { required: true, message: '请输入显示名称', trigger: 'blur' },
  ],
  displayType: [
    { required: true, message: '请选择显示类型', trigger: 'change' },
  ],
};

// 表单验证和提交
const { resetFields, validate, validateInfos } = useForm(formState, rules);

// 显示类型选项
const displayTypeOptions = computed(() => [
  { value: 'text', label: '文本' },
  { value: 'number', label: '数字' },
  { value: 'datetime', label: '日期时间' },
  { value: 'date', label: '日期' },
  { value: 'boolean', label: '布尔值' },
  { value: 'select', label: '下拉选择' },
  { value: 'tags', label: '标签' },
  { value: 'image', label: '图片' },
  { value: 'file', label: '文件' },
  { value: 'link', label: '链接' },
]);

// 对齐方式选项
const alignOptions = computed(() => [
  { value: 'left', label: '左对齐' },
  { value: 'center', label: '居中' },
  { value: 'right', label: '右对齐' },
]);

// 日期格式选项
const dateFormatOptions = computed(() => [
  { value: 'YYYY-MM-DD', label: 'YYYY-MM-DD' },
  { value: 'YYYY/MM/DD', label: 'YYYY/MM/DD' },
  { value: 'DD/MM/YYYY', label: 'DD/MM/YYYY' },
  { value: 'YYYY-MM-DD HH:mm:ss', label: 'YYYY-MM-DD HH:mm:ss' },
  { value: 'YYYY年MM月DD日', label: 'YYYY年MM月DD日' },
  { value: 'MM-DD', label: 'MM-DD' },
  { value: 'HH:mm:ss', label: 'HH:mm:ss' },
]);

// 数字格式选项
const numberFormatOptions = computed(() => [
  { value: 'default', label: '默认' },
  { value: 'currency', label: '货币 (¥)' },
  { value: 'percent', label: '百分比 (%)' },
  { value: 'scientific', label: '科学计数法' },
  { value: 'decimal', label: '小数 (0.00)' },
  { value: 'integer', label: '整数' },
]);

// 初始化表单
watch(() => props.column, (newColumn) => {
  if (newColumn) {
    Object.assign(formState, {
      ...newColumn,
      options: newColumn.options ? [...newColumn.options] : [],
    });

    // 根据已有配置设置选项来源
    optionSource.value = newColumn.enumId ? 'enum' : 'custom';
  } else {
    // 重置表单
    Object.assign(formState, {
      id: '',
      field: '',
      label: '',
      displayType: 'text',
      description: '',
      visible: true,
      align: ColumnAlign.LEFT,
      width: 150,
      sortable: false,
      filterable: false,
      options: [],
      format: '',
    });

    // 重置选项来源
    optionSource.value = 'custom';
  }
}, { immediate: true });

// 添加选项
const addOption = () => {
  if (!formState.options) {
    formState.options = [];
  }
  formState.options.push({ value: '', label: '' });
};

// 移除选项
const removeOption = (index: number) => {
  if (formState.options) {
    formState.options.splice(index, 1);
  }
};

// 提交表单
const onSubmit = () => {
  validate()
    .then(() => {
      // 为新列生成ID
      if (!isEdit.value) {
        formState.id = `col_${Date.now()}`;
      }

      // 对选项进行处理
      if (optionSource.value === 'custom') {
        // 使用自定义选项时，清空枚举配置
        formState.enumId = undefined;
        formState.enumCode = undefined;
        formState.enumName = undefined;
        formState.enumDisplay = undefined;

        // 移除空选项
        if (formState.options) {
          formState.options = formState.options.filter(opt => opt.value.trim() !== '' || opt.label.trim() !== '');
        }
      } else {
        // 使用枚举时，清空自定义选项
        formState.options = [];
      }

      // 如果不是select或tags类型，则清空所有选项和枚举配置
      if (formState.displayType !== 'select' && formState.displayType !== 'tags') {
        formState.options = [];
        formState.enumId = undefined;
        formState.enumCode = undefined;
        formState.enumName = undefined;
        formState.enumDisplay = undefined;
      }

      emit('save', { ...formState });
    })
    .catch((err: Error) => {
      console.error('验证失败:', err);
    });
};
</script>

<style scoped>
.column-form {
  padding: 16px;
  width: 100%;
  max-width: 720px;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.form-header h3 {
  margin: 0;
}

.enum-selector-area,
.custom-options-area {
  margin-top: 16px;
}

.selected-enum {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background-color: #f5f5f5;
  border-radius: 4px;
  margin-bottom: 16px;
}

.enum-details {
  display: flex;
  flex-direction: column;
}

.enum-name {
  font-weight: 500;
  font-size: 14px;
}

.enum-code {
  font-size: 12px;
  color: #666;
}

.text-sm {
  font-size: 12px;
}

.text-gray-500 {
  color: #6b7280;
}

.mt-1 {
  margin-top: 4px;
}

.form-actions {
  margin-top: 24px;
  display: flex;
  justify-content: flex-end;
}
</style>
