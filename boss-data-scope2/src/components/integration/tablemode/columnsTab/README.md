# 表格列配置组件

这个目录包含了用于配置表格列的组件，包括列表、表单和主组件。

## 组件目录

- `ColumnsTab.vue`: 主组件，用于集成列表和表单
- `ColumnsList.vue`: 列表组件，用于显示和管理列
- `ColumnForm.vue`: 表单组件，用于添加和编辑列
- `index.ts`: 导出所有组件

## 功能特点

- 支持列的添加、编辑、删除和重新排序
- 支持设置列的显示名称、显示类型和可见性
- 支持拖拽排序列的顺序
- 支持为选择类型的列添加选项

## 使用示例

```vue
<template>
  <ColumnsTab
    :integration-id="integrationId"
    :data-source="dataSource"
  />
</template>

<script setup lang="ts">
import { ColumnsTab } from '@/components/integration/tablemode/columnsTab';
</script>
```

## 类型说明

组件使用`@/types/integration.ts`中定义的`TableColumn`类型：

```typescript
export interface TableColumn {
  id: string;
  field: string;
  label: string;
  displayType: DisplayType;
  description?: string;
  visible: boolean;
  options?: SelectOption[];
}

export type DisplayType = 
  | 'text'
  | 'number'
  | 'date'
  | 'datetime'
  | 'boolean'
  | 'select'
  | 'tags'
  | 'image'
  | 'file'
  | 'link';

export interface SelectOption {
  value: string;
  label: string;
}
```