<template>
  <!-- 加载状态 -->
  <div v-if="isLoading" class="loading-container">
    <a-spin size="large" tip="数据加载中..." />
  </div>

  <!-- 错误提示 -->
  <div v-else-if="error" class="error-message">
    <a-alert type="error" :message="error" show-icon />
  </div>

  <!-- 结果展示区域 -->
  <div v-else class="result-container">
    <!-- 图表视图 -->
    <ChartView
      v-if="type === 'CHART' && data.length > 0"
      :config="config"
      :data="data"
      @refresh="$emit('refresh')"
    />

    <!-- 表格视图 -->
    <TableView
      v-else-if="(type === 'TABLE' || type === 'SIMPLE_TABLE') && data.length > 0"
      :columns="columns"
      :data="data"
      :pagination="pagination"
      :title="title"
      :export-config="fullExportConfig"
      @refresh="$emit('refresh')"
    />

    <!-- 无数据提示 -->
    <div v-else class="empty-container">
      <a-empty description="暂无数据，请修改查询条件后重试">
        <a-button type="primary" @click="$emit('refresh')">刷新数据</a-button>
      </a-empty>
    </div>
  </div>
</template>

<script setup lang="ts">
// defineProps和defineEmits是编译器宏，不需要导入
import { computed } from 'vue';
import ChartView from '@/components/integration/preview/ChartView.vue';
import TableView from '@/components/integration/preview/TableView.vue';

const props = defineProps({
  type: {
    type: String,
    default: ''
  },
  data: {
    type: Array,
    default: () => []
  },
  columns: {
    type: Array,
    default: () => []
  },
  pagination: {
    type: Object,
    default: () => ({})
  },
  config: {
    type: Object,
    default: () => ({})
  },
  title: {
    type: String,
    default: ''
  },
  exportConfig: {
    type: Object,
    default: () => ({ enabled: true, formats: ['Excel', 'CSV'] })
  },
  isLoading: {
    type: Boolean,
    default: false
  },
  error: {
    type: String,
    default: null
  }
});

defineEmits(['refresh']);

// 确保导出配置包含所有必要字段
const fullExportConfig = computed(() => {
  return {
    enabled: props.exportConfig.enabled !== undefined ? props.exportConfig.enabled : true,
    formats: props.exportConfig.formats || ['Excel', 'CSV'],
    maxRows: props.exportConfig.maxRows || 1000
  };
});
</script>

<style scoped>
.loading-container,
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.error-message {
  margin-bottom: 16px;
}

.result-container {
  display: flex;
  flex-direction: column;
  flex: 1;
}
</style>