<template>
  <div class="bg-white shadow rounded-lg overflow-hidden">
    <div class="px-4 py-5 bg-gray-50 border-b border-gray-200 text-sm font-medium text-gray-900">
      基本信息
    </div>
    <div class="p-6">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="flex flex-col gap-1">
          <label class="text-sm font-medium text-gray-700">
            名称 <span class="text-red-500">*</span>
          </label>
          <input
            v-model="integration.name"
            type="text"
            class="border rounded-md p-2 w-full"
            :class="{ 'border-red-500': showValidationErrors && validationErrors['name'] }"
            placeholder="输入集成名称"
          />
          <span v-if="showValidationErrors && validationErrors['name']" class="text-red-600 text-sm">
            {{ validationErrors['name'] }}
          </span>
        </div>

        <div class="flex flex-col gap-1">
          <label class="text-sm font-medium text-gray-700">描述</label>
          <input
            v-model="integration.description"
            type="text"
            class="border rounded-md p-2 w-full"
            placeholder="输入集成描述（选填）"
          />
        </div>

        <div class="flex flex-col gap-1">
          <label class="text-sm font-medium text-gray-700">
            数据源 <span class="text-red-500">*</span>
          </label>
          <DataSourceSelector
            :model-value="integration.dataSourceId"
            :required="true"
            @selected="handleDataSourceSelected"
            :class="{ 'error-border': showValidationErrors && validationErrors['dataSourceId'] }"
          />
          <span v-if="showValidationErrors && validationErrors['dataSourceId']" class="text-red-600 text-sm">
            {{ validationErrors['dataSourceId'] }}
          </span>
        </div>

        <div class="flex flex-col gap-1">
          <label class="text-sm font-medium text-gray-700">
            数据查询 <span class="text-red-500">*</span>
          </label>
          <QuerySelector
            :model-value="integration.queryId"
            :data-source-id="integration.dataSourceId"
            @change="handleQueryChange"
            @selected="handleQuerySelected"
            :class="{ 'error-border': showValidationErrors && validationErrors['queryId'] }"
          />
          <span v-if="showValidationErrors && validationErrors['queryId']" class="text-red-600 text-sm">
            {{ validationErrors['queryId'] }}
          </span>
        </div>

        <!-- 查询版本 -->
        <div class="flex flex-col gap-1">
          <QueryVersionSelector
            v-model="integration.versionId"
            :query-id="integration.queryId"
            label="查询版本"
            :required="true"
            :error="showValidationErrors && validationErrors['versionId'] ? validationErrors['versionId'] : ''"
            @selected="handleVersionChange"
          />
        </div>
      </div>

      <div class="mt-6">
        <label class="block text-sm font-medium text-gray-700 mb-2">集成类型</label>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div
            v-for="type in integrationTypes"
            :key="type.value"
            class="relative rounded-lg border p-4 cursor-pointer transition-all duration-200 ease-in-out"
            :class="[
              integration.type === type.value
                ? 'border-indigo-500 bg-indigo-50 shadow-md transform scale-[1.02]'
                : type.disabled
                  ? 'border-gray-200 bg-gray-50 opacity-60 cursor-not-allowed'
                  : 'border-gray-300 hover:bg-gray-50 hover:border-indigo-500'
            ]"
            @click="selectIntegrationType(type)"
          >
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="flex-shrink-0 w-10 h-10 flex items-center justify-center rounded-full"
                  :class="type.disabled ? 'bg-gray-100 text-gray-400' : getIntegrationTypeIconBg(type.value)">
                  <i :class="getIntegrationTypeIcon(type.value)" class="text-lg"></i>
                </div>
                <div class="ml-3">
                  <div class="flex items-center">
                    <h3 class="text-sm font-medium" :class="type.disabled ? 'text-gray-500' : 'text-gray-900'">
                      {{ type.label }}
                    </h3>
                    <span v-if="type.disabled" class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                      即将上线
                    </span>
                  </div>
                  <p class="text-sm text-gray-500">{{ type.description }}</p>
                </div>
              </div>
              <div v-if="integration.type === type.value" class="flex-shrink-0 flex items-center">
                <span class="inline-flex items-center justify-center h-6 w-6 rounded-full bg-indigo-100 text-indigo-600">
                  <i class="fas fa-check text-sm"></i>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// defineProps和defineEmits是编译器宏，不需要导入
import DataSourceSelector from '@/components/datasource/DataSourceSelector.vue';
import QuerySelector from '@/components/query/QuerySelector.vue';
import QueryVersionSelector from '@/components/query/QueryVersionSelector.vue';
import { INTEGRATION_TYPES, INTEGRATION_TYPE_ICONS } from '@/config/integrationConfigs';
import type { IntegrationType, IntegrationData } from '@/types/unified-integration';

// 使用从配置文件导入的集成类型选项
const integrationTypes = INTEGRATION_TYPES;

const props = defineProps({
  integration: {
    type: Object as () => IntegrationData,
    required: true
  },
  validationErrors: {
    type: Object as () => Record<string, string>,
    required: true
  },
  showValidationErrors: {
    type: Boolean,
    required: true
  }
});

const emit = defineEmits([
  'type-selected',
  'data-source-selected',
  'query-changed',
  'version-changed'
]);

// 获取集成类型图标
const getIntegrationTypeIcon = (type: string) => {
  return INTEGRATION_TYPE_ICONS[type as keyof typeof INTEGRATION_TYPE_ICONS]?.icon || 'fas fa-cog';
};

// 获取集成类型图标背景
const getIntegrationTypeIconBg = (type: string) => {
  return INTEGRATION_TYPE_ICONS[type as keyof typeof INTEGRATION_TYPE_ICONS]?.bgClass || 'bg-gray-100 text-gray-600';
};

// 方法：选择集成类型
const selectIntegrationType = (type: any) => {
  // 如果类型被禁用，不执行任何操作
  if (type.disabled) return;

  // 如果当前已经选中，不执行任何操作
  if (props.integration.type === type.value) return;

  // 触发类型选择事件
  emit('type-selected', type.value as IntegrationType);
};

// 方法：处理数据源选择
const handleDataSourceSelected = (id: string) => {
  emit('data-source-selected', id);
};

// 方法：处理查询变更
const handleQueryChange = async (queryId: string) => {
  emit('query-changed', queryId);
};

// 方法：处理查询选中事件
const handleQuerySelected = (id: string, query: any) => {
  console.log('[BasicInfoForm] 查询选中:', id, query);
  // 可以在这里进行额外处理
};

// 方法：处理版本变更
const handleVersionChange = (versionId: string) => {
  emit('version-changed', versionId);
};
</script>

<style scoped>
.force-border {
  border: 1px solid #d1d5db !important;
}

/* 错误状态的输入框边框样式 */
.error-border {
  border: 1px solid #ef4444 !important;
  box-shadow: 0 0 0 1px #ef4444 !important;
}
</style>