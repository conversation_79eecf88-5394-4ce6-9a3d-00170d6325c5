/**
 * 集成导出相关工具函数
 */
import type { IntegrationData, QueryParam } from '@/types/unified-integration';
import { transformFrontendIntegrationToApi } from '@/utils/apiTransformer';

/**
 * 导出集成配置为JSON
 * @param integration 集成数据
 * @param queryParams 查询参数
 * @param paramValues 参数值
 * @returns 导出的文件名
 */
export const exportIntegrationToJson = (
  integration: any,
  queryParams: any[] = [],
  paramValues: Record<string, any> = {}
) => {
  console.log('[exportIntegrationToJson] 开始导出集成数据');
  console.log('[exportIntegrationToJson] 集成数据类型:', typeof integration);
  console.log('[exportIntegrationToJson] 查询参数数量:', Array.isArray(integration.queryParams) ? integration.queryParams.length : (queryParams.length || 0));
  
  // 确保集成对象包含所有必要的字段
  if (!integration) {
    console.error('[exportIntegrationToJson] 错误: 集成对象为空');
    return;
  }

  try {
    // 如果集成对象已包含查询参数，则使用它们
    const exportObj = {
      ...integration,
      // 仅当集成对象不包含查询参数时添加
      ...((!integration.queryParams && queryParams.length > 0) ? { queryParams } : {}),
      ...(Object.keys(paramValues).length > 0 ? { paramValues } : {})
    };
    
    console.log('[exportIntegrationToJson] 最终导出对象:', exportObj);
    console.log('[exportIntegrationToJson] 最终导出对象包含queryParams:', Boolean(exportObj.queryParams));
    
    // 转换为JSON字符串，并添加额外的稳健性检查
    const jsonString = JSON.stringify(exportObj, null, 2);
    
    // 验证JSON字符串
    try {
      JSON.parse(jsonString);
      console.log('[exportIntegrationToJson] JSON验证成功');
    } catch (e) {
      console.error('[exportIntegrationToJson] JSON验证失败:', e);
      return;
    }
    
    // 使用正确的MIME类型创建Blob
    const blob = new Blob([jsonString], { type: 'application/json;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    const filename = `${integration.name || 'integration'}_${new Date().getTime()}.json`;
    link.download = filename;
    link.href = url;
    document.body.appendChild(link);
    link.click();
    
    // 清理
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    
    console.log('[exportIntegrationToJson] 导出完成, 文件名:', filename);
    return filename;
  } catch (error) {
    console.error('[exportIntegrationToJson] 导出过程中发生错误:', error);
  }
};