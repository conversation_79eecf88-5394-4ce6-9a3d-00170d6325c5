<template>
  <Teleport to="body">
    <transition
      enter-active-class="ease-out duration-300"
      enter-from-class="opacity-0"
      enter-to-class="opacity-100"
      leave-active-class="ease-in duration-200"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <div
        v-if="modelValue"
        class="fixed inset-0 modal-container overflow-y-auto"
        aria-labelledby="modal-title"
        role="dialog"
        aria-modal="true"
        @click.self="closeOnBackdropClick ? $emit('update:modelValue', false) : null"
      >
        <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
          <div
            class="fixed inset-0 modal-backdrop bg-gray-500 bg-opacity-75 transition-opacity"
            aria-hidden="true"
          ></div>

          <span
            class="hidden sm:inline-block sm:align-middle sm:h-screen"
            aria-hidden="true"
          >&#8203;</span>

          <div
            class="inline-block align-bottom modal-content bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6"
            :class="[sizeClasses]"
          >
            <div class="absolute top-0 right-0 pt-4 pr-4">
              <button
                v-if="showCloseButton"
                type="button"
                class="bg-white rounded-md text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                @click="$emit('update:modelValue', false)"
              >
                <span class="sr-only">Close</span>
                <i class="fas fa-times h-6 w-6"></i>
              </button>
            </div>

            <div v-if="title" class="sm:flex sm:items-start">
              <div class="mt-3 text-center sm:mt-0 sm:text-left w-full">
                <h3
                  class="text-lg leading-6 font-medium text-gray-900"
                  id="modal-title"
                >
                  {{ title }}
                </h3>
              </div>
            </div>

            <div class="mt-3 sm:mt-4">
              <slot></slot>
            </div>

            <div v-if="$slots.footer" class="mt-5 sm:mt-6 sm:grid sm:grid-cols-2 sm:gap-3 sm:grid-flow-row-dense">
              <slot name="footer"></slot>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </Teleport>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'Modal',
  props: {
    modelValue: {
      type: Boolean,
      required: true
    },
    title: {
      type: String,
      default: ''
    },
    size: {
      type: String,
      default: 'md',
      validator: (value: string) => ['sm', 'md', 'lg', 'xl', 'full'].includes(value)
    },
    closeOnBackdropClick: {
      type: Boolean,
      default: true
    },
    showCloseButton: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    sizeClasses(): string {
      const sizes = {
        sm: 'sm:max-w-sm',
        md: 'sm:max-w-lg',
        lg: 'sm:max-w-2xl',
        xl: 'sm:max-w-4xl',
        full: 'sm:max-w-full sm:mx-4'
      };
      return sizes[this.size as keyof typeof sizes] || sizes.md;
    }
  },
  methods: {
    close() {
      this.$emit('update:modelValue', false);
    }
  }
});
</script>

<style scoped>
.modal-container {
  z-index: var(--z-modal);
}

.modal-backdrop {
  z-index: var(--z-modal-backdrop);
}

.modal-content {
  z-index: var(--z-modal-content);
  position: relative;
}

/* 修复模态框内部的下拉菜单被截断问题 */
:deep(select),
:deep(.field-selector),
:deep(.dropdown) {
  position: relative;
}

:deep(select:focus),
:deep(select:active),
:deep(.field-selector:focus-within),
:deep(.dropdown.active) {
  z-index: var(--z-modal-dropdown) !important;
}

:deep(.overflow-hidden),
:deep(.overflow-x-auto),
:deep(.overflow-y-auto) {
  overflow: visible !important;
}

:deep(.absolute) {
  z-index: var(--z-modal-dropdown) !important;
}
</style> 