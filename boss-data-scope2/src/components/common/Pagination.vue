<template>
  <div class="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6">
    <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
      <div>
        <p class="text-sm text-gray-700">
          显示第 
          <span class="font-medium">{{ startItem }}</span>
          至
          <span class="font-medium">{{ endItem }}</span>
          条，共
          <span class="font-medium">{{ totalItems }}</span>
          条
        </p>
      </div>
      <div>
        <nav class="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
          <a href="#" 
            class="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0"
            :class="{ 'cursor-not-allowed opacity-50': currentPage === 1 }"
            @click.prevent="handlePageChange(currentPage - 1)"
            v-if="showPrevious"
          >
            <span class="sr-only">上一页</span>
            <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
              <path fill-rule="evenodd" d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z" clip-rule="evenodd" />
            </svg>
          </a>
          
          <!-- 页码按钮 -->
          <template v-for="(page, index) in visiblePageNumbers" :key="index">
            <!-- 省略号 -->
            <span
              v-if="page === '...'"
              class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-700 ring-1 ring-inset ring-gray-300 focus:outline-offset-0"
            >...</span>
            
            <!-- 页码按钮 -->
            <a href="#"
              v-else
              class="relative inline-flex items-center px-4 py-2 text-sm font-semibold focus:z-20 focus:outline-offset-0"
              :class="[
                currentPage === page
                  ? 'z-10 bg-indigo-600 text-white focus:outline-offset-0'
                  : 'text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50'
              ]"
              @click.prevent="handlePageChange(page)"
            >{{ page }}</a>
          </template>
          
          <a href="#"
            class="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0"
            :class="{ 'cursor-not-allowed opacity-50': currentPage === totalPages }"
            @click.prevent="handlePageChange(currentPage + 1)"
            v-if="showNext"
          >
            <span class="sr-only">下一页</span>
            <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
              <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
            </svg>
          </a>
        </nav>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

// 定义属性
const props = defineProps({
  currentPage: {
    type: Number,
    required: true
  },
  pageSize: {
    type: Number,
    default: 10
  },
  totalItems: {
    type: Number,
    required: true
  },
  showPageCount: {
    type: Number,
    default: 5
  }
});

// 定义事件
const emit = defineEmits(['page-change']);

// 计算总页数
const totalPages = computed(() => {
  return Math.max(1, Math.ceil(props.totalItems / props.pageSize));
});

// 计算显示的页码
const visiblePageNumbers = computed(() => {
  const current = props.currentPage;
  const total = totalPages.value;
  const count = props.showPageCount;
  const result = [];
  
  if (total <= count) {
    // 如果总页数小于等于显示页数，显示所有页码
    for (let i = 1; i <= total; i++) {
      result.push(i);
    }
  } else {
    // 总是显示第一页
    result.push(1);
    
    // 当前页靠近开始
    if (current <= Math.floor(count / 2) + 1) {
      for (let i = 2; i <= count - 1; i++) {
        result.push(i);
      }
      if (count < total) result.push('...');
      result.push(total);
    } 
    // 当前页靠近结束
    else if (current >= total - Math.floor(count / 2)) {
      result.push('...');
      for (let i = total - count + 2; i < total; i++) {
        result.push(i);
      }
      result.push(total);
    } 
    // 当前页在中间
    else {
      result.push('...');
      const start = current - Math.floor((count - 4) / 2);
      const end = current + Math.ceil((count - 4) / 2);
      for (let i = start; i <= end; i++) {
        result.push(i);
      }
      result.push('...');
      result.push(total);
    }
  }
  
  return result;
});

// 计算当前页显示的起始项
const startItem = computed(() => {
  if (props.totalItems === 0) return 0;
  return (props.currentPage - 1) * props.pageSize + 1;
});

// 计算当前页显示的结束项
const endItem = computed(() => {
  return Math.min(props.currentPage * props.pageSize, props.totalItems);
});

// 是否显示上一页按钮
const showPrevious = computed(() => {
  return props.totalItems > 0;
});

// 是否显示下一页按钮
const showNext = computed(() => {
  return props.totalItems > 0;
});

// 页码变更处理
const handlePageChange = (page: number | string) => {
  if (typeof page === 'string') return;
  
  // 边界检查
  if (page < 1 || page > totalPages.value) return;
  
  // 只有当页码变化时才触发事件
  if (page !== props.currentPage) {
    emit('page-change', page);
  }
};
</script>