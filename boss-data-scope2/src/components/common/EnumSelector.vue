<template>
  <div class="enum-selector">
    <a-select
      v-model:value="selectedValue"
      :placeholder="placeholder"
      :loading="loading"
      show-search
      :allowClear="clearable"
      :disabled="disabled"
      @change="onChange"
      class="w-full"
      :filter-option="filterOption"
    >
      <template #dropdownRender="{ menuNode: menu }">
        <div v-if="recentOptions.length > 0">
          <div class="recent-options-header text-xs text-gray-500 px-3 py-1">最近使用</div>
          <a-select-option
            v-for="item in recentOptions"
            :key="'recent-' + item.value"
            :value="item.value"
          >
            {{ item.label }}
          </a-select-option>
          <a-divider style="margin: 4px 0" />
        </div>
        <div v-if="loading" class="p-4 text-center text-gray-500">
          <span class="ant-spin-dot" style="margin-right: 8px">
            <i class="ant-spin-dot-item"></i>
            <i class="ant-spin-dot-item"></i>
            <i class="ant-spin-dot-item"></i>
            <i class="ant-spin-dot-item"></i>
          </span>
          加载中...
        </div>
        <div v-else-if="options.length === 0" class="p-4 text-center text-gray-500">
          暂无选项
        </div>
        <template v-else>
          {{ menu }}
        </template>
      </template>

      <a-select-option
        v-for="item in options"
        :key="item.value"
        :value="item.value"
      >
        {{ item.label }}
      </a-select-option>
    </a-select>
  </div>
</template>

<script setup lang="ts">
// @ts-ignore - 为了解决Vue API导入问题
import { ref, computed, onMounted, watch } from 'vue';
import { listEnumerations, convertEnumerationToOptions, type Enumeration } from '@/api/enumerationService';
import { enumServiceConfig } from '@/utils/config';

// 组件属性
const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: ''
  },
  projectCode: {
    type: String,
    default: () => enumServiceConfig.projectCode
  },
  enumCode: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '请选择'
  },
  clearable: {
    type: Boolean,
    default: true
  },
  disabled: {
    type: Boolean,
    default: false
  }
});

// 组件事件
const emit = defineEmits(['update:modelValue', 'change']);

// 状态
const loading = ref(false);
const enumerations = ref<Enumeration[]>([]);
const selectedValue = ref(props.modelValue);
const recentOptions = ref<Array<{label: string, value: string}>>([]);

// 计算选项列表
const options = computed(() => {
  if (!props.enumCode || enumerations.value.length === 0) {
    return [];
  }

  const enumeration = enumerations.value.find(e => e.code === props.enumCode);
  if (!enumeration) {
    return [];
  }

  const opts = convertEnumerationToOptions(enumeration);
  
  // 从本地存储获取最近使用的选项，并排除当前选项列表中已有的
  const recentValues = recentOptions.value.map(item => item.value);
  
  return opts.filter(item => !recentValues.includes(item.value));
});

// 搜索过滤函数
const filterOption = (input: string, option: any) => {
  return option.label?.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

// 更新modelValue的方法
const updateModelValue = () => {
  selectedValue.value = props.modelValue;
};

// 监听值变化
watch(() => props.modelValue, (newValue: string | number) => {
  selectedValue.value = newValue;
});

// 组件挂载时更新一次值
onMounted(() => {
  loadRecentOptions();
  loadEnumerations();
});

// 导出方法给父组件调用
defineExpose({
  updateModelValue
});

// 选项变化事件
const onChange = (value: string) => {
  emit('update:modelValue', value);
  emit('change', value);
  
  // 如果选择了值且不为空，添加到最近使用
  if (value) {
    const selectedOption = [...options.value, ...recentOptions.value].find(item => item.value === value);
    if (selectedOption) {
      addToRecentOptions(selectedOption);
    }
  }
};

// 加载指定项目的枚举列表
const loadEnumerations = async () => {
  if (!props.projectCode) return;
  
  try {
    loading.value = true;
    
    // 加载枚举列表，请求大页码以获取全部数据
    const result = await listEnumerations(props.projectCode, 1, 100);
    enumerations.value = result.list;
    
    // 如果有枚举编码但没找到对应的枚举，打印警告
    if (props.enumCode && !enumerations.value.find(e => e.code === props.enumCode)) {
      console.warn(`未找到编码为"${props.enumCode}"的枚举，请检查枚举编码是否正确`);
    }
  } catch (error) {
    console.error('加载枚举列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 添加到最近使用的选项
const addToRecentOptions = (option: {label: string, value: string}) => {
  // 获取当前的最近使用选项
  const current = [...recentOptions.value];
  
  // 移除已存在的相同选项
  const filtered = current.filter(item => item.value !== option.value);
  
  // 添加到最前面
  filtered.unshift(option);
  
  // 只保留最近使用的5个选项
  recentOptions.value = filtered.slice(0, 5);
  
  // 保存到localStorage
  saveRecentOptions();
};

// 保存最近使用的选项到localStorage
const saveRecentOptions = () => {
  try {
    const key = `enum_recent_${props.projectCode}_${props.enumCode}`;
    localStorage.setItem(key, JSON.stringify(recentOptions.value));
  } catch (e) {
    console.error('保存最近使用选项失败:', e);
  }
};

// 从localStorage加载最近使用的选项
const loadRecentOptions = () => {
  try {
    const key = `enum_recent_${props.projectCode}_${props.enumCode}`;
    const saved = localStorage.getItem(key);
    if (saved) {
      recentOptions.value = JSON.parse(saved);
    }
  } catch (e) {
    console.error('加载最近使用选项失败:', e);
  }
};
</script>

<style scoped>
.enum-selector {
  width: 100%;
}

.recent-options-header {
  background-color: #f5f7fa;
}
</style>