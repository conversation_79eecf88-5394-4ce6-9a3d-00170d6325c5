<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { watch } from '@/plugins/vue-types-fix'
import { useDataSourceStore } from '@/stores/datasource'
import { message } from '@/services/message'
import instance from "@/utils/axios";

// 组件属性
const props = defineProps<{
  dataSourceId: string
  tableName: string
  tableId?: string
  initialPageSize?: number
}>()

// 添加调试日志
console.log(`[TableDataPreview] 初始化，数据源ID: ${props.dataSourceId}, 表名: ${props.tableName}, 表ID: ${props.tableId}`);

// 数据源存储
const dataSourceStore = useDataSourceStore()

// 表信息
const tableInfo = ref<any>(null)

// 数据预览状态
const isLoading = ref(false)
// 当前页输入框
const currentPageInput = ref(1)
const tableData = ref<any[]>([])
const columns = ref<any[]>([])
const pagination = ref({
  page: 1,
  size: props.initialPageSize || 10,
  total: 0,
  totalPages: 0
})
const sortConfig = ref<{ field: string, order: 'asc' | 'desc' } | null>(null)
const filters = ref<Record<string, any>>({})
const showFilterPanel = ref(false)
const activeFilter = ref<string | null>(null)
const filterValues = ref<Record<string, any>>({})
const currentStats = ref<{
  uniqueValues: number,
  nullValues: number,
  minValue?: any,
  maxValue?: any
} | null>(null)
const error = ref<string | null>(null)

// 加载表信息
const loadTableInfo = async () => {
  if (props.tableId) {
    try {
      // 直接使用数据源服务获取表信息
      const table = await dataSourceStore.getTableMetadata(props.dataSourceId, props.tableName);
      tableInfo.value = table;
      console.log('表信息加载成功:', tableInfo.value);
    } catch (err) {
      console.error('加载表信息失败:', err);
      // 这里不设置错误，因为主要功能是显示列和数据，表信息只是辅助显示
    }
  }
}

// 加载表数据
const loadTableData = async (page = 1, pageSize = 10, forceReload = false) => {
  if (!props.dataSourceId || !props.tableName) return

  // 校验页码范围
  if (page < 1) {
    page = 1;
  }

  console.log('[TableDataPreview] 开始加载表数据预览', {
    dataSourceId: props.dataSourceId,
    tableName: props.tableName,
    tableId: props.tableId,
    page: page,
    size: pageSize,
    sort: sortConfig.value?.field,
    order: sortConfig.value?.order,
    filters: filters.value
  });

  isLoading.value = true
  error.value = null

  try {
    // 优先加载表信息
    if (props.tableId) {
      await loadTableInfo();
    }

    console.log('[TableDataPreview] 调用store.getTableDataPreview，参数:', {
      dataSourceId: props.dataSourceId,
      tableName: props.tableName,
      page: page,
      size: pageSize,
    });

    // 加载表的列信息
    if (props.tableId) {
      console.log(`[TableDataPreview] 尝试获取表 ${props.tableId} 的列信息`);
      try {
        // 使用正确的API路径
        const columnsUrl = `/api/metadata/tables/${props.tableId}/columns`;
        console.log('[TableDataPreview] 获取列信息API URL:', columnsUrl);

        // 添加request.前缀参数格式，如果需要的话
        const columnsQueryString = 'request.all=true';
        const fullColumnsUrl = `${columnsUrl}?${columnsQueryString}`;

        const columnsResponse = await instance.get(fullColumnsUrl);
        const columnsResponseData = columnsResponse.data;

        console.log('[TableDataPreview] 获取到列信息:', columnsResponseData);

        // 处理列信息 - 支持两种格式:
        // 1. 标准格式: { success: true, data: [...] }
        // 2. 直接数组格式: [...]
        let columnsArray: any[] = [];

        if (Array.isArray(columnsResponseData)) {
          // 直接是数组格式
          console.log(`[TableDataPreview] 列信息直接以数组形式返回，共 ${columnsResponseData.length} 列数据`);
          columnsArray = columnsResponseData;
        } else if (columnsResponseData.success && Array.isArray(columnsResponseData.data)) {
          // 标准格式
          console.log(`[TableDataPreview] 列信息API返回了 ${columnsResponseData.data.length} 列数据`);
          columnsArray = columnsResponseData.data;
        } else {
          console.warn('[TableDataPreview] 列信息API返回格式不正确:', columnsResponseData);
          columnsArray = [];
        }

        // 如果获取到了列信息
        if (columnsArray.length > 0) {
          // 更新表信息（如果还没有）
          if (!tableInfo.value || !tableInfo.value.columnsCount) {
            if (!tableInfo.value) {
              tableInfo.value = {};
            }

            // 无论有没有从表元数据API获取到信息，先把列数记录下来
            tableInfo.value.columnsCount = columnsArray.length;
            console.log('[TableDataPreview] 根据API返回设置表的列数:', columnsArray.length);

            const firstColumn = columnsArray.length > 0 ? columnsArray[0] : null;
            if (firstColumn && firstColumn.table_id) {
              try {
                // 尝试获取表的元数据信息
                const tableMetadataUrl = `/api/metadata/tables/${firstColumn.table_id}`;
                console.log('[TableDataPreview] 尝试获取表元数据，URL:', tableMetadataUrl);

                const metadataQueryString = 'request.detail=true';
                const fullMetadataUrl = `${tableMetadataUrl}?${metadataQueryString}`;

                const tableMetadataResp = await instance.get(fullMetadataUrl);
                const tableMetadata = tableMetadataResp.data;

                console.log('[TableDataPreview] 表元数据API返回:', tableMetadata);

                if (tableMetadata.success && tableMetadata.data) {
                  // 合并表信息，保留列数
                  const columnsCount = tableInfo.value.columnsCount;
                  tableInfo.value = {
                    ...tableMetadata.data,
                    columnsCount: tableMetadata.data.columnsCount || columnsCount
                  };
                  console.log('[TableDataPreview] 合并后的表元数据:', tableInfo.value);
                }
              } catch (metaErr) {
                console.error('[TableDataPreview] 获取表元数据失败:', metaErr);
              }
            }
          }

          // 处理列信息
          columns.value = columnsArray.map((col: any) => {
            return {
              name: col.name || col.columnName || 'unknown',
              type: col.dataType || col.type || 'VARCHAR',
              nullable: col.nullable !== undefined ? col.nullable : true,
              primaryKey: !!col.primaryKey,
              defaultValue: col.defaultValue
            };
          });

          console.log('[TableDataPreview] 处理后的列信息:', columns.value.length, '列');
        } else {
          console.warn('[TableDataPreview] 无法获取有效的列信息');
        }
      } catch (colErr) {
        console.error('[TableDataPreview] 获取列信息时发生错误:', colErr);
      }
    }

    // 然后加载表数据 - 使用正确的API路径格式
    if (props.tableId) {
      const dataUrl = `/api/metadata/tables/${props.tableId}/datas`;
      console.log(`[TableDataPreview] 开始请求表数据, URL: ${dataUrl}`);

      // 使用正确的参数格式
      const requestParams = {
        'page': page,  // 后端页码从1开始
        'size': pageSize
      };

      // 添加过滤器到请求参数
      if (Object.keys(filters.value).length > 0) {
        Object.entries(filters.value).forEach(([key, value]) => {
          if (value !== null && value !== undefined) {
            (requestParams as any)[`filters.${key}`] = value;
          }
        });
      }

      // 添加排序
      if (sortConfig.value && sortConfig.value.field) {
        (requestParams as any)['sort'] = sortConfig.value.field;
        (requestParams as any)['order'] = sortConfig.value.order;
      }

      // 构建URL参数
      const params = new URLSearchParams();
      for (const [key, value] of Object.entries(requestParams)) {
        params.append(key, String(value));
      }

      const fullUrl = `${dataUrl}?${params.toString()}`;
      console.log('[TableDataPreview] 完整请求URL:', fullUrl);

      try {
        const response = await instance.get(fullUrl);
        const responseData = response.data;

        console.log('[TableDataPreview] 表数据预览响应:', responseData);

        // 处理表数据 - 支持三种格式:
        // 1. 标准格式: { success: true, data: {...}, code: 200, message: '' }
        // 2. 直接数组格式: [...]
        // 3. 分页对象格式: { items: [...], total: number, ... }

        if (Array.isArray(responseData)) {
          // 直接是数组格式
          console.log('[TableDataPreview] 表数据以数组形式返回，共', responseData.length, '行');
          tableData.value = responseData;

          // 简单分页信息
          pagination.value = {
            page: page,
            size: pageSize,
            total: responseData.length,
            totalPages: Math.ceil(responseData.length / pageSize)
          };
        } else if (responseData.success && responseData.data) {
          // 标准成功响应格式
          console.log('[TableDataPreview] 表数据以标准格式返回');

          // 检查data字段是否包含嵌套分页结构
          if (responseData.data.items && Array.isArray(responseData.data.items)) {
            console.log('[TableDataPreview] 标准响应中包含嵌套分页结构', responseData.data);
            tableData.value = responseData.data.items;

            // 解析分页信息
            pagination.value = {
              page: responseData.data.page || page,
              size: responseData.data.size || pageSize,
              total: responseData.data.total || 0,
              totalPages: responseData.data.pages || Math.ceil((responseData.data.total || 0) / pageSize)
            };
          } else if (Array.isArray(responseData.data)) {
            tableData.value = responseData.data;
          } else if (responseData.data.items && Array.isArray(responseData.data.items)) {
            tableData.value = responseData.data.items;
          } else {
            // 单个对象情况
            tableData.value = [responseData.data];
          }

          // 解析分页信息（如果前面没有处理过）
          if (!(responseData.data.items && Array.isArray(responseData.data.items))) {
            pagination.value = {
              page: responseData.page || page,
              size: responseData.size || pageSize,
              total: responseData.total || tableData.value.length,
              totalPages: responseData.totalPages || Math.ceil((responseData.total || tableData.value.length) / pageSize)
            };
          }
        } else if (responseData.items && Array.isArray(responseData.items)) {
          // 分页对象格式
          console.log('[TableDataPreview] 表数据以分页对象格式返回');
          tableData.value = responseData.items;

          // 解析分页信息
          pagination.value = {
            page: responseData.page || page,
            size: responseData.size || pageSize,
            total: responseData.total || tableData.value.length,
            totalPages: responseData.pages || Math.ceil((responseData.total || tableData.value.length) / pageSize)
          };
        } else {
          // 无法解析的格式
          console.error('[TableDataPreview] 请求表数据失败:', responseData);
          error.value = responseData.message || '获取数据失败: 未知格式';
          tableData.value = [];
        }

        // 如果有数据但没有列信息，从数据中提取列信息
        if (tableData.value.length > 0 && columns.value.length === 0) {
          const firstRow = tableData.value[0];
          columns.value = Object.keys(firstRow).map((key: string) => {
            const type = typeof firstRow[key];
            return {
              name: key,
              type: type === 'number' ? 'NUMBER' : (type === 'boolean' ? 'BOOLEAN' : 'VARCHAR'),
              nullable: true
            };
          });
          console.log('[TableDataPreview] 从数据中提取列信息:', columns.value);
        }

        console.log('[TableDataPreview] 成功获取到数据:', tableData.value.length, '行');
      } catch (err) {
        console.error('[TableDataPreview] 请求表数据出错:', err);
        // 尝试使用直接的后端URL作为备选
        try {
          console.log('[TableDataPreview] 尝试使用直接的后端URL作为备选');
          // 构建直接的后端URL
          const directBackendUrl = `/api/data-sources/${props.dataSourceId}/tables/${props.tableName}/data`;
          console.log('[TableDataPreview] 备选URL:', directBackendUrl);

          const directResponse = await instance.get(directBackendUrl);
          const directData = directResponse.data;

          if (directData && (directData.data || Array.isArray(directData))) {
            // 处理响应数据
            tableData.value = directData.data || directData;

            // 自动设置列信息（如果还没有）
            if (tableData.value.length > 0 && columns.value.length === 0) {
              const firstRow = tableData.value[0];
              columns.value = Object.keys(firstRow).map((key: string) => {
                const type = typeof firstRow[key];
                return {
                  name: key,
                  type: type === 'number' ? 'NUMBER' : (type === 'boolean' ? 'BOOLEAN' : 'VARCHAR'),
                  nullable: true
                };
              });
            }

            // 使用简单的分页信息
            pagination.value = {
              page: page,
              size: pageSize,
              total: tableData.value.length,
              totalPages: Math.ceil(tableData.value.length / pageSize)
            };

            console.log('[TableDataPreview] 通过备选URL成功获取到数据:', tableData.value.length, '行');
          } else {
            error.value = '获取数据失败: 备选URL也没有返回有效数据';
          }
        } catch (directErr) {
          console.error('[TableDataPreview] 备选URL请求也失败:', directErr);
          error.value = '获取数据失败，所有尝试都失败了';
        }
      }
    }

    // 如果直接获取数据失败，继续尝试store方法
    console.log('[TableDataPreview] 尝试通过store获取数据');
    const result = await dataSourceStore.getTableDataPreview(
      props.dataSourceId,
      props.tableName,
      {
        'request.page': page - 1,
        'request.size': pageSize,
        'request.sort': sortConfig.value?.field,
        'request.order': sortConfig.value?.order,
        'request.filters': filters.value
      }
    )

    // 详细记录API返回的数据结构
    console.log('[TableDataPreview] 表数据预览API返回详情:', {
      resultType: typeof result,
      resultIsArray: Array.isArray(result),
      resultKeys: result ? Object.keys(result) : '无数据',
      dataType: result?.data ? typeof result.data : '无data字段',
      dataIsArray: result?.data ? Array.isArray(result.data) : '无data字段',
      dataLength: result?.data && Array.isArray(result.data) ? result.data.length : 0,
      columnsType: result?.columns ? typeof result.columns : '无columns字段',
      columnsIsArray: result?.columns ? Array.isArray(result.columns) : '无columns字段',
      columnsLength: result?.columns && Array.isArray(result.columns) ? result.columns.length : 0,
      pagination: {
        page: result?.page,
        size: result?.size,
        total: result?.total,
        totalPages: result?.totalPages
      }
    });

    // 处理返回的数据
    if (result) {
      // 确保tableData.value是数组
      if (result.data === null || result.data === undefined) {
        tableData.value = [];
      } else if (Array.isArray(result.data)) {
        tableData.value = result.data;
      } else if (result.data && result.data.items && Array.isArray(result.data.items)) {
        // 处理嵌套结构: { data: { items: [...], total: 41, ... } }
        console.log('[TableDataPreview] 处理嵌套结构的数据响应');
        tableData.value = result.data.items;

        // 更新分页信息
        if (result.data.total !== undefined) {
          pagination.value = {
            page: result.data.page || page || 1,
            size: result.data.size || pageSize || 10,
            total: result.data.total || 0,
            totalPages: result.data.pages || Math.ceil((result.data.total || 0) / (result.data.size || pageSize || 10))
          };
        }
      } else if (typeof result.data === 'object') {
        // 如果返回的是一个对象，尝试将其转换为数组
        tableData.value = [result.data];
      } else {
        console.warn('[TableDataPreview] API返回的data格式不是数组或对象:', result.data);
        tableData.value = [];
      }

      // 如果通过API未获取列信息，使用预览数据中的列信息
      if (columns.value.length === 0 && Array.isArray(result.columns) && result.columns.length > 0) {
        // 直接使用API返回的列定义
        columns.value = result.columns.map((col: any) => {
          return {
            name: col.name || col.columnName || 'unknown',
            type: col.dataType || col.type || 'VARCHAR',
            nullable: col.nullable !== undefined ? col.nullable : true,
            primaryKey: !!col.primaryKey,
            defaultValue: col.defaultValue
          };
        });
      } else if (columns.value.length === 0 && tableData.value.length > 0) {
        // 如果没有列定义但有数据，从第一行数据推断列定义
        const firstRow = tableData.value[0];
        columns.value = Object.keys(firstRow).map(key => ({
          name: key,
          type: typeof firstRow[key] === 'number' ? 'NUMBER' :
               (firstRow[key] instanceof Date ? 'TIMESTAMP' :
               (typeof firstRow[key] === 'boolean' ? 'BOOLEAN' : 'VARCHAR')),
          nullable: true,
          primaryKey: false,
          defaultValue: null
        }));
      }

      // 更新分页信息，使用data.total字段
      const total = result.total || 0;
      const size = result.size || pageSize || 10;
      const currentPage = result.page || page || 1;
      const totalPages = Math.ceil(total / size) || 1;

      pagination.value = {
        page: currentPage,
        size: size,
        total: total,
        totalPages: totalPages
      };
    } else {
      // 如果结果为null或undefined
      tableData.value = [];
      // 保留现有的列信息，因为可能已经通过API加载
    }

    console.log('[TableDataPreview] 表数据处理后结果:', {
      tableDataLength: tableData.value.length,
      columnsLength: columns.value.length,
      columnsSample: columns.value.slice(0, 3),
      pagination: pagination.value
    });

  } catch (err) {
    console.error('[TableDataPreview] 加载表数据预览失败:', err)
    const errorMessage = '加载表数据预览失败: ' + (err instanceof Error ? err.message : String(err));
    message.error(errorMessage);
    error.value = errorMessage;
    tableData.value = []
    // 如果没有通过API加载列信息，则清空列信息
    if (columns.value.length === 0) {
      columns.value = [];
    }
  } finally {
    isLoading.value = false
  }
}

// 处理成功响应的函数
interface ApiResponseData {
  records?: any[];
  content?: any[];
  items?: any[];
  total?: number;
  size?: number;
  page?: number;
  totalPages?: number;
  totalElements?: number;
  number?: number;
}

interface ApiResponse {
  success: boolean;
  data: ApiResponseData;
  items?: any[];
  total?: number;
  message?: string;
}

const handleSuccessResponse = (dataResultJson: ApiResponse) => {
  console.log('[TableDataPreview] 处理成功响应');

  // 获取实际的数据记录
  let records: any[] = [];

  if (dataResultJson.data) {
    if (Array.isArray(dataResultJson.data)) {
      records = dataResultJson.data;
      console.log('[TableDataPreview] 数据是数组，直接使用, 长度:', records.length);
    } else if (dataResultJson.data.records && Array.isArray(dataResultJson.data.records)) {
      records = dataResultJson.data.records;
      console.log('[TableDataPreview] 使用records数组, 长度:', records.length);
    } else if (dataResultJson.data.content && Array.isArray(dataResultJson.data.content)) {
      records = dataResultJson.data.content;
      console.log('[TableDataPreview] 使用content数组, 长度:', records.length);
    } else if (dataResultJson.data.items && Array.isArray(dataResultJson.data.items)) {
      records = dataResultJson.data.items;
      console.log('[TableDataPreview] 使用items数组, 长度:', records.length);
    } else if (typeof dataResultJson.data === 'object') {
      // 单条记录情况
      records = [dataResultJson.data];
      console.log('[TableDataPreview] 数据是单个对象，封装为数组');
    }
  }

  // 如果还是没有记录，尝试从顶层获取
  if (records.length === 0 && dataResultJson.items && Array.isArray(dataResultJson.items)) {
    records = dataResultJson.items;
    console.log('[TableDataPreview] 使用顶层items数组, 长度:', records.length);
  }

  if (records.length > 0) {
    console.log('[TableDataPreview] 样本数据:', records[0]);
  } else {
    console.log('[TableDataPreview] 没有记录数据');
  }

  // 显示数据
  tableData.value = records;
  isLoading.value = false;
  error.value = null;

  // 更新分页信息 - 处理标准分页响应格式
  const total = dataResultJson.data.total || 0;
  const size = dataResultJson.data.size || pagination.value.size;
  const currentPage = pagination.value.page;
  const totalPages = Math.ceil(total / size) || 1;

  pagination.value = {
    page: currentPage,
    size: size,
    total: total,
    totalPages: totalPages
  };

  console.log('[TableDataPreview] 更新分页信息:', pagination.value);
};

// 处理页面变化
const handlePageChange = (newPage: number) => {
  // 严格校验页码范围
  if (!newPage || isNaN(newPage) || newPage < 1) {
    newPage = 1;
  }

  // 如果有总页数，则限制最大页码
  if (pagination.value.totalPages && newPage > pagination.value.totalPages) {
    newPage = pagination.value.totalPages;
  }

  // 如果页码没有变化，则不重新加载
  if (newPage === pagination.value.page) {
    return;
  }

  pagination.value.page = newPage;
  currentPageInput.value = newPage;
  loadTableData(newPage, pagination.value.size);
}

// 处理页面跳转
const handlePageJump = () => {
  // 校验输入值
  let page = Number(currentPageInput.value);

  if (!page || isNaN(page) || page < 1) {
    page = 1;
  }

  // 如果有总页数，则限制最大页码
  if (pagination.value.totalPages && page > pagination.value.totalPages) {
    page = pagination.value.totalPages;
  }

  // 更新输入框显示
  currentPageInput.value = page;

  // 如果页码发生变化，则加载新数据
  if (page !== pagination.value.page) {
    handlePageChange(page);
  }
}

// 初始化时同步当前页输入框
watch(() => pagination.value.page, (newPage) => {
  currentPageInput.value = newPage;
}, { immediate: true });

// 处理排序
const handleSort = (columnName: string) => {
  if (!sortConfig.value || sortConfig.value.field !== columnName) {
    sortConfig.value = { field: columnName, order: 'asc' }
  } else if (sortConfig.value.order === 'asc') {
    sortConfig.value = { field: columnName, order: 'desc' }
  } else {
    sortConfig.value = null
  }

  loadTableData(pagination.value.page, pagination.value.size)
}

// 显示过滤面板
const showFilter = (columnName: string) => {
  activeFilter.value = columnName
  showFilterPanel.value = true

  // 加载列统计信息
  calculateColumnStats(columnName)
}

// 计算列统计信息
const calculateColumnStats = (columnName: string) => {
  if (!tableData.value.length) {
    currentStats.value = {
      uniqueValues: 0,
      nullValues: 0
    }
    return
  }

  // 获取列数据
  const values = tableData.value.map(row => row[columnName])

  // 计算唯一值数量
  const uniqueValues = new Set(values.filter(v => v !== null && v !== undefined)).size

  // 计算空值数量
  const nullValues = values.filter(v => v === null || v === undefined).length

  // 计算最小和最大值（对于数值型数据）
  const columnType = columns.value.find(c => c.name === columnName)?.type || ''
  const simpleType = getSimpleType(columnType)

  let minValue, maxValue

  if (simpleType === 'number') {
    const numValues = values.filter(v => v !== null && v !== undefined && !isNaN(Number(v))).map(Number)
    minValue = numValues.length ? Math.min(...numValues) : undefined
    maxValue = numValues.length ? Math.max(...numValues) : undefined
  } else if (simpleType === 'datetime') {
    const dateValues = values.filter(v => v !== null && v !== undefined).map(v => new Date(v))
    minValue = dateValues.length ? new Date(Math.min(...dateValues.map(d => d.getTime()))).toLocaleString() : undefined
    maxValue = dateValues.length ? new Date(Math.max(...dateValues.map(d => d.getTime()))).toLocaleString() : undefined
  }

  currentStats.value = {
    uniqueValues,
    nullValues,
    minValue,
    maxValue
  }
}

// 关闭过滤面板
const closeFilterPanel = () => {
  showFilterPanel.value = false
  activeFilter.value = null
  currentStats.value = null
}

// 应用过滤
const applyFilter = () => {
  if (activeFilter.value && filterValues.value[activeFilter.value]) {
    filters.value[activeFilter.value] = filterValues.value[activeFilter.value]
  }

  closeFilterPanel()
  pagination.value.page = 1
  loadTableData(pagination.value.page, pagination.value.size)
}

// 清除当前过滤
const clearCurrentFilter = () => {
  if (activeFilter.value) {
    delete filters.value[activeFilter.value]
    delete filterValues.value[activeFilter.value]
  }

  closeFilterPanel()
  pagination.value.page = 1
  loadTableData(pagination.value.page, pagination.value.size)
}

// 清除所有过滤器
const clearAllFilters = () => {
  filters.value = {}
  filterValues.value = {}
  pagination.value.page = 1
  loadTableData(pagination.value.page, pagination.value.size)
}

// 导出当前数据为CSV
const exportToCSV = () => {
  try {
    // 获取列名
    const header = columns.value.map(column => column.name).join(',')

    // 获取数据行
    const rows = tableData.value.map(row => {
      return columns.value.map(column => {
        const value = row[column.name]
        if (value === null || value === undefined) {
          return ''
        }
        // 处理包含逗号的字段，添加引号
        if (typeof value === 'string' && value.includes(',')) {
          return `"${value.replace(/"/g, '""')}"`
        }
        return value
      }).join(',')
    })

    // 组合CSV内容
    const csvContent = [header, ...rows].join('\n')

    // 创建Blob对象
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const url = URL.createObjectURL(blob)

    // 创建下载链接并触发下载
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', `${props.tableName}_data.csv`)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    message.success('数据已成功导出为CSV文件')
  } catch (error) {
    console.error('Failed to export data:', error)
    message.error('导出数据失败: ' + (error instanceof Error ? error.message : String(error)))
  }
}

// 监听属性变化
watch(
  () => [props.dataSourceId, props.tableName],
  () => {
    pagination.value.page = 1
    sortConfig.value = null
    filters.value = {}
    filterValues.value = {}
    loadTableData(pagination.value.page, pagination.value.size)
  }
)

// 组件挂载
onMounted(() => {
  console.log('TableDataPreview组件已挂载，准备加载数据', {
    dataSourceId: props.dataSourceId,
    tableName: props.tableName
  });
  loadTableData(pagination.value.page, pagination.value.size)
})

// 格式化字段值显示
const formatValue = (value: any): string => {
  if (value === null || value === undefined) {
    return '<null>'
  }

  if (typeof value === 'object') {
    if (value instanceof Date) {
      return value.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      })
    }
    return JSON.stringify(value)
  }

  if (typeof value === 'boolean') {
    return value ? 'true' : 'false'
  }

  return String(value)
}

// 处理列类型，返回简化的类型
const getSimpleType = (columnType: string): string => {
  columnType = columnType.toLowerCase()

  if (columnType.includes('int') || columnType.includes('float') || columnType.includes('double') ||
      columnType.includes('decimal') || columnType.includes('numeric')) {
    return 'number'
  } else if (columnType.includes('date') || columnType.includes('time')) {
    return 'datetime'
  } else if (columnType.includes('boolean') || columnType.includes('bool')) {
    return 'boolean'
  } else {
    return 'string'
  }
}

// 获取排序图标类
const getSortIconClass = (columnName: string) => {
  if (!sortConfig.value || sortConfig.value.field !== columnName) {
    return 'text-gray-400'
  }

  return sortConfig.value.order === 'asc' ? 'text-blue-500' : 'text-blue-700'
}

// 检查列是否有过滤器
const hasFilter = (columnName: string) => {
  return !!filters.value[columnName]
}

// 获取当前已应用的过滤器数量
const activeFiltersCount = computed(() => {
  return Object.keys(filters.value).length
})

// 格式化列类型显示
const formatColumnType = (columnType: string): string => {
  if (!columnType) return '-';

  // 简化类型显示
  const simplifiedType = columnType.split('(')[0];
  return simplifiedType;
}

// 格式化单元格内容
const formatCellContent = (value: any, type: string): string => {
  if (value === null || value === undefined) {
    return '<空>';
  }

  if (typeof value === 'object') {
    if (value instanceof Date) {
      return value.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      });
    }
    return JSON.stringify(value);
  }

  return String(value);
}

// 处理每页大小变化
const handleSizeChange = () => {
  pagination.value.page = 1;
  loadTableData(pagination.value.page, pagination.value.size);
}

// 获取排序类名
const getSortClass = (columnName: string) => {
  if (!sortConfig.value || sortConfig.value.field !== columnName) {
    return '';
  }
  return sortConfig.value.order === 'asc' ? 'asc' : 'desc';
}
</script>

<template>
  <div class="table-data-preview-container">
    <template v-if="isLoading">
      <div class="loading-container">
        <div class="loading-spinner"></div>
        <p>加载中...</p>
      </div>
    </template>
    <template v-else>
      <div v-if="!error" class="table-data-preview">
        <div class="preview-header">
          <h4>
            表数据预览
            <template v-if="tableInfo && (tableInfo.columnsCount !== undefined || tableInfo.rowCount !== undefined)">
              <span class="text-sm text-gray-500 ml-2">
                <template v-if="tableInfo.columnsCount !== undefined && tableInfo.columnsCount !== null">
                  ({{ tableInfo.columnsCount }} 列{{ tableInfo.rowCount !== undefined && tableInfo.rowCount !== null ? ' / ' : '' }})
                </template>
                <template v-if="tableInfo.rowCount !== undefined && tableInfo.rowCount !== null">
                  {{ tableInfo.columnsCount !== undefined && tableInfo.columnsCount !== null ? '' : '(' }}{{ tableInfo.rowCount }} 行)
                </template>
              </span>
            </template>
            <template v-else-if="columns.length > 0">
              <span class="text-sm text-gray-500 ml-2">({{ columns.length }} 列 {{ pagination.total > 0 ? `/ ${pagination.total} 行` : '' }})</span>
            </template>
          </h4>
        </div>
        <div class="preview-content">
          <!-- 显示数据表格，无论是否有数据行 -->
          <table v-if="columns.length > 0" class="data-table">
            <thead>
              <tr>
                <th v-for="column in columns" :key="column.name" @click="handleSort(column.name)" :class="['sortable', getSortClass(column.name)]">
                  <div class="column-header">
                    <div class="name-with-sort">
                      <span class="column-name">{{ column.name }}</span>
                      <span v-if="sortConfig && sortConfig.field === column.name" class="sort-icon">
                        <span v-if="sortConfig.order === 'asc'">↑</span>
                        <span v-else>↓</span>
                  </span>
              </div>
                    <span class="column-type">{{ formatColumnType(column.type) }}</span>
                  </div>
            </th>
          </tr>
        </thead>
            <tbody>
              <!-- 有数据行时显示数据 -->
              <template v-if="tableData.length > 0">
                <tr v-for="(row, rowIndex) in tableData" :key="rowIndex" :class="{'row-even': rowIndex % 2 === 0}">
                  <td v-for="column in columns" :key="column.name">
                    <span class="cell-content" :class="{'empty-value': row[column.name] === null || row[column.name] === undefined}">
                      {{ formatCellContent(row[column.name], column.type) }}
                    </span>
                  </td>
                </tr>
              </template>
              <!-- 无数据行时显示空表格信息 -->
              <tr v-else>
                <td :colspan="columns.length" class="text-center py-4">
                  <div class="empty-data-message">
                    <div class="empty-icon">📊</div>
                    <p class="empty-title">暂无数据</p>
                    <p class="empty-desc">该表没有数据，但已成功加载表结构信息</p>
                  </div>
            </td>
          </tr>
        </tbody>
            <tfoot>
              <tr>
                <td :colspan="columns.length" class="border-t border-gray-200 px-0">
                  <div class="bg-white px-4 py-3 flex items-center justify-between sm:px-6">
                    <div class="flex items-center">
                      <span class="text-sm text-gray-700">
                        共 <span class="font-medium">{{ pagination.total || 0 }}</span> 条
                      </span>
                      <select
                        v-model="pagination.size"
                        @change="handleSizeChange"
                        class="ml-2 rounded-md border-gray-300 py-1 text-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                      >
                        <option value="10">10 条/页</option>
                        <option value="20">20 条/页</option>
                        <option value="50">50 条/页</option>
                        <option value="100">100 条/页</option>
                      </select>
    </div>

                    <div class="flex items-center">
          <button
                        class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
            @click="handlePageChange(pagination.page - 1)"
                        :disabled="pagination.page <= 1"
          >
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                          <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
          </button>
          <button
                        class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
            @click="handlePageChange(pagination.page + 1)"
                        :disabled="pagination.page >= pagination.totalPages"
          >
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                          <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                        </svg>
          </button>
      </div>
    </div>
                </td>
              </tr>
            </tfoot>
          </table>

          <!-- 无列信息情况 -->
          <div v-else class="empty-data-message">
            <div class="icon-placeholder">ℹ️</div>
            <span>无列信息</span>
        </div>
            </div>
            </div>
      <div class="error-message" v-else>
        <div class="icon-placeholder">⚠️</div>
        <span>{{ error }}</span>
          </div>
    </template>
            </div>
</template>

<style lang="scss" scoped>
.table-data-preview-container {
  position: relative;
  height: 100%;

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 3px solid #f3f3f3;
      border-top: 3px solid #3498db;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    p {
      margin-top: 10px;
      color: #666;
    }
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .table-data-preview {
    height: 100%;
    display: flex;
    flex-direction: column;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
    container-type: inline-size;
    container-name: table-preview;

    .preview-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      background-color: #f9fafb;
      border-bottom: 1px solid #e5e7eb;

      h4 {
        margin: 0;
        color: #111827;
        font-size: 16px;
        font-weight: 600;
      }
    }
  }

  .preview-content {
    flex: 1;
    overflow: auto;
    padding: 0;
    position: relative;
  }

  .table-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 16px;
    background-color: #f9fafb;
    border-top: 1px solid #e5e7eb;

    .table-summary {
      display: flex;
      align-items: center;
      gap: 16px;

      .summary-item {
        display: flex;
        align-items: center;

        .summary-label {
          font-size: 13px;
          color: #6b7280;
          margin-right: 4px;
        }

        .summary-value {
          font-size: 13px;
          font-weight: 600;
          color: #374151;
        }
      }
    }

    .pagination-controls {
      display: flex;
      align-items: center;
      gap: 16px;

      .page-size-selector {
        display: flex;
        align-items: center;

        .page-size-label {
          font-size: 13px;
          color: #6b7280;
          margin-right: 4px;
        }

        .page-size-select {
          padding: 2px 6px;
          border: 1px solid #d1d5db;
          border-radius: 4px;
          background-color: white;
          color: #374151;
          font-size: 13px;
          cursor: pointer;
          outline: none;

          &:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
          }
        }
      }

      .page-navigation {
        display: flex;
        align-items: center;
        gap: 6px;

        .page-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 4px 8px;
          background-color: white;
          border: 1px solid #d1d5db;
          border-radius: 4px;
          color: #374151;
          cursor: pointer;
          font-size: 14px;
          transition: all 0.2s;

          &:hover:not(:disabled) {
            background-color: #f3f4f6;
            border-color: #9ca3af;
          }

          &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }

          &:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
          }
        }

        .page-jumper {
          display: flex;
          align-items: center;
          margin: 0 4px;

          .page-input {
            width: 40px;
            padding: 3px 6px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            text-align: center;
            color: #374151;
            font-size: 13px;

            &:focus {
              outline: none;
              border-color: #3b82f6;
              box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
            }

            &::-webkit-inner-spin-button,
            &::-webkit-outer-spin-button {
              -webkit-appearance: none;
              margin: 0;
            }
          }

          .page-separator {
            margin: 0 4px;
            color: #6b7280;
          }

          .total-pages {
            color: #6b7280;
          }
        }
      }
    }
  }

  .empty-data-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;

    .empty-icon {
      font-size: 36px;
      margin-bottom: 16px;
      color: #9ca3af;
    }

    .empty-title {
      margin: 0 0 8px 0;
      color: #4b5563;
      font-size: 16px;
      font-weight: 500;
    }

    .empty-desc {
      margin: 0;
      color: #6b7280;
      font-size: 14px;
      text-align: center;
    }
  }

  .error-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #ef4444;

    .icon-placeholder {
      font-size: 32px;
      margin-bottom: 10px;
      text-align: center;
    }

    span {
      font-size: 14px;
    }
  }

  /* 容器查询响应式样式 */
  @container table-preview (max-width: 768px) {
    .preview-header {
      flex-direction: column;
      align-items: flex-start;

      .preview-actions {
        margin-top: 12px;
        width: 100%;
        justify-content: space-between;
      }
    }

    .data-table,
    .column-structure-table {
      th, td {
        padding: 10px 12px;
      }
    }

    .data-table .column-header {
      .column-name {
        font-size: 14px;
      }

      .column-type {
        font-size: 10px;
      }
    }
  }

  @container table-preview (max-width: 640px) {
    .data-table,
    .column-structure-table {
      th, td {
        padding: 8px 10px;
        font-size: 12px;
      }
    }

    .data-table .column-header {
      .column-name {
        font-size: 12px;
      }

      .column-type {
        font-size: 9px;
        padding: 1px 4px;
      }
    }

    .pagination-controls {
      .page-info {
        font-size: 12px;
      }

      .page-btn {
        padding: 4px 8px;
        font-size: 12px;
      }
    }
  }

  @container table-preview (max-width: 480px) {
    .preview-header h4 {
      font-size: 14px;
    }

    .data-table,
    .column-structure-table {
      th, td {
        padding: 6px 8px;
        font-size: 11px;
      }
    }
  }
}

// 表格摘要样式
.table-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  padding: 12px 16px;
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

  .summary-info {
    display: flex;
    gap: 24px;

    .summary-item {
      display: flex;
      align-items: center;
      background-color: #eef2ff;
      padding: 6px 12px;
      border-radius: 4px;
      border-left: 3px solid #4f46e5;

      &.columns-count {
        border-left-color: #2563eb;
        background-color: #eff6ff;
      }

      &.rows-count {
        border-left-color: #0891b2;
        background-color: #ecfeff;
      }

      .summary-label {
        color: #4b5563;
        font-size: 13px;
        margin-right: 8px;
        font-weight: 500;
      }

      .summary-value {
        color: #111827;
        font-weight: 600;
        font-size: 15px;
      }
    }
  }
}

.data-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  margin-bottom: 0;

  th, td {
    padding: 12px 16px;
    text-align: left;
    font-size: 14px;
    line-height: 1.5;
  }

  th {
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
    font-weight: 600;
    color: #4b5563;
  }

  tbody td {
    border-bottom: 1px solid #f2f4f7;
    color: #1f2937;

    .cell-content {
      display: inline-block;
      max-width: 300px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      &.empty-value {
        color: #9ca3af;
        font-style: italic;
      }
    }
  }

  tbody tr:hover td {
    background-color: #f9fafb;
  }

  tfoot td {
    padding: 0;
    border: none;
  }

  .column-header {
    display: flex;
    flex-direction: column;

    .name-with-sort {
      display: flex;
      align-items: center;
      margin-bottom: 4px;

      .column-name {
        font-weight: 600;
        color: #374151;
      }

      .sort-icon {
        margin-left: 6px;
        font-size: 14px;
        color: #3b82f6;
      }
    }

    .column-type {
      font-size: 12px;
      padding: 2px 6px;
      background-color: #f3f4f6;
      border-radius: 4px;
      color: #6b7280;
      font-weight: 500;
      display: inline-block;
    }
  }

  .sortable {
    cursor: pointer;
    user-select: none;

    &:hover {
      background-color: #f3f4f6;

      .column-name {
        color: #3b82f6;
      }
    }

    &.asc, &.desc {
      .column-name {
        color: #2563eb;
      }
    }
  }
}
</style>
