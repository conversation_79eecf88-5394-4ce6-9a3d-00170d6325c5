<script lang="ts" setup>
import {computed, onMounted, ref, watch} from 'vue';
import {useMessageService} from '@/services/message';
import {useResponseHandler} from '@/utils/api';
import instance from "@/utils/axios";

// 组件属性
const props = defineProps<{
  visible: boolean;
  columnId: string;
  columnName: string;
  initialConfig?: any;
}>();

// 组件事件
const emit = defineEmits<{
  (e: 'close'): void;
  (e: 'save', config: any): void;
}>();

// 消息服务
const messageService = useMessageService();
const {handleResponse} = useResponseHandler();

// 组件状态
const isLoading = ref(false);
const isDecryptEnabled = ref(false);
const decryptType = ref('gm');
const aesKey = ref('');
const gmDecryptMode = ref('full'); // 'full' 或 'partial'
const gmJsonKeys = ref<string[]>([]);
const newJsonKey = ref('');

// 标题计算属性
const modalTitle = computed(() => {
  return `列字段解密配置 - ${props.columnName}`;
});

// 初始化表单数据
const initFormData = () => {
  console.log('初始化解密配置表单数据:', props.initialConfig);

  // 重置所有状态
  isDecryptEnabled.value = false;
  decryptType.value = 'gm';
  aesKey.value = '';
  gmDecryptMode.value = 'full';
  gmJsonKeys.value = [];
  newJsonKey.value = '';

  // 如果有初始配置，解析并设置
  if (props.initialConfig) {
    try {
      const config = typeof props.initialConfig === 'string'
          ? JSON.parse(props.initialConfig)
          : props.initialConfig;

      console.log('解析的解密配置:', config);

      // 检查是否启用了解密
      if (config.aes || config.gm) {
        isDecryptEnabled.value = true;

        if (config.aes) {
          decryptType.value = 'aes';
          aesKey.value = config.aes.key || '';
        } else if (config.gm) {
          decryptType.value = 'gm';
          if (config.gm.encrypt_json_key && Array.isArray(config.gm.encrypt_json_key)) {
            gmDecryptMode.value = 'partial';
            gmJsonKeys.value = [...config.gm.encrypt_json_key];
          } else {
            gmDecryptMode.value = 'full';
          }
        }
      }
    } catch (e) {
      console.error('解析解密配置失败:', e);
    }
  }
};

// 监听props变化，重新初始化表单
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    initFormData();
  }
}, {immediate: true});

// 监听初始配置变化
watch(() => props.initialConfig, () => {
  if (props.visible) {
    initFormData();
  }
});

// 初始化
onMounted(() => {
  initFormData();
});

// 添加JSON key
const addJsonKey = () => {
  if (newJsonKey.value.trim() && !gmJsonKeys.value.includes(newJsonKey.value.trim())) {
    gmJsonKeys.value.push(newJsonKey.value.trim());
    newJsonKey.value = '';
  }
};

// 移除JSON key
const removeJsonKey = (index: number) => {
  gmJsonKeys.value.splice(index, 1);
};

// 保存配置
const saveConfig = async () => {
  isLoading.value = true;

  try {
    let decryptConfig: any = {};

    // 如果启用了解密，构建配置对象
    if (isDecryptEnabled.value) {
      if (decryptType.value === 'aes') {
        if (!aesKey.value.trim()) {
          messageService.error('请输入AES密钥');
          return;
        }
        decryptConfig.aes = {
          key: aesKey.value.trim()
        };
      } else if (decryptType.value === 'gm') {
        decryptConfig.gm = {};
        if (gmDecryptMode.value === 'partial') {
          if (gmJsonKeys.value.length === 0) {
            messageService.error('部分解密模式下，请至少添加一个JSON字段');
            return;
          }
          decryptConfig.gm.encrypt_json_key = [...gmJsonKeys.value];
        }
        // 整体解密模式下，gm对象为空即可
      }
    }

    // 调用API更新解密配置
    const response = await instance.put(`/api/metadata/columns/${props.columnId}/decrypt-config`, decryptConfig);

    // 处理响应
    handleResponse(response, {
      showSuccessMessage: false,
      errorMessage: '解密配置保存失败'
    });

    // 显示成功消息
    messageService.success('解密配置保存成功');

    // 发送保存事件
    emit('save', decryptConfig);

    // 关闭弹窗
    emit('close');
  } catch (error) {
    console.error('保存解密配置失败:', error);
    messageService.error('保存解密配置失败: ' + (error instanceof Error ? error.message : String(error)));
  } finally {
    isLoading.value = false;
  }
};

// 取消
const cancel = () => {
  emit('close');
};
</script>

<template>
  <div v-if="visible" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl max-w-lg w-full max-h-[90vh] overflow-y-auto">
      <!-- 弹窗标题 -->
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">{{ modalTitle }}</h3>
      </div>

      <!-- 弹窗内容 -->
      <div class="px-6 py-4">
        <!-- 解密开关 -->
        <div class="mb-6">
          <div class="flex items-center">
            <input
                id="isDecryptEnabled"
                v-model="isDecryptEnabled"
                class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                type="checkbox"
            />
            <label class="ml-2 block text-sm text-gray-900" for="isDecryptEnabled">
              启用解密功能
            </label>
          </div>
          <p class="mt-1 text-sm text-gray-500">
            启用后，系统将对此列字段进行解密处理
          </p>
        </div>

        <!-- 解密配置 -->
        <div v-if="isDecryptEnabled" class="space-y-6">
          <!-- 解密算法选择 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-3">解密算法</label>
            <div class="flex items-center space-x-6">
              <div class="flex items-center">
                <input
                    id="gm"
                    v-model="decryptType"
                    class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                    type="radio"
                    value="gm"
                />
                <label class="ml-2 text-sm text-gray-700" for="gm">国密算法</label>
              </div>
              <div class="flex items-center">
                <input
                    id="aes"
                    v-model="decryptType"
                    class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                    type="radio"
                    value="aes"
                />
                <label class="ml-2 text-sm text-gray-700" for="aes">AES算法</label>
              </div>
            </div>
          </div>

          <!-- AES配置 -->
          <div v-if="decryptType === 'aes'" class="bg-gray-50 p-4 rounded-md">
            <label class="block text-sm font-medium text-gray-700 mb-2" for="aesKey">AES密钥</label>
            <input
                id="aesKey"
                v-model="aesKey"
                class="w-full px-3 py-2 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                placeholder="请输入AES密钥"
                type="password"
            />
            <p class="mt-1 text-xs text-gray-500">请输入用于AES解密的密钥</p>
          </div>

          <!-- 国密配置 -->
          <div v-if="decryptType === 'gm'" class="bg-gray-50 p-4 rounded-md space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-3">解密模式</label>
              <div class="space-y-2">
                <div class="flex items-center">
                  <input
                      id="gmFull"
                      v-model="gmDecryptMode"
                      class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                      type="radio"
                      value="full"
                  />
                  <label class="ml-2 text-sm text-gray-700" for="gmFull">整体解密</label>
                </div>
                <div class="flex items-center">
                  <input
                      id="gmPartial"
                      v-model="gmDecryptMode"
                      class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                      type="radio"
                      value="partial"
                  />
                  <label class="ml-2 text-sm text-gray-700" for="gmPartial">部分字段解密</label>
                </div>
              </div>
            </div>

            <!-- 部分字段解密配置 -->
            <div v-if="gmDecryptMode === 'partial'" class="space-y-3">
              <label class="block text-sm font-medium text-gray-700">需要解密的JSON字段</label>
              
              <!-- 添加字段 -->
              <div class="flex space-x-2">
                <input
                    v-model="newJsonKey"
                    class="flex-1 px-3 py-2 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    placeholder="输入字段名，如：name, age"
                    type="text"
                    @keyup.enter="addJsonKey"
                />
                <button
                    class="px-3 py-2 bg-indigo-600 text-white text-sm rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                    type="button"
                    @click="addJsonKey"
                >
                  添加
                </button>
              </div>

              <!-- 字段列表 -->
              <div v-if="gmJsonKeys.length > 0" class="space-y-2">
                <div
                    v-for="(key, index) in gmJsonKeys"
                    :key="index"
                    class="flex items-center justify-between bg-white px-3 py-2 rounded border"
                >
                  <span class="text-sm text-gray-700">{{ key }}</span>
                  <button
                      class="text-red-600 hover:text-red-800 text-sm"
                      type="button"
                      @click="removeJsonKey(index)"
                  >
                    删除
                  </button>
                </div>
              </div>

              <p class="text-xs text-gray-500">
                指定需要解密的JSON字段名称，系统将只对这些字段进行解密处理
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- 弹窗按钮 -->
      <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
        <button
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500"
            type="button"
            @click="cancel"
        >
          取消
        </button>
        <button
            :disabled="isLoading"
            class="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:opacity-50"
            type="button"
            @click="saveConfig"
        >
          {{ isLoading ? '保存中...' : '保存' }}
        </button>
      </div>
    </div>
  </div>
</template>
