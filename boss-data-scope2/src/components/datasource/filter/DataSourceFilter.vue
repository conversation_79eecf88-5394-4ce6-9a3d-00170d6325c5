<script setup lang="ts">
import { ref, watch } from 'vue'
import type { DataSourceType, DataSourceStatus } from '@/types/datasource'
import { DataSourceTypeEnum, DataSourceStatusEnum } from '@/types/datasource'

// 组件属性
const props = defineProps<{
  initialFilters?: {
    name?: string;
    type?: DataSourceType;
    status?: DataSourceStatus;
  }
}>()

// 组件事件
const emit = defineEmits<{
  (e: 'filter', filters: { name?: string; type?: DataSourceType; status?: DataSourceStatus }): void;
  (e: 'reset'): void;
}>()

// 过滤状态
const searchKeyword = ref(props.initialFilters?.name || '')
const typeFilter = ref<DataSourceType | ''>(props.initialFilters?.type || '')
const statusFilter = ref<DataSourceStatus | ''>(props.initialFilters?.status || '')

// 应用过滤器
const applyFilters = () => {
  // 调试信息
  console.log('[DataSourceFilter] 应用过滤器, 原始值:', {
    name: searchKeyword.value,
    type: typeFilter.value,
    status: statusFilter.value
  });

  // 处理过滤参数，确保空字符串转换为undefined
  const filters = {
    name: searchKeyword.value?.trim() || undefined,
    type: typeFilter.value || undefined,
    status: statusFilter.value || undefined
  };

  console.log('[DataSourceFilter] 发出过滤事件，处理后的值:', filters);

  // 发送过滤事件
  emit('filter', filters);
}

// 监听回车按键
const handleSearchInput = (event: KeyboardEvent) => {
  if (event.key === 'Enter') {
    applyFilters()
  }
}

// 清除所有过滤器
const clearFilters = () => {
  searchKeyword.value = ''
  typeFilter.value = ''
  statusFilter.value = ''
  emit('reset')
}

// 监听初始过滤器变化
watch(() => props.initialFilters, (newFilters) => {
  if (newFilters) {
    searchKeyword.value = newFilters.name || ''
    typeFilter.value = newFilters.type || ''
    statusFilter.value = newFilters.status || ''
  }
}, { immediate: true })
</script>

<template>
  <!-- 数据源过滤表单 -->
  <div>
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <!-- 搜索关键词 -->
      <div>
        <label for="search-keyword" class="block text-sm font-medium text-gray-700 mb-1">关键词</label>
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none" style="position: absolute !important; top: 0; bottom: 0; z-index: 1;">
            <i class="fas fa-search text-gray-400"></i>
          </div>
          <input
            id="search-keyword"
            v-model="searchKeyword"
            type="text"
            class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            :placeholder="searchKeyword ? '' : '搜索数据源名称...'"
            @keyup="handleSearchInput"
            @blur="applyFilters"
          />
        </div>
      </div>

      <!-- 类型过滤 -->
      <div>
        <label for="type-filter" class="block text-sm font-medium text-gray-700 mb-1">数据源类型</label>
        <div class="relative select-container">
          <select
            id="type-filter"
            v-model="typeFilter"
            class="block w-full pl-3 pr-10 py-2 border border-gray-300 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md appearance-none"
          >
            <option value="">所有类型</option>
            <option :value="DataSourceTypeEnum.MYSQL">MySQL</option>
            <option :value="DataSourceTypeEnum.POSTGRESQL">PostgreSQL</option>
            <option :value="DataSourceTypeEnum.ORACLE">Oracle</option>
            <option :value="DataSourceTypeEnum.SQLSERVER">SQL Server</option>
            <option :value="DataSourceTypeEnum.MONGODB">MongoDB</option>
            <option :value="DataSourceTypeEnum.ELASTICSEARCH">Elasticsearch</option>
          </select>
        </div>
      </div>

      <!-- 状态过滤 -->
      <div>
        <label for="status-filter" class="block text-sm font-medium text-gray-700 mb-1">状态</label>
        <div class="relative select-container">
          <select
            id="status-filter"
            v-model="statusFilter"
            class="block w-full pl-3 pr-10 py-2 border border-gray-300 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md appearance-none"
          >
            <option value="">不限</option>
            <option :value="DataSourceStatusEnum.ACTIVE">活跃</option>
            <option :value="DataSourceStatusEnum.INACTIVE">不活跃</option>
            <option :value="DataSourceStatusEnum.ERROR">错误</option>
            <option :value="DataSourceStatusEnum.SYNCING">同步中</option>
          </select>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex items-end space-x-2">
        <button
          @click="clearFilters"
          class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          <i class="fas fa-sync-alt mr-2"></i>
          重置筛选
        </button>
        <button
          @click="applyFilters"
          class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          <i class="fas fa-search mr-2"></i>
          查询
        </button>
      </div>
    </div>
  </div>
</template>