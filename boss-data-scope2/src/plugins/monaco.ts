// 使用自定义Monaco加载器
import { configureMonacoEnvironment } from '../utils/monaco-loader';

// 配置Monaco编辑器全局环境
configureMonacoEnvironment();

// 直接导入monaco-editor
// 使用 @vite-ignore 注释忽略动态导入警告
import * as monaco from 'monaco-editor';

// 注册SQL语言，提供基本的语法高亮
monaco.languages.register({ id: 'sql' });

// 配置SQL语言的语法高亮规则
monaco.languages.setMonarchTokensProvider('sql', {
  defaultToken: '',
  tokenPostfix: '.sql',

  keywords: [
    'SELECT', 'FROM', 'WHERE', 'AS', 'ORDER', 'BY', 'GROUP', 'HAVING', 'WITH',
    'AND', 'OR', 'NOT', 'IN', 'IS', 'NULL', 'LIKE', 'BETWEEN', 'JOIN', 'LEFT',
    'RIGHT', 'INNER', 'OUTER', 'FULL', 'ON', 'USING', 'UNION', 'ALL', 'DISTINCT',
    'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'ALTER', 'DROP', 'TABLE', 'VIEW',
    'INDEX', 'INTO', 'VALUES', 'SET', 'TRUNCATE', 'SCHEMA', 'DATABASE', 'TRIGGER',
    'PROCEDURE', 'FUNCTION', 'LIMIT', 'OFFSET', 'DESC', 'ASC', 'CASE', 'WHEN',
    'THEN', 'ELSE', 'END', 'EXISTS', 'CAST', 'COUNT', 'SUM', 'AVG', 'MIN', 'MAX'
  ],

  operators: [
    '+', '-', '*', '/', '%', '&', '|', '^', '~', '!', '=', '<', '>', '<=', '>=', '<>', '!=', '<=>',
    'AND', 'OR', 'NOT', 'LIKE', 'IN', 'IS', 'BETWEEN',
  ],

  builtinFunctions: [
    'AVG', 'COUNT', 'FIRST', 'LAST', 'MAX', 'MIN', 'SUM', 'UCASE', 'LCASE', 'MID', 'LEN',
    'ROUND', 'NOW', 'FORMAT', 'CAST', 'CONVERT', 'LOWER', 'UPPER', 'SUBSTRING', 'TRIM',
    'TO_CHAR', 'TO_DATE', 'TO_NUMBER', 'NVL', 'DECODE', 'REPLACE', 'FLOOR', 'CEIL', 'EXTRACT',
    'DATE_FORMAT', 'DATE_ADD', 'DATE_SUB', 'DATEDIFF', 'TIMESTAMPDIFF', 'YEAR', 'MONTH',
    'DAY', 'HOUR', 'MINUTE', 'SECOND', 'CONCAT', 'CONCAT_WS', 'IFNULL', 'COALESCE'
  ],

  builtinVariables: [
    'CURRENT_DATE', 'CURRENT_TIME', 'CURRENT_TIMESTAMP', 'CURRENT_USER', 'SESSION_USER', 'SYSTEM_USER'
  ],

  // 重要的SQL词法单元
  tokenizer: {
    root: [
      { include: '@whitespace' },
      { include: '@numbers' },
      { include: '@strings' },
      { include: '@comments' },

      // 识别关键字
      [/[a-zA-Z_]\w*/, {
        cases: {
          '@keywords': { token: 'keyword' },
          '@operators': { token: 'operator' },
          '@builtinFunctions': { token: 'predefined.sql' },
          '@builtinVariables': { token: 'predefined.sql' },
          '@default': { token: 'identifier' }
        }
      }],

      // 标点符号和操作符
      [/[;,.(){}[\]]/, 'delimiter'],
      [/[<>=!%&+\-*/|~^]/, 'operator'],
    ],

    whitespace: [
      [/\s+/, 'white']
    ],

    comments: [
      [/--.*/, 'comment'],
      [/\/\*/, 'comment', '@comment'],
    ],

    comment: [
      [/[^*/]+/, 'comment'],
      [/\*\//, 'comment', '@pop'],
      [/./, 'comment']
    ],

    numbers: [
      [/0[xX][0-9a-fA-F]*/, 'number'],
      [/[$][+-]*\d*(\.\d*)?/, 'number'],
      [/\d*\.\d+([eE][\-+]?\d+)?/, 'number'],
      [/\d+([eE][\-+]?\d+)?/, 'number']
    ],

    strings: [
      [/'/, 'string', '@stringLiteral'],
      [/"/, 'string', '@dblStringLiteral']
    ],

    stringLiteral: [
      [/[^']+/, 'string'],
      [/''/, 'string'],
      [/'/, 'string', '@pop']
    ],

    dblStringLiteral: [
      [/[^"]+/, 'string'],
      [/""/, 'string'],
      [/"/, 'string', '@pop']
    ]
  }
});

// 配置SQL的自动完成提示
monaco.languages.registerCompletionItemProvider('sql', {
  provideCompletionItems: (model, position) => {
    const suggestions = [
      ...['SELECT', 'FROM', 'WHERE', 'GROUP BY', 'ORDER BY', 'HAVING', 'LIMIT',
         'JOIN', 'LEFT JOIN', 'RIGHT JOIN', 'INNER JOIN', 'UNION', 'INSERT',
         'UPDATE', 'DELETE'].map(keyword => ({
        label: keyword,
        kind: monaco.languages.CompletionItemKind.Keyword,
        insertText: keyword,
      })),

      ...['COUNT', 'SUM', 'AVG', 'MIN', 'MAX', 'CONCAT', 'SUBSTRING', 'TO_CHAR',
         'DATE_FORMAT', 'NOW()'].map(func => ({
        label: func,
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: func,
      })),

      ...['AND', 'OR', 'NOT', 'IN', 'LIKE', 'BETWEEN', 'IS NULL', 'IS NOT NULL'].map(op => ({
        label: op,
        kind: monaco.languages.CompletionItemKind.Operator,
        insertText: op,
      }))
    ];

    return { suggestions };
  }
});

export default monaco;