/**
 * 模拟字段数据
 * 用于开发环境下的测试
 */
export const MOCK_FIELDS = [
  { name: "id", type: "String", label: "id" },
  { name: "name", type: "String", label: "名称" },
  { name: "description", type: "String", label: "描述" },
  { name: "type", type: "String", label: "类型" },
  { name: "host", type: "String", label: "主机" },
  { name: "port", type: "Integer", label: "端口" },
  { name: "database_name", type: "String", label: "数据库名" },
  { name: "schema", type: "String", label: "架构" },
  { name: "username", type: "String", label: "用户名" },
  { name: "created_at", type: "Date", label: "创建时间" },
  { name: "updated_at", type: "Date", label: "更新时间" }
];

/**
 * 模拟字段数据 - 简化版
 * 用于错误处理场景
 */
export const MOCK_SIMPLE_FIELDS = [
  { name: "id", type: "String", label: "id" },
  { name: "name", type: "String", label: "名称" },
  { name: "description", type: "String", label: "描述" },
  { name: "type", type: "String", label: "类型" },
  { name: "created_at", type: "Date", label: "创建时间" },
  { name: "updated_at", type: "Date", label: "更新时间" }
];