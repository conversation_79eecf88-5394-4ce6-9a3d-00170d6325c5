import instance from '@/utils/axios';
import type { VersionListResponse, VersionResponse, CreateVersionRequest } from '@/types/version';

/**
 * 获取查询的版本列表
 * @param queryId 查询ID
 */
export async function fetchQueryVersions(queryId: string): Promise<VersionListResponse> {
  try {
    const response = await instance.get(`/queries/${queryId}/versions`);
    return response.data;
  } catch (error) {
    console.error('获取查询版本失败:', error);
    throw error;
  }
}

/**
 * 获取特定版本信息
 * @param queryId 查询ID
 * @param versionId 版本ID
 */
export async function fetchVersionById(queryId: string, versionId: string): Promise<VersionResponse> {
  try {
    const response = await instance.get(`/queries/${queryId}/versions/${versionId}`);
    return response.data;
  } catch (error) {
    console.error('获取版本信息失败:', error);
    throw error;
  }
}

/**
 * 创建查询新版本
 * @param queryId 查询ID
 * @param versionData 版本数据
 */
export async function createQueryVersion(
  queryId: string, 
  versionData: CreateVersionRequest
): Promise<VersionResponse> {
  try {
    const response = await instance.post(`/queries/${queryId}/versions`, versionData);
    return response.data;
  } catch (error) {
    console.error('创建查询版本失败:', error);
    throw error;
  }
}

/**
 * 删除查询版本
 * @param queryId 查询ID
 * @param versionId 版本ID
 */
export async function deleteVersion(queryId: string, versionId: string): Promise<{ success: boolean; message?: string }> {
  try {
    const response = await instance.delete(`/queries/${queryId}/versions/${versionId}`);
    return response.data;
  } catch (error) {
    console.error('删除版本失败:', error);
    throw error;
  }
}

/**
 * 恢复到指定版本
 * @param queryId 查询ID
 * @param versionId 版本ID
 */
export async function restoreVersion(queryId: string, versionId: string): Promise<VersionResponse> {
  try {
    const response = await instance.post(`/queries/${queryId}/versions/${versionId}/restore`);
    return response.data;
  } catch (error) {
    console.error('恢复版本失败:', error);
    throw error;
  }
}
