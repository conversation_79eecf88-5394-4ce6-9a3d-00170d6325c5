/**
 * 枚举值管理服务
 * 用于创建、更新和查询枚举值选项列表
 */
import { message } from '@/services/message';
import { lowCodeConfig, enumServiceConfig } from '@/utils/config';
import { enumCache } from '@/utils/enumCache';
import http from "@/utils/http";

export interface EnumerationOption {
  key: string;
  value: string;
  hide?: boolean;
}

export interface Enumeration {
  id?: string;
  projectCode: string;
  name: string;
  code: string;
  content: EnumerationOption[];
}

/**
 * 创建枚举
 * @param enumData 枚举数据
 * @returns 创建成功返回的枚举对象
 */
export async function createEnumeration(enumData: Omit<Enumeration, 'id'>): Promise<Enumeration> {
  if (!enumData.projectCode) {
    throw new Error('项目代码不能为空');
  }

  try {
    // 生成请求路径
    const url = `${enumServiceConfig.baseUrl}${enumServiceConfig.apiBasePath}/create`;

    // 打印请求参数，便于调试
    console.log('创建枚举请求参数:', JSON.stringify(enumData, null, 2));

    // 发起请求
    const response = await http.post(url, enumData);

    console.log('创建枚举响应数据:', response.data);

    // 检查是否成功
    if (response.status === 200 && response.data) {
      if (response.data.code === "000000") {
        // 处理两种可能的成功响应格式
        let createdEnum: Enumeration;

        if (response.data.data) {
          // 有data字段的标准响应
          createdEnum = response.data.data;
        } else {
          // 没有data字段，但编码表示成功的情况
          // 构造一个基本枚举对象，确保后续流程能正常工作
          createdEnum = {
            id: `temp-${Date.now()}`, // 临时ID
            projectCode: enumData.projectCode,
            name: enumData.name,
            code: enumData.code,
            content: enumData.content
          };
          console.log('成功创建枚举，但API响应中没有返回枚举实体，使用构造的对象:', createdEnum);
        }

        // 创建成功后清除项目缓存，强制下次刷新
        enumCache.clear(enumData.projectCode);

        return createdEnum;
      } else {
        // 处理业务错误码
        const errorMsg = response.data.message || '创建枚举失败';
        const errorCode = response.data.code || 'UNKNOWN_ERROR';
        console.error(`创建枚举业务错误: ${errorCode} - ${errorMsg}`);

        // 创建一个包含API返回信息的错误对象
        const error: any = new Error(errorMsg);
        error.code = errorCode;
        error.apiResponse = response.data;
        throw error;
      }
    }

    throw new Error('创建枚举失败: 服务器响应无效');
  } catch (error) {
    console.error('创建枚举出错:', error);

    // 其他情况直接抛出原始错误
    throw error;
  }
}

/**
 * 更新枚举
 * @param enumData 枚举数据（包含id）
 * @returns 更新成功的枚举对象
 */
export async function updateEnumeration(enumData: Enumeration): Promise<Enumeration> {
  if (!enumData.id) {
    throw new Error('枚举ID不能为空');
  }

  if (!enumData.projectCode) {
    throw new Error('项目代码不能为空');
  }

  try {
    // 生成请求路径
    const url = `${enumServiceConfig.baseUrl}${enumServiceConfig.apiBasePath}/edit`;

    // 打印请求参数，便于调试
    console.log('更新枚举请求URL:', url);
    console.log('更新枚举请求参数:', JSON.stringify(enumData, null, 2));

    // 发起请求
    const response = await http.post(url, enumData);

    console.log('更新枚举响应数据:', response.data);

    // 检查是否成功
    if (response.status === 200 && response.data) {
      if (response.data.code === "000000") {
        // 更新成功后清除项目缓存，强制下次刷新
        enumCache.clear(enumData.projectCode);

        // 如果响应中包含更新后的枚举数据，则使用响应数据
        // 否则使用原始的enumData作为返回值
        const updatedEnum = response.data.data || enumData;

        return updatedEnum;
      } else {
        // 处理业务错误码
        const errorMsg = response.data.message || '更新枚举失败';
        const errorCode = response.data.code || 'UNKNOWN_ERROR';
        console.error(`更新枚举业务错误: ${errorCode} - ${errorMsg}`);

        // 创建一个包含API返回信息的错误对象
        const error: any = new Error(errorMsg);
        error.code = errorCode;
        error.apiResponse = response.data;
        throw error;
      }
    }

    throw new Error('更新枚举失败: 服务器响应无效');
  } catch (error) {
    console.error('更新枚举出错:', error);

    // 其他情况直接抛出原始错误
    throw error;
  }
}

/**
 * 根据ID获取枚举值选项列表
 * @param id 枚举值ID
 * @param projectCode 项目代码
 * @returns 枚举值对象
 */
export async function getEnumerationById(id: string, projectCode: string): Promise<Enumeration> {
  if (!projectCode) {
    throw new Error('项目代码不能为空');
  }

  try {
    const timestamp = new Date().getTime();
    const url = `${enumServiceConfig.baseUrl}${enumServiceConfig.apiBasePath}/get?_t=${timestamp}&id=${id}&projectCode=${projectCode}`;
    console.log('获取枚举详情URL:', url);

    const response = await http.get(url);

    console.log('获取枚举详情响应:', response.data);

    if (response.status === 200 && response.data && response.data.code === "000000") {
      return response.data.data;
    }

    throw new Error(`获取枚举值失败: ${response.data?.message || '未知错误'}`);
  } catch (error) {
    message.error(`获取枚举值失败: ${error instanceof Error ? error.message : '未知错误'}`);
    console.error('获取枚举值出错:', error);
    throw error;
  }
}

/**
 * 获取枚举列表
 * @param projectCode 项目代码（必需）
 * @param page 页码
 * @param pageSize 每页数量
 * @param keyword 搜索关键词（可选）
 * @returns 枚举列表和总数
 */
export async function listEnumerations(
  projectCode: string,
  page: number = 1,
  pageSize: number = 10,
  keyword?: string
): Promise<{ list: Enumeration[]; total: number }> {
  if (!projectCode) {
    throw new Error('项目代码不能为空');
  }

  console.log(`[枚举服务] 请求项目 ${projectCode} 的枚举列表, 页码: ${page}, 每页: ${pageSize}, 关键词: ${keyword || '(无)'}`);

  // 无关键词搜索时，尝试从缓存获取
  if (!keyword) {
    const cachedResult = enumCache.search(projectCode, keyword, page, pageSize);
    if (cachedResult) {
      console.log(`[枚举服务] 从缓存获取 ${projectCode} 的枚举列表, 共 ${cachedResult.total} 条`);
      return cachedResult;
    }
  }

  try {
    const timestamp = new Date().getTime();
    let url = `${enumServiceConfig.baseUrl}${enumServiceConfig.apiBasePath}/list?_t=${timestamp}`;

    // 添加查询参数
    const params = new URLSearchParams();
    params.append('projectCode', projectCode);
    if (keyword) {
      params.append('keyword', keyword);
    }
    params.append('page', String(page));
    params.append('pageSize', String(pageSize));

    // 将参数添加到URL
    if (params.toString()) {
      url += '&' + params.toString();
    }

    console.log('[枚举服务] 请求URL:', url);

    const response = await http.get(url);

    console.log('[枚举服务] 响应状态码:', response.status);

    // 检查响应结构
    if (response.data) {
      console.log('[枚举服务] 响应code:', response.data.code);
      console.log('[枚举服务] 响应message:', response.data.message);

      // 打印完整的响应数据
      console.log('[枚举服务] 完整响应数据:', JSON.stringify(response.data));

      if (response.data.data) {
        console.log('[枚举服务] 响应data类型:', typeof response.data.data);

        // 如果data是数字，可能是返回了总数
        if (typeof response.data.data === 'number') {
          console.log('[枚举服务] 数据为数字类型，可能表示总数:', response.data.data);
          return {
            list: [],
            total: response.data.data
          };
        }

        console.log('[枚举服务] 响应data结构:', Object.keys(response.data.data).join(', '));

        // 检查是否有直接的数组数据
        if (Array.isArray(response.data.data)) {
          console.log('[枚举服务] 数据是数组类型，长度:', response.data.data.length);
          return {
            list: response.data.data,
            total: response.data.data.length
          };
        }

        if (response.data.data.list) {
          console.log('[枚举服务] 列表长度:', response.data.data.list.length);
          if (response.data.data.list.length > 0) {
            const firstItem = response.data.data.list[0];
            console.log('[枚举服务] 第一项数据:', {
              id: firstItem.id,
              name: firstItem.name,
              code: firstItem.code,
              contentLength: firstItem.content ? firstItem.content.length : 0
            });

            // 如果有内容，打印第一个选项
            if (firstItem.content && firstItem.content.length > 0) {
              console.log('[枚举服务] 第一项第一个选项:', firstItem.content[0]);
            }
          }
        }
      }
    }

    if (response.status === 200 && response.data && response.data.code === "000000") {
      // 增加防御性检查，确保data字段存在
      if (!response.data.data) {
        console.log('[枚举服务] 响应中没有data字段或data为空，返回空结果');
        return {
          list: [],
          total: 0
        };
      }

      const result = {
        list: response.data.data.list || [],
        total: response.data.data.total || 0
      };

      // 详细日志
      console.log(`[枚举服务] 处理后的枚举列表: ${result.list.length ? '有数据' : '空列表'}, ` +
                 `总数: ${result.total}, ` +
                 `列表类型: ${Array.isArray(result.list) ? 'Array' : typeof result.list}`);

      // 保存完整结果到缓存（仅当不是关键词搜索时）
      if (!keyword && response.data.data.list && Array.isArray(response.data.data.list)) {
        enumCache.set(projectCode, response.data.data.list);
        console.log(`[枚举服务] 已缓存项目 ${projectCode} 的 ${response.data.data.list.length} 条枚举数据`);
      }

      return result;
    }

    // 处理特定错误类型
    if (response.data && response.data.code === "230401" && response.data.message.includes("AppPmcRelation not existed")) {
      console.error('[枚举服务] 项目码不存在错误:', response.data.message);
      throw new Error(`项目代码 "${projectCode}" 不存在或无权访问，请检查项目代码是否正确`);
    }

    console.error('[枚举服务] 获取枚举列表失败，服务器返回:', response.data?.code, response.data?.message);
    throw new Error(response.data?.message || '获取枚举列表失败');
  } catch (error) {
    console.error('[枚举服务] 获取枚举列表出错:', error);

    // 如果是我们自定义的错误消息，则直接抛出
    if (error instanceof Error && error.message.includes("不存在或无权访问")) {
      throw error;
    }

    // 尝试检查错误消息中是否包含"AppPmcRelation not existed"
    if (error instanceof Error &&
        (error.message.includes("AppPmcRelation not existed") ||
         error.message.includes("230401"))) {
      throw new Error(`项目代码 "${projectCode}" 不存在或无权访问，请检查项目代码是否正确`);
    }

    throw error;
  }
}

/**
 * 根据ID删除枚举值
 * @param id 枚举值ID
 * @param projectCode 项目代码
 * @returns 是否删除成功
 */
export async function deleteEnumeration(id: string, projectCode: string): Promise<boolean> {
  if (!id) {
    throw new Error('枚举值ID不能为空');
  }

  if (!projectCode) {
    throw new Error('项目代码不能为空');
  }

  try {
    const timestamp = new Date().getTime();
    const url = `${enumServiceConfig.baseUrl}${enumServiceConfig.apiBasePath}/delete?_t=${timestamp}&id=${id}&projectCode=${projectCode}`;
    console.log('删除枚举URL:', url);

    const response = await http.get(url);

    console.log('删除枚举响应:', response.data);

    if (response.status === 200 && response.data && response.data.code === "000000") {
      message.success('删除枚举值成功');

      // 删除成功后清除项目缓存，强制下次刷新
      enumCache.clear(projectCode);

      return true;
    }

    message.error(`删除枚举值失败: ${response.data?.message || '未知错误'}`);
    return false;
  } catch (error) {
    message.error(`删除枚举值失败: ${error instanceof Error ? error.message : '未知错误'}`);
    console.error('删除枚举值出错:', error);
    return false;
  }
}

/**
 * 将参数的选项列表转换为枚举值格式
 * @param options 参数选项列表
 * @param name 枚举名称
 * @param code 枚举代码
 * @param projectCode 项目代码
 * @returns 枚举值对象
 */
export function convertOptionsToEnumeration(
  options: Array<{label: string, value: string}>,
  name: string,
  code: string,
  projectCode: string
): Enumeration {
  if (!projectCode) {
    throw new Error('项目代码不能为空');
  }

  const content: EnumerationOption[] = options.map(option => ({
    key: option.value,
    value: option.label,
    hide: false
  }));

  return {
    projectCode,
    name,
    code,
    content
  };
}

/**
 * 将枚举值内容转换为参数选项列表格式
 * @param enumeration 枚举值对象
 * @returns 参数选项列表
 */
export function convertEnumerationToOptions(enumeration: Enumeration): Array<{label: string, value: string}> {
  if (!enumeration.content || !Array.isArray(enumeration.content)) {
    return [];
  }

  return enumeration.content
    .filter(item => !item.hide)
    .map(item => ({
      label: item.value,
      value: item.key
    }));
}
