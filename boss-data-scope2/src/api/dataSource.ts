import instance from '@/utils/axios';
import type { DataSource } from '@/types/datasource';

/**
 * 获取数据源列表
 */
export async function fetchDataSources(): Promise<{ dataSources: DataSource[] }> {
  try {
    const response = await instance.get('/api/datasources');
    return response.data;
  } catch (error) {
    console.error('获取数据源列表失败:', error);
    throw error;
  }
}

/**
 * 获取特定数据源信息
 * @param id 数据源ID
 */
export async function fetchDataSourceById(id: string): Promise<{ dataSource: DataSource }> {
  try {
    const response = await instance.get(`/api/datasources/${id}`);
    return response.data;
  } catch (error) {
    console.error('获取数据源详情失败:', error);
    throw error;
  }
}

/**
 * 创建数据源
 * @param dataSource 数据源数据
 */
export async function createDataSource(dataSource: Partial<DataSource>): Promise<{ dataSource: DataSource }> {
  try {
    const response = await instance.post('/api/datasources', dataSource);
    return response.data;
  } catch (error) {
    console.error('创建数据源失败:', error);
    throw error;
  }
}

/**
 * 更新数据源
 * @param id 数据源ID
 * @param dataSource 数据源数据
 */
export async function updateDataSource(id: string, dataSource: Partial<DataSource>): Promise<{ dataSource: DataSource }> {
  try {
    const response = await instance.put(`/api/datasources/${id}`, dataSource);
    return response.data;
  } catch (error) {
    console.error('更新数据源失败:', error);
    throw error;
  }
}

/**
 * 删除数据源
 * @param id 数据源ID
 */
export async function deleteDataSource(id: string): Promise<{ success: boolean }> {
  try {
    const response = await instance.delete(`/api/datasources/${id}`);
    return response.data;
  } catch (error) {
    console.error('删除数据源失败:', error);
    throw error;
  }
}

/**
 * 测试数据源连接
 * @param dataSource 数据源配置
 */
export async function testDataSourceConnection(dataSource: Partial<DataSource>): Promise<{ success: boolean; message?: string }> {
  try {
    const response = await instance.post('/api/datasources/test', dataSource);
    return response.data;
  } catch (error) {
    console.error('测试数据源连接失败:', error);
    throw error;
  }
}

/**
 * 刷新数据源元数据
 * @param id 数据源ID
 */
export async function refreshDataSourceMetadata(id: string): Promise<{ success: boolean }> {
  try {
    const response = await instance.post(`/api/datasources/${id}/refresh`);
    return response.data;
  } catch (error) {
    console.error('刷新数据源元数据失败:', error);
    throw error;
  }
}
