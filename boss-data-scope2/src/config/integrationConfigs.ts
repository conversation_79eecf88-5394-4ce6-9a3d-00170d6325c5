import type { TableConfig, ChartConfig, IntegrationType, IntegrationStatus } from '@/types/unified-integration';

// 默认表格配置
export const DEFAULT_TABLE_CONFIG: TableConfig = {
  columns: [],
  pagination: { 
    enabled: true,
    pageSize: 10, 
    showSizeChanger: true,
    pageSizeOptions: [10, 20, 50, 100]
  },
  actions: [],
  export: {
    enabled: true,
    formats: ['CSV', 'EXCEL'],
    maxRows: 1000
  },
  batchActions: [],
  aggregation: {
    enabled: false,
    groupByFields: [],
    aggregationFunctions: []
  },
  advancedFilters: {
    enabled: false,
    defaultFilters: [],
    savedFilters: []
  },
  rowSelection: false,
  showHeader: true,
  bordered: true,
  showFooter: false,
  rowActionsFixed: 'right' // 默认行操作列固定在右侧
};

// 默认图表配置
export const DEFAULT_CHART_CONFIG: ChartConfig = {
  type: 'bar',
  title: '',
  description: '',
  height: 400,
  showLegend: true,
  animation: true,
  theme: 'default',
  dataMapping: {
    xField: '',
    yField: '',
    seriesField: '',
    valueField: '',
    categoryField: '',
    sizeField: '',
    colorField: '',
    nameField: ''
  },
  styleOptions: {
    backgroundColor: '',
    colors: []
  },
  interactions: {
    enableZoom: false,
    enablePan: false,
    tooltipMode: 'single'
  }
};

// 集成类型选项
export const INTEGRATION_TYPES = [
  {
    value: 'TABLE',
    label: '高级表格',
    description: '支持复杂条件查询，查询结果支持丰富的展示与操作'
  },
  {
    value: 'SIMPLE_TABLE',
    label: '简单表格',
    description: '简易版表格集成，提供基本的数据展示功能。'
  },
  {
    value: 'CHART',
    label: '数据图表',
    description: '支持柱状图、折线图、饼图、散点图等多种图表类型。',
    disabled: true // 图表集成本期暂不上线
  }
];

// 默认集成对象
export const DEFAULT_INTEGRATION = {
  id: '',
  name: '',
  description: '',
  type: 'TABLE' as IntegrationType,
  status: 'DRAFT' as IntegrationStatus,
  dataSourceId: '',
  queryId: '',
  parameters: [],
  tableConfig: DEFAULT_TABLE_CONFIG,
  chartConfig: DEFAULT_CHART_CONFIG,
  createTime: '',
  updateTime: ''
};

// 图标配置
export const INTEGRATION_TYPE_ICONS = {
  'TABLE': { icon: 'fas fa-table', bgClass: 'bg-blue-100 text-blue-600' },
  'SIMPLE_TABLE': { icon: 'fas fa-list', bgClass: 'bg-green-100 text-green-600' },
  'CHART': { icon: 'fas fa-chart-bar', bgClass: 'bg-purple-100 text-purple-600' },
  'FORM': { icon: 'fas fa-wpforms', bgClass: 'bg-yellow-100 text-yellow-600' }
};