/**
 * 查询参数接口定义
 */

/**
 * 查询参数接口中的参数对象
 */
export interface QueryParameterObject {
  id?: string;
  name: string;
  type: string;
  label?: string;
  defaultValue?: any;
  required?: boolean;
  options?: Array<{label: string, value: string}>;
  tableName?: string;
  isEncrypted?: boolean;
}

/**
 * API响应中的查询参数接口
 */
export interface QueryParametersResponse {
  success: boolean;
  code: number;
  message: string;
  data: {
    sql?: string;
    dataSourceId?: string;
    parameters: QueryParameterObject[];
    fields?: Array<{
      name: string;
      type: string;
      label: string;
    }>;
  };
}

/**
 * 查询字段接口
 */
export interface QueryField {
  name: string;
  type: string;
  label: string;
  tableName?: string;
  isEncrypted?: boolean;
}

/**
 * 将API参数转换为统一参数的映射规则
 */
export interface ParamMappingRule {
  sourceField: string;
  targetField: string;
  transformer?: (value: any) => any;
}

/**
 * 参数值对象
 */
export interface ParamValues {
  [key: string]: any;
} 