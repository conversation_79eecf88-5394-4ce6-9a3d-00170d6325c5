// 统一的集成类型定义文件 - 兼容不同组件中的类型

import { ColumnAlign, ChartType, ChartTheme } from '@/types/integration';
import type { ChartStyleOptions, ChartInteractions } from '@/types/integration';

// 集成类型定义
export type IntegrationType = 'TABLE' | 'SIMPLE_TABLE' | 'CHART';

// 集成状态定义
export type IntegrationStatus = 'DRAFT' | 'ACTIVE' | 'INACTIVE';

// 表格列定义
export interface TableColumn {
  field: string;
  label: string;
  type: string;
  format?: string;
  visible: boolean;
  align: ColumnAlign;
  width?: string | number; // 兼容字符串和数字类型的宽度
  sortable?: boolean;
  filterable?: boolean;
  displayOrder?: number;
  displayType?: string;
  helpText?: string;
  isNewColumn?: boolean;
  [key: string]: any;
}

// 分页配置
export interface PaginationConfig {
  enabled: boolean;
  pageSize: number;
  pageSizeOptions: number[];
  showSizeChanger?: boolean;
}

// 导出配置
export interface ExportConfig {
  enabled: boolean;
  formats: string[];
  maxRows: number;
}

// 聚合配置
export interface AggregationConfig {
  enabled: boolean;
  groupByFields: string[];
  aggregationFunctions: any[];
}

// 高级筛选配置
export interface AdvancedFiltersConfig {
  enabled: boolean;
  defaultFilters: any[];
  savedFilters: any[];
}

// 表格配置定义
export interface TableConfig {
  columns: TableColumn[];
  actions: any[];
  pagination: PaginationConfig;
  export: ExportConfig;
  batchActions: any[];
  aggregation: AggregationConfig;
  advancedFilters: AdvancedFiltersConfig;
  rowSelection?: boolean;
  showHeader?: boolean;
  bordered?: boolean;
  showFooter?: boolean;
  rowActionsFixed?: 'left' | 'right' | false; // 行操作列是否固定，以及固定位置
}

// 图表数据映射配置
export interface ChartDataMapping {
  xField?: string;
  yField?: string;
  seriesField?: string;
  valueField?: string;
  categoryField?: string;
  sizeField?: string;
  colorField?: string;
  nameField?: string;
}

// 图表配置定义
export interface ChartConfig {
  type: string;
  title?: string;
  description?: string;
  height?: number;
  showLegend?: boolean;
  animation?: boolean;
  theme?: string;
  dataMapping: ChartDataMapping;
  styleOptions?: ChartStyleOptions;
  interactions?: ChartInteractions;
  [key: string]: any;
}

// 查询参数定义
export interface QueryParam {
  name: string;
  type: string;
  description: string;
  format: string;
  formType: string;
  required: boolean;
  displayOrder: number;
  isNewParam?: boolean;
  options?: Array<{label: string, value: string}>;
  minLength?: number;
  maxLength?: number;
  minValue?: number;
  maxValue?: number;
  defaultValue?: any;
  placeholder?: string;
  allowMultiple?: boolean;
  fuzzyMatch?: boolean;
  dateFormat?: string;
  searchable?: boolean;
  multiSelect?: boolean;
  validationRegex?: string;
  validationMessage?: string;
  maxDateSpan?: number;
  disablePastDates?: boolean;
  disableFutureDates?: boolean;
  rangeSeparator?: string;
  rangePresets?: Array<{label: string, startOffset: number, endOffset: number}>;
  defaultStartOffset?: number;
  defaultEndOffset?: number;
  [key: string]: any;
}

// 表单配置
export interface FormConfig {
  layout?: string;
  conditions?: any[];
  buttons?: any[];
  [key: string]: any;
}

// 版本信息接口
export interface VersionInfo {
  id: string;
  currentVersion?: {
    id: string;
    versionNumber: number;
    isLatest: boolean;
    status: string;
  };
  [key: string]: any;
}

// API接口定义
export interface ApiDefinition {
  /** HTTP请求方法：GET, POST, PUT, DELETE等 */
  method: string;
  /** 
   * API路径，可以包含路径参数占位符，如: /api/queries/{id}/execute
   * 其中 {id} 就是查询ID，占位符将在实际请求时被替换为实际的查询ID值
   * 
   * 支持的路径格式示例（基于API文档）：
   * - /api/queries/{id}/execute - 执行查询 (POST)
   * - /api/queries/{queryId}/versions/{versionId}/execute - 执行查询特定版本 (POST)
   * - /api/integrations/{id}/preview - 预览集成 (GET)
   * - /api/integrations/execute-query - 执行集成查询 (POST)
   * - /api/datasources/{id}/sync - 同步数据源元数据 (POST)
   * - /api/metadata/tables/{tableId}/datas - 获取表数据 (GET)
   */
  path: string;
}

// APIs映射
export interface ApiMap {
  [key: string]: ApiDefinition;
}

// 元数据
export interface MetaData {
  database?: string;
  schema?: string;
  table?: string;
  pageCode?: string;
  apis?: ApiMap;
  /** 标记该集成不需要查询参数 */
  noParamsRequired?: boolean;
}

// 集成对象定义
export interface IntegrationData {
  id: string;
  name: string;
  description: string;
  type: IntegrationType;
  status: IntegrationStatus;
  dataSourceId: string;
  queryId: string;
  versionId?: string | VersionInfo; // 支持字符串ID或完整版本信息对象
  tableConfig?: TableConfig;
  chartConfig?: ChartConfig;
  queryParams?: QueryParam[];
  parameters?: QueryParam[];
  createTime?: string;
  updateTime?: string;
  formConfig?: FormConfig;
  meta?: MetaData;
  [key: string]: any;
}