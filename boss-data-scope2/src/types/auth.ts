import type { BaseResponse } from '@/types/common';

/**
 * 获取用户信息返回值
 */
export interface GetUserProfileResponse extends BaseResponse {
  data: {
    id: string;
    loginName: string;
    username: string;
    email: string;
    login_url: string;
  };
}

/**
 * 权限申请状态
 */
export type AuthRequestStatus = 'PENDING' | 'APPROVED' | 'REJECTED' | 'EXPIRED';

/**
 * 权限申请类型
 */
export type AuthRequestType = 'DATASOURCE' | 'SCHEMA' | 'TABLE' | 'COLUMN';

/**
 * 用户权限状态
 */
export interface UserPermissionStatus {
  hasPermission: boolean;
  requestStatus?: AuthRequestStatus;
  requestId?: string;
  requestTime?: string;
  approvalTime?: string;
  expiryTime?: string;
  reason?: string;
}

/**
 * 权限申请请求
 */
export interface AuthRequest {
  id?: string;
  resourceType: AuthRequestType;
  resourceId: string;
  resourceName: string;
  reason: string;
  userId?: string;
  userName?: string;
  status?: AuthRequestStatus;
  requestTime?: string;
  approvalTime?: string;
  approvedBy?: string;
  rejectionReason?: string;
  expiryTime?: string;
}

/**
 * 权限申请响应
 */
export interface AuthRequestResponse extends BaseResponse {
  data: AuthRequest;
}

/**
 * 用户权限查询响应
 */
export interface UserPermissionResponse extends BaseResponse {
  data: UserPermissionStatus;
}
