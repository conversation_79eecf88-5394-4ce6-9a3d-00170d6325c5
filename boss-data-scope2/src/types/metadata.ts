/**
 * 列元数据定义
 */
export interface ColumnConstraints {
  primaryKey: boolean;
  foreignKey: boolean;
  unique: boolean;
  referencedTable?: string;
  referencedColumn?: string;
}

export interface ColumnMetadata {
  id: string;
  name: string;
  dataType: string;
  columnType: string;
  position: number;
  isNullable: boolean;
  isPrimaryKey: boolean;
  isUnique: boolean;
  isIndexed: boolean;
  defaultValue: string | null;
  characterLength: number | null;
  numericPrecision: number | null;
  numericScale: number | null;
  description: string | null;
}

/**
 * 表元数据定义
 */
export interface TableMetadata {
  id: string;
  name: string;
  type: string;
  schema?: string;
  comment?: string;
  columns: ColumnMetadata[];
  indexes?: IndexMetadata[];
  error?: string;
}

/**
 * 索引元数据定义
 */
export interface IndexMetadata {
  /**
   * 索引名称
   */
  name: string;
  
  /**
   * 索引类型
   */
  type: 'BTREE' | 'HASH' | 'FULLTEXT' | 'OTHER';
  
  /**
   * 索引列
   */
  columns: string[];
  
  /**
   * 是否唯一
   */
  unique: boolean;
}

/**
 * 元数据定义
 */
export interface Metadata {
  /**
   * 表元数据列表
   */
  tables: TableMetadata[];
  
  /**
   * 视图元数据列表
   */
  views?: TableMetadata[];
  
  /**
   * 数据库版本
   */
  databaseVersion?: string;
  
  /**
   * 数据库产品名称
   */
  databaseProductName?: string;
  
  /**
   * 上次同步时间
   */
  lastSyncTime?: string;
}

/**
 * 扩展的表元数据定义
 */
export interface ExtendedTableMetadata extends TableMetadata {
  isUpdating: boolean;
  lastUpdated: string;
  error?: string;
}

/**
 * 元数据状态定义
 */
export interface MetadataState {
  loading: boolean;
  error: Error | null;
  tables: ExtendedTableMetadata[];
  expandedTables: Set<string>;
  searchTerm: string;
  highlightedTable?: string;
  schemas: any[];
}

/**
 * 表关系定义
 */
export interface TableRelationship {
  /**
   * 关系ID
   */
  id: string;
  
  /**
   * 源表
   */
  sourceTable: string;
  
  /**
   * 源列
   */
  sourceColumn: string;
  
  /**
   * 目标表
   */
  targetTable: string;
  
  /**
   * 目标列
   */
  targetColumn: string;
  
  /**
   * 关系类型
   */
  relationType: 'ONE_TO_ONE' | 'ONE_TO_MANY' | 'MANY_TO_ONE' | 'MANY_TO_MANY';
  
  /**
   * 是否为外键约束
   */
  isForeignKey: boolean;
  
  /**
   * 自定义关系名称
   */
  name?: string;
  
  /**
   * 关系描述
   */
  description?: string;
}

/**
 * 新增：Schema元数据接口
 */
export interface SchemaMetadata {
  value: string;  // schema的实际值，用于API调用
  name: string;   // schema的显示名称
  dataSourceId?: string; // 关联的数据源ID
}

/**
 * 数据库元数据接口 - 整个数据库的元数据集合
 */
export interface DatabaseMetadata {
  tables: TableMetadata[];
  relationships: TableRelationship[];
  schemas?: SchemaMetadata[]; // 新增：可用的schema列表
}

export interface TablesResponse {
  success: boolean;
  data: TableMetadata[];
  message?: string;
}

export interface ColumnsResponse {
  success: boolean;
  data: ColumnMetadata[];
  message?: string;
}

export type TableMetadataArray = TableMetadata[];
export type ExtendedTableMetadataArray = ExtendedTableMetadata[];
export type ColumnMetadataArray = ColumnMetadata[];

// 默认值
export const DEFAULT_COLUMN_CONSTRAINTS: ColumnConstraints = {
  primaryKey: false,
  foreignKey: false,
  unique: false,
  referencedTable: undefined,
  referencedColumn: undefined
};