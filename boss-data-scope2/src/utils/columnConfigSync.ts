import type { TableColumn } from '../types/integration';

/**
 * 将config对象中的属性同步到顶级属性
 * @param column 列配置对象
 */
export const syncConfigToTopLevel = (column: TableColumn) => {
  if (!column || !column.config) return;
  
  // 从config对象同步到顶级属性
  if (column.config.truncate !== undefined) {
    (column as any).truncate = column.config.truncate;
  }
  if (column.config.fixedPoint !== undefined) {
    (column as any).fixedPoint = column.config.fixedPoint;
  }
  if (column.config.thousandSeparator !== undefined) {
    (column as any).thousandSeparator = column.config.thousandSeparator;
  }
  // 始终同步help到helpText，即使是空字符串
  column.helpText = column.config.help !== undefined ? column.config.help : '';
  
  console.log('[同步] 已将config对象属性同步到顶级属性:', column);
};

/**
 * 将顶级属性同步回config对象
 * @param column 列配置对象
 */
export const syncTopLevelToConfig = (column: TableColumn) => {
  if (!column) return;
  
  // 确保config对象存在
  if (!column.config) {
    column.config = {};
  }
  
  // 先备份原来的config对象内容便于调试
  const originalConfig = { ...column.config };
  
  // 从顶级属性同步到config对象
  if ((column as any).truncate !== undefined) {
    column.config.truncate = (column as any).truncate;
  }
  if ((column as any).fixedPoint !== undefined) {
    column.config.fixedPoint = (column as any).fixedPoint;
  }
  if ((column as any).thousandSeparator !== undefined) {
    column.config.thousandSeparator = (column as any).thousandSeparator;
  }
  // 始终同步helpText到config.help，即使是空字符串
  column.config.help = column.helpText !== undefined ? column.helpText : '';
  
  // 同步枚举相关配置
  if (column.enumCode) {
    column.config.enumKey = column.enumCode;
  }
  if (column.enumDisplay) {
    column.config.enumDisplay = column.enumDisplay;
  }
  if (column.defaultEnumValue) {
    column.config.defaultEnumValue = column.defaultEnumValue;
  }
  
  // 记录同步前后的变化
  console.log('[同步] 已将顶级属性同步回config对象:', 
    '\n原先config:', originalConfig, 
    '\n更新后config:', column.config);
  
  // 确保同步后的column对象也被正确打印
  console.log('[同步] 同步后的完整列对象(深拷贝):', JSON.parse(JSON.stringify(column)));
};