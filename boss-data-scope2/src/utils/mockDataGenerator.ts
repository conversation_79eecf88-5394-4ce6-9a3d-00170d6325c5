import { formatDate } from '@/utils/formatter';
import { ChartType, ColumnAlign, ColumnDisplayType } from '@/types/integration';

/**
 * 生成模拟表格数据
 * @param count 数据条数
 * @returns 模拟数据数组
 */
export function generateMockTableData(count: number) {
  return Array.from({ length: count }, (_, i) => ({
    id: i + 1,
    date: formatDate(new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)),
    product: ['笔记本电脑', '智能手机', '平板电脑', '智能手表', '无线耳机'][Math.floor(Math.random() * 5)],
    quantity: Math.floor(Math.random() * 100) + 1,
    amount: (Math.random() * 10000 + 500).toFixed(2),
    status: ['已完成', '进行中', '已取消'][Math.floor(Math.random() * 3)],
    customer: ['张三', '李四', '王五', '赵六', '钱七'][Math.floor(Math.random() * 5)],
    department: ['销售部', '市场部', '技术部', '客服部', '财务部'][Math.floor(Math.random() * 5)]
  }));
}

/**
 * 生成模拟图表数据
 * @param chartType 图表类型
 * @param dataSize 数据点数量
 * @returns 模拟数据数组
 */
export function generateMockChartData(chartType: string, dataSize: number) {
  if (chartType === 'pie') {
    // 饼图数据
    return Array.from({ length: 5 }, (_, i) => ({
      category: `分类${i + 1}`,
      value: Math.floor(Math.random() * 1000)
    }));
  } else if (chartType === 'line' || chartType === 'bar') {
    // 折线图和柱状图数据
    const categories = Array.from({ length: dataSize }, (_, i) => `类别${i + 1}`);
    const series = ['系列A', '系列B', '系列C'];
    
    const result: any[] = [];
    
    categories.forEach(category => {
      series.forEach(series => {
        result.push({
          category,
          series,
          value: Math.floor(Math.random() * 1000)
        });
      });
    });
    
    return result;
  } else {
    // 其他类型图表
    return Array.from({ length: dataSize }, (_, i) => ({
      x: Math.random() * 100,
      y: Math.random() * 100,
      category: `分类${i % 3 + 1}`
    }));
  }
}

/**
 * 生成模拟表格配置
 * @returns 表格配置对象
 */
export function generateMockTableConfig() {
  return {
    columns: [
      { field: 'id', label: 'ID', type: 'number', visible: true, sortable: true, displayOrder: 0, align: ColumnAlign.LEFT, displayType: ColumnDisplayType.TEXT },
      { field: 'date', label: '日期', type: 'date', visible: true, sortable: true, displayOrder: 1, align: ColumnAlign.LEFT, displayType: ColumnDisplayType.DATE },
      { field: 'product', label: '产品', type: 'string', visible: true, sortable: true, displayOrder: 2, align: ColumnAlign.LEFT, displayType: ColumnDisplayType.TEXT },
      { field: 'quantity', label: '数量', type: 'number', visible: true, sortable: true, displayOrder: 3, align: ColumnAlign.RIGHT, displayType: ColumnDisplayType.NUMBER },
      { field: 'amount', label: '金额', type: 'currency', visible: true, sortable: true, displayOrder: 4, align: ColumnAlign.RIGHT, displayType: ColumnDisplayType.TEXT },
      { field: 'status', label: '状态', type: 'enum', visible: true, sortable: true, displayOrder: 5, align: ColumnAlign.CENTER, displayType: ColumnDisplayType.TAG },
      { field: 'customer', label: '客户', type: 'string', visible: true, sortable: true, displayOrder: 6, align: ColumnAlign.LEFT, displayType: ColumnDisplayType.TEXT },
      { field: 'department', label: '部门', type: 'string', visible: true, sortable: true, displayOrder: 7, align: ColumnAlign.LEFT, displayType: ColumnDisplayType.TEXT }
    ],
    actions: [
      { type: 'link', label: '查看', handler: 'viewRecord', icon: 'eye', style: 'primary' },
      { type: 'link', label: '编辑', handler: 'editRecord', icon: 'edit', style: 'secondary' },
      { type: 'link', label: '删除', handler: 'deleteRecord', icon: 'trash', style: 'danger', confirm: true, confirmMessage: '确定要删除此记录吗？' }
    ],
    pagination: {
      enabled: true,
      pageSize: 10,
      pageSizeOptions: [10, 20, 50, 100]
    },
    export: {
      enabled: true,
      formats: ['CSV', 'EXCEL'],
      maxRows: 1000
    },
    batchActions: [],
    aggregation: {
      enabled: false,
      groupByFields: [],
      aggregationFunctions: []
    },
    advancedFilters: {
      enabled: false,
      defaultFilters: [],
      savedFilters: []
    }
  };
}

/**
 * 生成模拟图表配置
 * @returns 图表配置对象
 */
export function generateMockChartConfig() {
  return {
    type: 'bar' as ChartType,
    title: '销售分析图表',
    description: '按月度显示销售数据',
    height: 400,
    animation: true,
    showLegend: true,
    dataMapping: {
      xField: 'category',
      yField: 'value',
      seriesField: 'series'
    },
    styleOptions: {
      colors: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de']
    }
  };
}

/**
 * 生成模拟表单查询参数
 * @returns 查询参数数组
 */
export function generateMockQueryParams() {
  return [
    {
      name: 'product',
      type: 'STRING',
      format: 'string',
      formType: 'input',
      required: false,
      defaultValue: '',
      description: '产品名称',
      displayOrder: 1
    },
    {
      name: 'status',
      type: 'SELECT',
      format: 'enum',
      formType: 'select',
      required: false,
      defaultValue: '',
      description: '订单状态',
      displayOrder: 2,
      options: [
        { label: '全部', value: '' },
        { label: '已完成', value: '已完成' },
        { label: '进行中', value: '进行中' },
        { label: '已取消', value: '已取消' }
      ]
    },
    {
      name: 'dateRange',
      type: 'DATE',
      format: 'date',
      formType: 'date',
      required: false,
      defaultValue: '',
      description: '订单日期',
      displayOrder: 3
    }
  ];
}