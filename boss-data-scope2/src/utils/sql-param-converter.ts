/**
 * SQL参数格式转换工具
 * 
 * 用于统一处理SQL查询语句中的参数占位符格式，将不同格式的参数占位符转换为后端API支持的格式。
 * 目前支持的转换：
 * - #{param} → ${param}
 * - list 关键字 → LIKE 关键字
 * - '%${param}%' → 处理为合适的参数化格式
 */

/**
 * 统一处理SQL参数占位符，将 #{param} 转换为 ${param}
 * @param sql 原始SQL语句
 * @returns 处理后的SQL语句
 */
export function normalizeParameterPlaceholders(sql: string): string {
  if (!sql) return sql;
  
  // 将 #{param} 转换为 ${param}
  return sql.replace(/\#\{([^}]+)\}/g, '${$1}');
}

/**
 * 处理SQL中的关键字，将不规范的关键字转换为标准形式
 * 目前支持的转换：
 * - list → LIKE
 * @param sql 原始SQL语句
 * @returns 处理后的SQL语句
 */
export function normalizeKeywords(sql: string): string {
  if (!sql) return sql;
  
  // 将 list 关键字转换为 LIKE
  return sql.replace(/\blist\b/gi, 'LIKE');
}

/**
 * 提取SQL中的参数列表
 * @param sql SQL语句
 * @returns 参数名称数组
 */
export function extractSqlParameters(sql: string): string[] {
  if (!sql) return [];
  
  const parameters: string[] = [];
  
  // 提取 ${param} 格式的参数
  const dollarRegex = /\${([^}]+)}/g;
  let match;
  
  while ((match = dollarRegex.exec(sql)) !== null) {
    const paramName = match[1];
    if (!parameters.includes(paramName)) {
      parameters.push(paramName);
    }
  }
  
  // 提取 #{param} 格式的参数
  const hashRegex = /\#\{([^}]+)\}/g;
  while ((match = hashRegex.exec(sql)) !== null) {
    const paramName = match[1];
    if (!parameters.includes(paramName)) {
      parameters.push(paramName);
    }
  }
  
  return parameters;
}

/**
 * 处理LIKE语句中的参数格式
 * 例如将 name like '%${data}%' 转换为合适的参数化格式
 * @param sql 原始SQL语句
 * @returns 处理后的SQL语句
 */
export function normalizeLikeParameters(sql: string): string {
  if (!sql) return sql;
  
  // 场景1: LIKE '%${param}%' 转换
  // 针对常见的 LIKE '%${param}%' 模式，直接字符串替换
  // 注意后端可能不支持 ${param} 格式的参数，此处做特殊处理
  let result = sql;
  
  // 查找模式: LIKE '%${param}%' 
  // 转换为: LIKE CONCAT('%', #{param}, '%')
  // 这是比较标准和安全的参数化方式
  const likePattern = /LIKE\s+'%\${([^}]+)}%'/gi;
  result = result.replace(likePattern, (match, paramName) => {
    console.log(`处理LIKE模式 '%${paramName}%' -> CONCAT函数`);
    return `LIKE CONCAT('%', #{${paramName}}, '%')`;
  });
  
  // 场景2: LIKE '${param}%' 转换 (前缀匹配)
  const prefixPattern = /LIKE\s+'\${([^}]+)}%'/gi;
  result = result.replace(prefixPattern, (match, paramName) => {
    console.log(`处理LIKE前缀模式 '${paramName}%' -> CONCAT函数`);
    return `LIKE CONCAT(#{${paramName}}, '%')`;
  });
  
  // 场景3: LIKE '%${param}' 转换 (后缀匹配)
  const suffixPattern = /LIKE\s+'%\${([^}]+)}'/gi;
  result = result.replace(suffixPattern, (match, paramName) => {
    console.log(`处理LIKE后缀模式 '%${paramName}' -> CONCAT函数`);
    return `LIKE CONCAT('%', #{${paramName}})`;
  });
  
  return result;
}

/**
 * 全面处理SQL语句，进行所有必要的转换
 * @param sql 原始SQL语句
 * @returns 处理后的SQL语句
 */
export function processSQL(sql: string): string {
  if (!sql) return sql;
  
  console.log(`处理SQL参数 - 原始SQL: ${sql.substring(0, 100)}${sql.length > 100 ? '...' : ''}`);
  
  let processed = sql;
  
  // 首先，标准化参数前缀 - 将所有 #{param} 转换为 ${param}
  processed = processed.replace(/\#\{([^}]+)\}/g, '${$1}');
  
  // 注意：不再将 ${param} 转换为 ?，保留 ${param} 格式
  // 原来的代码会导致参数替换问题
  // processed = processed.replace(/\${([^}]+)}/g, '?');
  
  // 处理包含LIKE的查询，但保留 ${param} 格式
  processed = processed.replace(/LIKE\s+'%\${([^}]+)}%'/gi, (match, paramName) => {
    console.log(`将LIKE模式 '%${paramName}%' 保留为 '%\${${paramName}}%'`);
    // 保留原始格式，让后端处理参数替换
    return `LIKE '%\${${paramName}}%'`;
  });
  
  // 同样处理LIKE '${param}%' (前缀匹配)和LIKE '%${param}' (后缀匹配)，保留参数格式
  processed = processed.replace(/LIKE\s+'\${([^}]+)}%'/gi, (match, paramName) => {
    return `LIKE '\${${paramName}}%'`;
  });
  processed = processed.replace(/LIKE\s+'%\${([^}]+)}'/gi, (match, paramName) => {
    return `LIKE '%\${${paramName}}'`;
  });
  
  console.log(`处理SQL参数 - 处理后SQL: ${processed.substring(0, 100)}${processed.length > 100 ? '...' : ''}`);
  
  return processed;
}

// 导出统一的SQL处理函数
export default processSQL; 