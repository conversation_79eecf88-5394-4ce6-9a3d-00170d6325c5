let urlToken = ''

/**
 * 从 URL 中提取查询参数对象
 * @param url URL 字符串，默认为当前页面URL
 * @returns 查询参数对象
 */
export function getQueryObject(url: string): any {
  url = url == null ? window.location.href : url
  
  // 检查是否URL以&开头的特殊情况（处理非标准URL格式）
  if (url.indexOf('?') === -1 && url.indexOf('&') !== -1) {
    // 分离URL和参数部分
    const parts = url.split('&')
    const baseUrl = parts[0]
    const params = parts.slice(1).join('&')
    // 重新构造标准URL
    url = `${baseUrl}?${params}`
  }
  
  // 检查是否有查询参数
  if (url.indexOf('?') === -1) {
    return {}
  }
  
  const search = url.substring(url.lastIndexOf('?') + 1)
  const obj: any = {}
  const reg = /([^?&=]+)=([^?&=]*)/g
  search.replace(reg, (rs, $1: string, $2: string) => {
    const name = decodeURIComponent($1)
    let val = decodeURIComponent($2)
    val = String(val)

    // 去除可能的哈希部分
    const hashIndex = val.indexOf('#')
    if (hashIndex !== -1) {
      val = val.slice(0, hashIndex)
    }

    obj[name] = val
    return rs
  })
  return obj
}

/**
 * 从URL中提取token并存储到localStorage，同时立即从URL中移除token参数
 * @param key token参数名称
 * @returns 是否成功获取并存储token
 */
export function setTokenFromUrl(key: string): boolean {
  // 获取URL中的token参数
  const queryParams = getQueryObject(window.location.href)
  urlToken = queryParams[key]
  
  if (!urlToken) {
    return false
  }
  
  // 处理可能的哈希部分
  const hashIndex = urlToken.indexOf('#')
  if (hashIndex !== -1) {
    urlToken = urlToken.slice(0, hashIndex)
  }

  // 保存token到localStorage
  localStorage.setItem('token', urlToken)
  
  // 立即从URL中移除token参数
  try {
    // 先尝试使用URL API (现代浏览器支持)
    const currentUrl = new URL(window.location.href)
    currentUrl.searchParams.delete(key)
    
    // 获取清理后的URL
    let cleanUrl = currentUrl.toString()
    
    // 处理特殊情况：如果URL开头是域名加&而不是?，修复URL格式
    if (cleanUrl.includes('&') && !cleanUrl.includes('?')) {
      const [baseUrl, ...paramsParts] = cleanUrl.split('&')
      if (paramsParts.length > 0) {
        cleanUrl = `${baseUrl}?${paramsParts.join('&')}`
      }
    }
    
    // 立即替换当前URL，不延迟
    window.history.replaceState({}, '', cleanUrl)
    console.info('[安全] 已从URL中移除token参数')
    
    // 验证是否成功移除
    const verifyParams = getQueryObject(window.location.href)
    if (verifyParams[key]) {
      // 如果仍存在token，尝试使用备用方法再次移除
      const regex = new RegExp(`[?&]${key}=[^&#]*`, 'g')
      let newUrl = window.location.href.replace(regex, '')
      
      // 如果替换后URL以?&开头，修复为只有?
      if (newUrl.indexOf('?&') === 0) {
        newUrl = newUrl.replace('?&', '?')
      }
      
      // 如果只剩下一个?，去掉?
      if (newUrl.endsWith('?')) {
        newUrl = newUrl.slice(0, -1)
      }
      
      window.history.replaceState({}, '', newUrl)
      console.info('[安全] 使用备用方法移除URL中的token参数')
    }
  } catch (error) {
    // 备用方法，如果新的URL API不可用
    console.error('[安全] 移除URL中token参数时出错，使用备用方法:', error)
    
    // 使用正则表达式直接替换
    const regex = new RegExp(`[?&]${key}=[^&#]*`, 'g')
    let newUrl = window.location.href.replace(regex, '')
    
    // 如果替换后URL以?&开头，修复为只有?
    if (newUrl.indexOf('?&') === 0) {
      newUrl = newUrl.replace('?&', '?')
    }
    
    // 如果只剩下一个?，去掉?
    if (newUrl.endsWith('?')) {
      newUrl = newUrl.slice(0, -1)
    }
    
    window.history.replaceState({}, '', newUrl)
  }
  
  return true
}