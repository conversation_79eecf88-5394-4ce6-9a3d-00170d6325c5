/**
 * 枚举服务缓存工具
 * 提供本地缓存机制，减少重复API请求
 */
import type { Enumeration } from '@/api/enumerationService';

interface CacheEntry {
  timestamp: number;
  enums: Enumeration[];
}

/**
 * 枚举缓存服务
 */
export const enumCache = {
  // 存储枚举数据，按项目代码分类
  data: new Map<string, CacheEntry>(),
  
  // 缓存有效期（10分钟）
  CACHE_TTL: 10 * 60 * 1000,
  
  /**
   * 获取缓存数据
   * @param projectCode 项目代码
   * @returns 枚举数据数组或null（缓存未命中）
   */
  get(projectCode: string): Enumeration[] | null {
    const cached = this.data.get(projectCode);
    if (!cached) return null;
    
    // 检查是否过期
    if (Date.now() - cached.timestamp > this.CACHE_TTL) {
      this.data.delete(projectCode);
      return null;
    }
    
    return cached.enums;
  },
  
  /**
   * 设置缓存数据
   * @param projectCode 项目代码
   * @param enums 枚举数据数组
   */
  set(projectCode: string, enums: Enumeration[]): void {
    this.data.set(projectCode, {
      timestamp: Date.now(),
      enums
    });
  },
  
  /**
   * 清除特定项目的缓存
   * @param projectCode 项目代码
   */
  clear(projectCode: string): void {
    this.data.delete(projectCode);
  },
  
  /**
   * 清除所有缓存
   */
  clearAll(): void {
    this.data.clear();
  },
  
  /**
   * 获取项目下的枚举，支持模糊搜索
   * @param projectCode 项目代码
   * @param keyword 搜索关键词
   * @param page 页码
   * @param pageSize 每页条数
   * @returns 枚举列表和总数
   */
  search(
    projectCode: string,
    keyword?: string,
    page: number = 1,
    pageSize: number = 10
  ): { list: Enumeration[]; total: number } | null {
    const cached = this.get(projectCode);
    if (!cached) return null;
    
    let filteredList = cached;
    
    // 如果有关键词，进行本地过滤
    if (keyword) {
      const lowerKeyword = keyword.toLowerCase();
      filteredList = cached.filter(item => 
        item.name.toLowerCase().includes(lowerKeyword) || 
        item.code.toLowerCase().includes(lowerKeyword)
      );
    }
    
    // 计算分页
    const total = filteredList.length;
    const start = (page - 1) * pageSize;
    const end = start + pageSize;
    const pagedList = filteredList.slice(start, end);
    
    return {
      list: pagedList,
      total
    };
  }
};

export default enumCache;