/**
 * Monaco Editor加载器
 *
 * 这个文件用于处理Monaco Editor的加载，并解决Vite动态导入警告
 */

// 配置Monaco Editor的环境
export function configureMonacoEnvironment() {
  // 确保window对象存在（浏览器环境）
  if (typeof window !== 'undefined') {
    window.MonacoEnvironment = {
      // 获取worker URL的函数
      getWorkerUrl: function (moduleId: string, label: string) {
        const prefix = '/monaco-editor/esm/vs';

        if (label === 'json') {
          return `${prefix}/language/json/json.worker.js`;
        }
        if (label === 'css' || label === 'scss' || label === 'less') {
          return `${prefix}/language/css/css.worker.js`;
        }
        if (label === 'html' || label === 'handlebars' || label === 'razor') {
          return `${prefix}/language/html/html.worker.js`;
        }
        if (label === 'typescript' || label === 'javascript') {
          return `${prefix}/language/typescript/ts.worker.js`;
        }

        return `${prefix}/editor/editor.worker.js`;
      },

      // 直接获取worker的函数，避免动态导入警告
      getWorker: function(moduleId: string, label: string) {
        const getWorkerModule = (url: string) => {
          // 使用 @vite-ignore 注释忽略动态导入警告
          return import(/* @vite-ignore */ url).then(worker => {
            return new worker.default();
          });
        };

        const prefix = '/monaco-editor/esm/vs';

        if (label === 'json') {
          return getWorkerModule(`${prefix}/language/json/json.worker.js`);
        }
        if (label === 'css' || label === 'scss' || label === 'less') {
          return getWorkerModule(`${prefix}/language/css/css.worker.js`);
        }
        if (label === 'html' || label === 'handlebars' || label === 'razor') {
          return getWorkerModule(`${prefix}/language/html/html.worker.js`);
        }
        if (label === 'typescript' || label === 'javascript') {
          return getWorkerModule(`${prefix}/language/typescript/ts.worker.js`);
        }

        return getWorkerModule(`${prefix}/editor/editor.worker.js`);
      }
    };
  }
}

// 预加载Monaco Editor的核心模块
export async function preloadMonacoEditor() {
  try {
    // 使用 @vite-ignore 注释忽略动态导入警告
    await import(/* @vite-ignore */ 'monaco-editor');
    console.log('[Monaco] 编辑器核心模块预加载成功');

    // 不再尝试预加载特定语言模块，因为路径可能不正确
    // 改为在需要时动态加载

    return true;
  } catch (error) {
    console.error('[Monaco] 预加载失败:', error);
    return false;
  }
}

// 加载Monaco Editor
export async function loadMonacoEditor() {
  // 配置环境
  configureMonacoEnvironment();

  try {
    // 使用 @vite-ignore 注释忽略动态导入警告
    const monaco = await import(/* @vite-ignore */ 'monaco-editor/esm/vs/editor/editor.api');
    console.log('[Monaco] 编辑器加载成功');
    return monaco;
  } catch (error) {
    console.error('[Monaco] 加载失败:', error);
    throw error;
  }
}

// 默认导出
export default {
  configureMonacoEnvironment,
  preloadMonacoEditor,
  loadMonacoEditor
};
