/**
 * SQL参数监控工具
 * 
 * 用于监控SQL查询中的参数，确保它们被正确识别和处理。
 * 这个工具会注入到全局环境中，帮助调试参数解析问题。
 */
import { extractSqlParameters } from './sql-param-converter';
import type { QueryParameter } from '@/types/query';

// 全局变量类型声明
declare global {
  interface Window {
    __SQL_PARAMETERS__?: string[];
    __PARAM_MONITOR_ACTIVE__?: boolean;
    monitorSqlParameters?: (sql: string) => string[];
    showParamsInputDialog?: (params: QueryParameter[]) => void;
  }
}

/**
 * 启动SQL参数监控
 * 这个函数会在全局注册一些辅助函数，用于监控和调试SQL参数
 */
export function initSqlParamMonitor(): void {
  console.log('初始化SQL参数监控');
  
  // 已经初始化过，不再重复初始化
  if (window.__PARAM_MONITOR_ACTIVE__) {
    console.log('SQL参数监控已经激活');
    return;
  }
  
  // 设置全局标志
  window.__PARAM_MONITOR_ACTIVE__ = true;
  
  // 添加监控SQL参数的方法
  window.monitorSqlParameters = (sql: string): string[] => {
    const params = extractSqlParameters(sql);
    window.__SQL_PARAMETERS__ = params;
    console.log('监测到SQL参数:', params);
    return params;
  };
  
  // 添加显示参数输入对话框的方法
  window.showParamsInputDialog = (params: QueryParameter[]): void => {
    if (!params || params.length === 0) {
      console.log('没有参数需要输入');
      return;
    }
    
    // 创建事件并触发
    const event = new CustomEvent('showParamsDialog', { 
      detail: { parameters: params } 
    });
    document.dispatchEvent(event);
    console.log('已触发参数对话框显示事件');
  };
  
  // 监听SQL编辑器内容变化
  document.addEventListener('sqlContentChanged', (event: Event) => {
    const customEvent = event as CustomEvent;
    if (customEvent.detail && customEvent.detail.sql) {
      const sql = customEvent.detail.sql;
      window.monitorSqlParameters?.(sql);
    }
  });
  
  console.log('SQL参数监控已初始化');
}

// 导出工具函数
export default {
  initSqlParamMonitor
}; 