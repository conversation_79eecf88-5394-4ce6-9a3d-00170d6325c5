/**
 * 表单默认值转换工具
 * 
 * 此工具提供统一的表单默认值序列化和反序列化功能，
 * 确保不同表单类型的默认值在配置导入导出过程中保持一致性。
 */

import dayjs from 'dayjs';

/**
 * 表单类型枚举
 */
export enum FormFieldType {
  TEXT = 'text',
  TEXTAREA = 'textarea',
  NUMBER = 'number',
  DATE = 'date',
  DATETIME = 'datetime',
  SELECT = 'select',
  MULTISELECT = 'multiselect',
  CHECKBOX = 'checkbox',
  RADIO = 'radio',
  DATE_RANGE = 'date-range'
}

/**
 * 将表单值序列化为配置格式（用于保存到配置）
 * @param value 表单值
 * @param fieldType 字段类型
 * @param format 数据格式
 * @returns 序列化后的值，适合保存到配置中
 */
export function serializeFormValue(value: any, fieldType: string, format?: string): any {
  // 空值处理
  if (value === undefined || value === null) {
    return null;
  }

  // 根据不同字段类型进行序列化
  const type = fieldType.toLowerCase();
  
  try {
    switch (type) {
      case FormFieldType.NUMBER:
        // 数字类型：确保是数字，或空字符串
        return value === '' ? '' : Number(value);
        
      case FormFieldType.DATE:
        // 日期类型：转为YYYY-MM-DD格式
        if (typeof value === 'object' && value.format) {
          return value.format('YYYY-MM-DD');
        } else if (typeof value === 'string' && value) {
          // 如果已经是字符串且不为空，尝试格式化
          const dateObj = dayjs(value);
          return dateObj.isValid() ? dateObj.format('YYYY-MM-DD') : value;
        }
        return value;
        
      case FormFieldType.DATETIME:
        // 日期时间类型：转为ISO格式但去掉秒和毫秒
        if (typeof value === 'object' && value.format) {
          return value.format('YYYY-MM-DDTHH:mm');
        } else if (typeof value === 'string' && value) {
          const dateObj = dayjs(value);
          return dateObj.isValid() ? dateObj.format('YYYY-MM-DDTHH:mm') : value;
        }
        return value;
        
      case FormFieldType.MULTISELECT:
        // 多选类型：确保是数组
        if (Array.isArray(value)) {
          return value;
        } else if (value && typeof value === 'string') {
          // 尝试解析JSON字符串
          try {
            const parsed = JSON.parse(value);
            return Array.isArray(parsed) ? parsed : [value];
          } catch {
            return [value]; // 如果解析失败，将字符串作为单个选项
          }
        } else if (value) {
          // 其他非空值，转为单元素数组
          return [value];
        }
        return [];
        
      case FormFieldType.CHECKBOX:
        // 复选框类型：转为布尔值
        return value === true || value === 'true' || value === 1 || value === '1';
        
      case FormFieldType.DATE_RANGE:
        // 日期区间类型：确保是数组并格式化日期
        if (Array.isArray(value) && value.length === 2) {
          return value.map(date => {
            if (typeof date === 'object' && date.format) {
              return date.format('YYYY-MM-DD');
            } else if (typeof date === 'string') {
              const dateObj = dayjs(date);
              return dateObj.isValid() ? dateObj.format('YYYY-MM-DD') : date;
            }
            return date;
          });
        } else if (typeof value === 'string' && value) {
          // 尝试解析日期范围字符串
          try {
            const parsed = JSON.parse(value);
            if (Array.isArray(parsed) && parsed.length === 2) {
              return parsed;
            }
          } catch {
            // 解析失败，返回原值
          }
        }
        return value;
        
      default:
        // 其他类型：保持原样
        return value;
    }
  } catch (error) {
    console.error(`序列化表单值失败 [${type}]:`, error);
    // 发生错误时返回原始值
    return value;
  }
}

/**
 * 将配置中的值反序列化为表单值（用于加载配置到表单）
 * @param value 配置中的值
 * @param fieldType 字段类型
 * @param format 数据格式
 * @returns 反序列化后的值，适合表单使用
 */
export function deserializeFormValue(value: any, fieldType: string, format?: string): any {
  // 空值处理
  if (value === undefined || value === null) {
    return '';
  }

  // 根据不同字段类型进行反序列化
  const type = fieldType.toLowerCase();
  
  try {
    switch (type) {
      case FormFieldType.NUMBER:
        // 数字类型：转为数字或空字符串
        return value === '' ? '' : Number(value);
        
      case FormFieldType.DATE:
        // 日期类型：转为dayjs对象或原始值
        if (typeof value === 'string' && value) {
          const dateObj = dayjs(value);
          return dateObj.isValid() ? dateObj : value;
        }
        return value;
        
      case FormFieldType.DATETIME:
        // 日期时间类型：转为dayjs对象或原始值
        if (typeof value === 'string' && value) {
          const dateObj = dayjs(value);
          return dateObj.isValid() ? dateObj : value;
        }
        return value;
        
      case FormFieldType.MULTISELECT:
        // 多选类型：确保是数组
        if (Array.isArray(value)) {
          return value;
        } else if (value && typeof value === 'string') {
          // 尝试解析JSON字符串
          try {
            const parsed = JSON.parse(value);
            return Array.isArray(parsed) ? parsed : [value];
          } catch {
            return [value]; // 如果解析失败，将字符串作为单个选项
          }
        } else if (value) {
          // 其他非空值，转为单元素数组
          return [value];
        }
        return [];
        
      case FormFieldType.CHECKBOX:
        // 复选框类型：转为布尔值
        return value === true || value === 'true' || value === 1 || value === '1';
        
      case FormFieldType.DATE_RANGE:
        // 日期区间类型：确保是数组并转为dayjs对象
        if (Array.isArray(value) && value.length === 2) {
          return value.map(date => {
            if (typeof date === 'string') {
              const dateObj = dayjs(date);
              return dateObj.isValid() ? dateObj : date;
            }
            return date;
          });
        }
        return value;
        
      default:
        // 字符串和其他类型：转为字符串
        return String(value);
    }
  } catch (error) {
    console.error(`反序列化表单值失败 [${type}]:`, error);
    // 发生错误时返回原始值
    return value;
  }
}

/**
 * 获取表单类型的默认空值
 * @param fieldType 字段类型
 * @returns 对应表单类型的默认空值
 */
export function getDefaultEmptyValue(fieldType: string): any {
  const type = fieldType.toLowerCase();
  
  switch (type) {
    case FormFieldType.NUMBER:
      return '';
    case FormFieldType.MULTISELECT:
      return [];
    case FormFieldType.CHECKBOX:
      return false;
    case FormFieldType.DATE_RANGE:
      return [];
    default:
      return '';
  }
}

/**
 * 检查值是否为空
 * @param value 要检查的值
 * @returns 是否为空
 */
export function isEmptyValue(value: any): boolean {
  if (value === undefined || value === null) {
    return true;
  }
  
  if (Array.isArray(value)) {
    return value.length === 0;
  }
  
  if (typeof value === 'string') {
    return value.trim() === '';
  }
  
  return false;
}