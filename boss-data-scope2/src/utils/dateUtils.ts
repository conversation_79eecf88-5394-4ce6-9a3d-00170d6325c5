/**
 * 日期和时间工具函数
 */
import dayjs from 'dayjs';

/**
 * 将日期格式化为标准格式
 * @param date 日期字符串、时间戳或Date对象
 * @param format 输出格式
 * @returns 格式化后的日期字符串
 */
export function formatDate(date: string | number | Date | null | undefined, format: string = 'YYYY-MM-DD'): string {
  if (!date) return '暂无数据';
  try {
    return dayjs(date).format(format);
  } catch (e) {
    console.error('日期格式化错误:', e);
    return String(date);
  }
}

/**
 * 将日期时间格式化为标准格式
 * @param dateTime 日期时间字符串、时间戳或Date对象
 * @param format 输出格式
 * @returns 格式化后的日期时间字符串
 */
export function formatDateTime(dateTime: string | number | Date | null | undefined, format: string = 'YYYY-MM-DD HH:mm:ss'): string {
  if (!dateTime) return '暂无数据';
  try {
    return dayjs(dateTime).format(format);
  } catch (e) {
    console.error('日期时间格式化错误:', e);
    return String(dateTime);
  }
}

/**
 * 获取相对时间描述
 * @param dateTime 日期时间字符串、时间戳或Date对象
 * @returns 相对时间描述，如"3小时前"、"2天前"
 */
export function getRelativeTime(dateTime: string | number | Date | null | undefined): string {
  if (!dateTime) return '暂无数据';
  try {
    const now = dayjs();
    const target = dayjs(dateTime);
    const diffMinutes = now.diff(target, 'minute');
    
    if (diffMinutes < 1) return '刚刚';
    if (diffMinutes < 60) return `${diffMinutes}分钟前`;
    
    const diffHours = now.diff(target, 'hour');
    if (diffHours < 24) return `${diffHours}小时前`;
    
    const diffDays = now.diff(target, 'day');
    if (diffDays < 30) return `${diffDays}天前`;
    
    const diffMonths = now.diff(target, 'month');
    if (diffMonths < 12) return `${diffMonths}个月前`;
    
    return `${now.diff(target, 'year')}年前`;
  } catch (e) {
    console.error('相对时间计算错误:', e);
    return String(dateTime);
  }
}

/**
 * 检查日期是否是今天
 * @param date 日期字符串、时间戳或Date对象
 * @returns 是否是今天
 */
export function isToday(date: string | number | Date | null | undefined): boolean {
  if (!date) return false;
  try {
    const targetDate = dayjs(date);
    const today = dayjs();
    return targetDate.format('YYYY-MM-DD') === today.format('YYYY-MM-DD');
  } catch (e) {
    console.error('日期检查错误:', e);
    return false;
  }
}