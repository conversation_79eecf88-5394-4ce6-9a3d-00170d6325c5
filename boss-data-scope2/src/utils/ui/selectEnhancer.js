/**
 * 增强自定义下拉选择框的交互行为
 * 主要功能：
 * 1. 为带有select-container类的容器添加点击事件
 * 2. 管理下拉状态
 * 3. 处理鼠标悬停、焦点和选择变化事件
 * 
 * 注意：现在使用原生SVG下拉箭头，不再管理自定义箭头旋转
 */

/**
 * 增强单个select容器的交互行为
 * @param {HTMLElement} container - select容器元素
 */
export function enhanceSelect(container) {
  if (!container) return;
  
  const select = container.querySelector('select');
  if (!select) return;
  
  // 鼠标按下事件 - 点击事件处理
  container.addEventListener('mousedown', () => {
    container.classList.toggle('select-active');
  });
  
  // 失去焦点时恢复状态
  select.addEventListener('blur', () => {
    container.classList.remove('select-active');
  });
  
  // 选择变化时恢复状态
  select.addEventListener('change', () => {
    container.classList.remove('select-active');
  });
  
  // 获得焦点时添加状态
  select.addEventListener('focus', () => {
    container.classList.add('select-active');
  });
}

/**
 * 增强所有select容器的交互行为
 */
export function enhanceSelectContainers() {
  const containers = document.querySelectorAll('.select-container');
  containers.forEach(enhanceSelect);
}

/**
 * 初始化select增强器
 * 可在组件加载后调用以增强动态加载的select
 */
export function initSelectEnhancers() {
  // 在DOM加载完成后初始化
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', enhanceSelectContainers);
  } else {
    enhanceSelectContainers();
  }
  
  // 监听DOM变化，处理动态添加的select容器
  if (window.MutationObserver) {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList' && mutation.addedNodes.length) {
          const newContainers = document.querySelectorAll('.select-container:not([data-enhanced])');
          newContainers.forEach((container) => {
            enhanceSelect(container);
            container.setAttribute('data-enhanced', 'true');
          });
        }
      });
    });
    
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }
}

// 自动初始化
initSelectEnhancers();

export default {
  enhanceSelect,
  enhanceSelectContainers,
  initSelectEnhancers
};