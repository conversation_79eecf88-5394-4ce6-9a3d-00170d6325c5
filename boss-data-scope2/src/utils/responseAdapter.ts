/**
 * 响应格式适配器
 * 用于处理API返回的不同数据格式，统一转换为应用内的标准格式
 */

/**
 * 从API响应中提取字段列表
 * 支持多种常见的响应数据结构
 * @param data API响应数据
 * @returns 字段列表数组
 */
export function extractFields(data: any): any[] {
  if (!data) return [];
  
  // 记录处理过程
  console.log('[响应适配] 开始从数据中提取字段:', data);
  
  let fields: any[] = [];
  
  // 处理各种可能的数据格式
  if (data.success && data.data && data.data.fields) {
    // 标准格式 {success: true, data: {fields: [...]}}
    fields = data.data.fields;
    console.log('[响应适配] 从标准格式提取字段');
  } else if (data.data && Array.isArray(data.data.fields)) {
    // 格式 {data: {fields: [...]}}
    fields = data.data.fields;
    console.log('[响应适配] 从data.fields格式提取字段');
  } else if (data.fields && Array.isArray(data.fields)) {
    // 格式 {fields: [...]}
    fields = data.fields;
    console.log('[响应适配] 从fields数组格式提取字段');
  } else if (data.code === 200 && data.data && Array.isArray(data.data.fields)) {
    // 格式 {code: 200, data: {fields: [...]}}
    fields = data.data.fields;
    console.log('[响应适配] 从code格式提取字段');
  } else if (data.parameters && Array.isArray(data.parameters)) {
    // 格式 {parameters: [...], fields: [...]}
    if (data.fields && Array.isArray(data.fields)) {
      fields = data.fields;
      console.log('[响应适配] 从parameters+fields格式提取字段');
    } else {
      // 根据parameters构建fields
      fields = data.parameters.map((param: any) => ({
        name: param.name,
        type: param.type || 'string',
        label: param.label || param.name,
        tableName: param.tableName || 'unknown',
        isEncrypted: param.isEncrypted === true // 保留加密状态信息
      }));
      console.log('[响应适配] 从parameters格式构建字段');
    }
  } else if (Array.isArray(data)) {
    // 直接是数组格式
    fields = data;
    console.log('[响应适配] 从直接数组格式提取字段');
  } else {
    console.warn('[响应适配] 无法识别的响应格式:', data);
  }
  
  console.log('[响应适配] 最终提取的字段:', fields);
  return fields || [];
}

/**
 * 标准化API响应
 * 将不同格式的响应转换为统一标准格式
 * @param response API响应对象
 * @returns 标准化后的响应
 */
export function standardizeResponse(response: any): any {
  if (!response) return { success: false, data: null, message: 'Empty response' };
  
  // 如果已经是标准格式
  if (response.success !== undefined && response.data !== undefined) {
    return response;
  }
  
  // 如果是axios响应对象，提取data部分
  const data = response.data !== undefined ? response.data : response;
  
  // 处理带code的响应
  if (data.code !== undefined) {
    const isSuccess = data.code === 200 || data.code === 0;
    return {
      success: isSuccess,
      code: data.code,
      message: data.message || (isSuccess ? 'success' : 'error'),
      data: data.data || data
    };
  }
  
  // 处理其他情况，默认为成功
  return {
    success: true,
    code: 200,
    message: 'success',
    data: data
  };
}