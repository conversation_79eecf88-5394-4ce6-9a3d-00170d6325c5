const executeQuery = async () => {
  isExecuting.value = true;
  queryError.value = null;
  
  try {
    // 准备查询参数
    const params: ExecuteQueryRequest = {
      id: query.value?.id || '',
      name: query.value?.name || '',
      queryType: query.value?.queryType || 'SQL',
      dataSourceId: query.value?.dataSourceId || '',
      sql: editor.value?.getValue() || '',
      parameters: []
    };
    
    console.log('执行查询参数:', params);
    
    // 只有SQL查询才需要分析参数
    if (params.queryType === 'SQL') {
      try {
        console.log('分析SQL参数');
        
        // 使用参数分析服务分析SQL中的参数
        const parameters = await queryService.analyzeQueryParameters({
          dataSourceId: params.dataSourceId,
          sql: params.sql
        });
        
        console.log('分析出的参数:', parameters);
        
        // 如果有参数，显示参数输入对话框
        if (parameters && parameters.length > 0) {
          console.log('检测到查询参数，打开参数输入对话框');
          queryParameters.value = parameters;
          pendingQueryParams.value = { ...params };
          showParamsDialog.value = true;
          return; // 中断执行，等待用户输入参数
        }
        
        // 没有参数，直接执行查询
        return queryExecutionResult.executeQuery(params);
      } catch (error) {
        console.error('分析SQL参数失败:', error);
        // 参数分析失败，仍然尝试执行查询
        return queryExecutionResult.executeQuery(params);
      }
    }
    
    // 非SQL查询，直接执行
    return queryExecutionResult.executeQuery(params);
  } catch (error) {
    console.error('执行查询失败:', error);
    queryError.value = error instanceof Error 
      ? error.message 
      : String(error);
  } finally {
    isExecuting.value = false;
  }
}; 