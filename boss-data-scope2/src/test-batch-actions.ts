import { convertToStandardConfig, convertFromStandardConfig } from './utils/configConverter';

// 测试批量操作转换功能
export function testBatchActionsConversion() {
  // 测试数据 - 模拟集成编辑页面的数据结构
  const testData = {
    meta: {
      database: "测试数据库",
      schema: "测试schema",
      table: "测试表",
      pageCode: "testPage",
      apis: {
        query: { method: "GET", path: "/test-query" }
      }
    },
    tableConfig: {
      columns: [
        {
          field: "id",
          label: "ID",
          type: "TEXT"
        },
        {
          field: "name",
          label: "名称", 
          type: "TEXT"
        }
      ],
      pagination: {
        enabled: true,
        pageSize: 20
      },
      export: {
        enabled: true,
        formats: ["CSV", "EXCEL"]
      },
      actions: [
        {
          label: "新建",
          action: "create",
          icon: "plus",
          enabled: true,
          confirmationRequired: true,
          confirmationTitle: "确认创建",
          confirmationMessage: "确定要创建新记录吗？"
        }
      ],
      rowActions: [
        {
          label: "编辑",
          action: "edit",
          icon: "edit",
          enabled: true
        },
        {
          label: "删除",
          action: "delete",
          icon: "delete",
          enabled: true,
          confirmationRequired: true,
          confirmationTitle: "确认删除",
          confirmationMessage: "此操作不可撤销，确定要删除吗？",
          permissions: ["admin", "manager"]
        }
      ],
      batchActions: [
        {
          id: "batch_export",
          label: "批量导出",
          action: "exportSelected",
          icon: "download",
          enabled: true
        },
        {
          id: "batch_delete",
          label: "批量删除",
          action: "deleteSelected",
          icon: "delete",
          enabled: true,
          confirmationRequired: true,
          confirmationTitle: "批量删除确认",
          confirmationMessage: "确定要删除所选记录吗？此操作不可撤销。",
          permissions: ["admin"]
        }
      ]
    }
  };

  // 转换为标准配置
  const standardConfig = convertToStandardConfig(testData);
  
  // 检查标准配置中的批量操作
  const result1 = {
    batchEnable: standardConfig.operation.batchEnable,
    batchActionsCount: standardConfig.operation.batchActions ? standardConfig.operation.batchActions.length : 0,
    firstActionName: standardConfig.operation.batchActions && standardConfig.operation.batchActions.length > 0 
      ? standardConfig.operation.batchActions[0].name 
      : null,
    secondActionName: standardConfig.operation.batchActions && standardConfig.operation.batchActions.length > 1 
      ? standardConfig.operation.batchActions[1].name 
      : null,
    hasBatchDeleteConfirm: standardConfig.operation.batchActions 
      ? standardConfig.operation.batchActions.some(
          action => action.hybridEvent === 'deleteSelected' && action.confirm && action.confirm.title
        )
      : false,
    hasBatchDeletePermissions: standardConfig.operation.batchActions
      ? standardConfig.operation.batchActions.some(
          action => action.hybridEvent === 'deleteSelected' && action.permissions && action.permissions.includes('admin')
        )
      : false
  };
  
  // 再转换回内部格式
  const convertedBack = convertFromStandardConfig(standardConfig);
  
  // 检查还原后的配置中的批量操作
  const result2 = {
    batchActionsCount: convertedBack.tableConfig.batchActions ? convertedBack.tableConfig.batchActions.length : 0,
    firstActionLabel: convertedBack.tableConfig.batchActions && convertedBack.tableConfig.batchActions.length > 0 
      ? convertedBack.tableConfig.batchActions[0].label 
      : null,
    secondActionLabel: convertedBack.tableConfig.batchActions && convertedBack.tableConfig.batchActions.length > 1 
      ? convertedBack.tableConfig.batchActions[1].label 
      : null,
    hasBatchDeleteConfirm: convertedBack.tableConfig.batchActions 
      ? convertedBack.tableConfig.batchActions.some(
          action => action.action === 'deleteSelected' && action.confirmationRequired
        )
      : false,
    hasBatchDeletePermissions: convertedBack.tableConfig.batchActions
      ? convertedBack.tableConfig.batchActions.some(
          action => action.action === 'deleteSelected' && action.permissions && action.permissions.includes('admin')
        )
      : false
  };
  
  return { 
    standardConfig: result1,
    convertedBack: result2,
    fullStandardConfig: standardConfig,
    fullConvertedBack: convertedBack
  };
}