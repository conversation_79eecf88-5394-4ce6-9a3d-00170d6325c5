<template>
  <div class="mt-8">
    <h3 class="text-lg font-medium mb-4">API测试</h3>
    <button 
      @click="testEnumApi" 
      class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none"
      :disabled="isTesting"
    >
      <i v-if="isTesting" class="fas fa-spinner fa-spin mr-1"></i>
      {{ isTesting ? '测试中...' : '直接调用API测试' }}
    </button>
    
    <div v-if="result" class="mt-4 p-4 bg-gray-100 rounded-md">
      <pre class="text-sm overflow-auto">{{ JSON.stringify(result, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { runTest } from './testCreateEnum';

const isTesting = ref(false);
const result = ref(null);

const testEnumApi = async () => {
  isTesting.value = true;
  result.value = null;
  
  try {
    // 捕获控制台输出
    const originalConsoleLog = console.log;
    const logs: any[] = [];
    
    console.log = (...args) => {
      originalConsoleLog(...args);
      logs.push(args);
    };
    
    // 执行测试
    await runTest();
    
    // 恢复控制台
    console.log = originalConsoleLog;
    
    // 显示结果
    result.value = logs;
  } catch (error) {
    console.error('测试失败:', error);
    result.value = { error: error.message };
  } finally {
    isTesting.value = false;
  }
};
</script>