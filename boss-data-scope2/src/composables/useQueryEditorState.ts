import { ref, computed, watch } from 'vue';
import type { Ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useQueryStore } from '@/stores/query';
import { useDataSourceStore } from '@/stores/datasource';
import { message } from 'ant-design-vue';
import type { QueryEditorState, ProcessedSchema } from '@/types/queryEditor';
import type { DataSource } from '@/types/datasource';
import { metadataService } from '@/services/metadata';

/**
 * 查询编辑器状态管理组合式API
 */
export function useQueryEditorState() {
  // 路由
  const route = useRoute();
  const router = useRouter();
  
  // Store
  const queryStore = useQueryStore();
  const dataSourceStore = useDataSourceStore();
  
  // 组件状态标志
  let isComponentMounted = true;
  
  // 查询编辑器的状态
  const activeTab = ref<'editor' | 'nlq' | 'builder'>('editor');
  const leftPanel = ref<'metadata' | 'saved'>('metadata');
  const selectedDataSourceId = ref<string>('');
  const selectedSchema = ref<string>('');
  const availableSchemas = ref<any[]>([]);
  const sqlQuery = ref<string>('');
  const naturalLanguageQuery = ref('');
  const builderQuery = ref('');
  const isExecuting = ref(false);
  const queryError = ref<string | null>(null);
  const statusMessage = ref<string | null>(null);
  const errorMessage = ref<string | null>(null);
  const currentQueryId = ref<string | null>(null);
  const isSaveModalVisible = ref(false);
  const isLoadingQuery = ref(false);
  const queryName = ref('');
  const queryVersion = ref('V1');
  const availableVersions = ref<string[]>([]);
  const selectedVersion = ref('V1');
  const currentMaxVersionNumber = ref(1);
  const isFavorite = ref(false);
  const showExportOptions = ref(false);
  const savedQuerySearch = ref('');
  const executionTime = ref(0);
  const executionTimer = ref<number | null>(null);
  const lastDraftSaveAt = ref<string | null>(null);
  
  // 查询版本状态
  const versionStatus = ref<'DRAFT' | 'PUBLISHED' | 'DEPRECATED'>('DRAFT');
  const isActiveVersion = ref(false);
  const publishedAt = ref<string | null>(null);
  const lastEditedAt = ref<string | null>(null);
  const deprecatedAt = ref<string | null>(null);
  
  // 数据源相关状态
  const isLoadingDataSources = ref(false);
  const isLoadingSchemas = ref(false);
  const isRefreshingMetadata = ref(false);
  const schemaError = ref<string | null>(null);
  
  // 来源路径管理
  const backUrl = computed(() => {
    // 如果是从详情页过来，则返回详情页
    if (route.query.from === "detail" && route.query.id) {
      return `/query/detail/${route.query.id}`;
    }
    // 默认返回列表页
    return "/query/list";
  });
  
  // 计算属性：当前选中的数据源
  const selectedDataSource = computed<DataSource | null>(() => {
    if (!selectedDataSourceId.value) return null;
    return dataSourceStore.dataSources.find(ds => ds.id === selectedDataSourceId.value) || null;
  });
  
  // 计算属性：处理后的schemas
  const processedSchemas = computed<ProcessedSchema[]>(() => {
    if (!selectedDataSourceId.value) return [];
    
    const schemas = dataSourceStore.metadataState.schemas.get(selectedDataSourceId.value) || [];
    return schemas.map(schema => {
      if (typeof schema === 'string') {
        return { 
          value: schema, 
          name: schema,
          tablesCount: undefined,
          dataSourceId: selectedDataSourceId.value
        };
      }
      return {
        value: schema.value || schema.name || 'default',
        name: schema.name || schema.value || 'Default Schema',
        tablesCount: (schema as any).tablesCount,
        dataSourceId: selectedDataSourceId.value
      };
    });
  });
  
  // 计算属性：当前数据源是否可用
  const isDataSourceActive = computed<boolean>(() => {
    if (!selectedDataSource.value) return false;
    return selectedDataSource.value.status === 'active';
  });
  
  // 计算属性：是否可以执行查询
  const canExecuteQuery = computed(() => {
    if (!selectedDataSourceId.value) {
      return false;
    }
    
    if (activeTab.value === 'editor' && (!sqlQuery.value || sqlQuery.value.trim().length === 0)) {
      return false;
    }
    
    if (activeTab.value === 'nlq' && (!naturalLanguageQuery.value || naturalLanguageQuery.value.trim().length === 0)) {
      return false;
    }
    
    return true;
  });

  // 计算属性：版本状态文本
  const versionStatusText = computed(() => {
    if (versionStatus.value === 'DRAFT') {
      return '草稿';
    } else if (versionStatus.value === 'PUBLISHED') {
      return isActiveVersion.value ? '当前版本' : '已发布';
    } else if (versionStatus.value === 'DEPRECATED') {
      return '已废弃';
    }
    return versionStatus.value; // 默认返回原始状态值
  });
  
  // 计算属性：已格式化的执行时间
  const formattedExecutionTime = computed(() => {
    const minutes = Math.floor(executionTime.value / 60);
    const seconds = executionTime.value % 60;
    
    if (minutes > 0) {
      return `${minutes}分 ${seconds}秒`;
    } else {
      return `${seconds}秒`;
    }
  });
  
  // 监听数据源选择变化
  watch(selectedDataSourceId, async (newId, oldId) => {
    if (newId === oldId) return;
    
    // 清空schema相关状态
    selectedSchema.value = '';
    schemaError.value = null;
    
    if (!newId) return;
    
    // 加载schemas
    isLoadingSchemas.value = true;
    try {
      await dataSourceStore.getSchemas(newId);
    } catch (err) {
      schemaError.value = err instanceof Error ? err.message : '加载Schema失败';
    } finally {
      isLoadingSchemas.value = false;
    }
  });
  
  // 刷新元数据
  const refreshMetadata = async () => {
    if (!selectedDataSourceId.value) return;
    
    isRefreshingMetadata.value = true;
    schemaError.value = null;
    
    try {
      // 清除缓存
      dataSourceStore.clearMetadataCache(selectedDataSourceId.value);
      
      // 重新加载schemas
      await dataSourceStore.getSchemas(selectedDataSourceId.value);
      
      // 如果当前已选择schema，重新加载tables
      if (selectedSchema.value) {
        await dataSourceStore.getTables(selectedDataSourceId.value, selectedSchema.value);
      }
      
      message.success('元数据已刷新');
    } catch (err) {
      console.error('刷新元数据失败:', err);
      schemaError.value = err instanceof Error ? err.message : '刷新元数据失败';
      message.error('刷新元数据失败');
    } finally {
      isRefreshingMetadata.value = false;
    }
  };
  
  // 处理Schema变更
  const handleSchemaChange = async () => {
    if (!selectedSchema.value || !selectedDataSourceId.value) return;
    
    schemaError.value = null;
    
    try {
      // 加载表数据
      await dataSourceStore.getTables(selectedDataSourceId.value, selectedSchema.value);
    } catch (err) {
      schemaError.value = err instanceof Error ? err.message : '加载表数据失败';
    }
  };
  
  // 返回对象
  return {
    // 状态引用
    activeTab,
    leftPanel,
    selectedDataSourceId,
    selectedSchema,
    availableSchemas,
    sqlQuery,
    naturalLanguageQuery,
    builderQuery,
    isExecuting,
    queryError,
    statusMessage,
    errorMessage,
    currentQueryId,
    isSaveModalVisible,
    isLoadingQuery,
    queryName,
    queryVersion,
    availableVersions,
    selectedVersion,
    currentMaxVersionNumber,
    isFavorite,
    showExportOptions,
    savedQuerySearch,
    executionTime,
    executionTimer,
    lastDraftSaveAt,
    versionStatus,
    isActiveVersion,
    publishedAt,
    lastEditedAt,
    deprecatedAt,
    isLoadingDataSources,
    isLoadingSchemas,
    isRefreshingMetadata,
    schemaError,
    
    // 计算属性
    backUrl,
    selectedDataSource,
    processedSchemas,
    isDataSourceActive,
    canExecuteQuery,
    versionStatusText,
    formattedExecutionTime,
    
    // 方法
    refreshMetadata,
    handleSchemaChange,
    
    // 工具
    queryStore,
    dataSourceStore,
    route,
    router,
    
    // 设置组件已卸载的标识
    setComponentUnmounted: () => {
      isComponentMounted = false;
    },
    
    // 组件是否已挂载
    get isComponentMounted() {
      return isComponentMounted;
    }
  };
}