import { defineStore } from 'pinia';
import type { User } from '@/types/user';
import { getUserProfile, logout as _logout } from '@/api/auth';

export const useUserStore = defineStore('user', {
  state: () => ({
    user: null as User | null,
    token: localStorage.getItem('token') || null,
    loading: false,
    error: null
  }),

  getters: {
    isAuthenticated(): boolean {
      return !!this.token;
    },

    isAdmin(): boolean {
      return this.user?.role === 'ADMIN';
    },

    currentUser(): User | null {
      return this.user;
    }
  },

  actions: {
    setUser(user: User | null) {
      this.user = user;
    },

    setToken(token: string | null) {
      this.token = token;
      if (token) {
        localStorage.setItem('token', token);
      } else {
        localStorage.removeItem('token');
      }
    },

    async login(username: string, password: string) {
      this.loading = true;
      this.error = null;
      try {
        // 在实际应用中，这里应该调用API进行登录
        // 以下是模拟的登录逻辑
        const mockUsers = [
          { id: '1', username: 'admin', password: 'admin', name: '管理员', role: 'ADMIN' },
          { id: '2', username: 'user', password: 'user', name: '普通用户', role: 'USER' }
        ];

        const user = mockUsers.find(u => u.username === username && u.password === password);
        if (!user) {
          throw new Error('用户名或密码错误');
        }

        // 模拟获取令牌
        const token = `mock-token-${Date.now()}`;

        // 保存用户和令牌
        this.setUser({
          id: user.id,
          username: user.username,
          name: user.name,
          role: user.role,
          email: `${user.username}@example.com`,
          avatar: null
        });
        this.setToken(token);

        return true;
      } catch (error) {
        this.error = error instanceof Error ? error.message : '登录失败';
        return false;
      } finally {
        this.loading = false;
      }
    },

    async logout() {
      try {
        // 先调用退出登录API
        await _logout();
      } catch (error) {
        console.error('退出登录API调用失败:', error);
        // 即使 API 调用失败，仍然继续清除本地状态
      }
      
      // 清除用户信息
      this.setUser(null);
      
      // 清除token
      this.setToken(null);
      
      // 清除所有本地存储的认证数据
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      
      // 清除可能的其他认证相关信息
      document.cookie.split(';').forEach(cookie => {
        const [name] = cookie.trim().split('=');
        if (name && (name.includes('token') || name.includes('auth') || name.includes('session'))) {
          document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
        }
      });

    },

    async fetchUserProfile() {
      this.loading = true;
      this.error = null;

      // 如果没有token，则不发起请求
      if (!this.token) {
        this.loading = false;
        return null;
      }

      try {
        // 发起用户信息请求
        const res = await getUserProfile();
        const data = res?.data;

        if (!data) {
          return null;
        }

        const { login_url, loginName, id, username, email } = data;

        if (login_url) {
          this.setUser(null);
          this.setToken(null);
          const callback = window.location.href.split('#')[0];
          window.location.href = `${login_url}?callback=${encodeURIComponent(callback)}`;
          return null;
        }

        // 处理正常情况，无论是否有loginName都尝试构建用户信息
        if (loginName || username) {
          // 构建用户信息对象
          const userProfile = {
            id: id || 'temp-id',
            username: loginName || username || 'user',
            name: username || loginName || 'User', // 确保始终有名称
            email: email || '',
            avatar: null,
          };

          // 更新用户信息
          this.setUser(userProfile);
          
          // 重试机制，确保用户信息被设置
          if (!this.user) {
            this.setUser(userProfile); 
          }
          
          return userProfile;
        } else {
          // 如果无法获取到用户信息，但有token，创建一个临时用户
          const tempProfile = {
            id: 'temp-id',
            username: 'user',
            name: '已登录用户',
            email: '',
            avatar: null,
          };
          
          this.setUser(tempProfile);
          return tempProfile;
        }
      } catch (error) {
        console.error('获取用户信息失败:', error);
        this.error = error instanceof Error ? error.message : '获取用户信息失败';
        
        // 即使请求失败，仍然维持登录状态，创建临时用户信息
        if (this.token && !this.user) {
          const tempProfile = {
            id: 'temp-id',
            username: 'user',
            name: '已登录用户',
            email: '',
            avatar: null,
          };
          
          this.setUser(tempProfile);
          return tempProfile;
        }
        
        return null;
      } finally {
        this.loading = false;
      }
    }
  }
});
