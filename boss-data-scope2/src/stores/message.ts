import { defineStore } from 'pinia';
import { ref } from 'vue';

interface Message {
  id: string;
  type: 'success' | 'error' | 'info' | 'warning';
  content: string;
  description?: string;
  duration?: number;
}

export const useMessageStore = defineStore('message', () => {
  const messages = ref<Message[]>([]);
  
  // 添加消息
  const addMessage = (message: Omit<Message, 'id'>) => {
    const id = Date.now().toString();
    
    messages.value.push({
      id,
      ...message
    });
    
    // 设置自动移除
    if (message.duration !== 0) {
      setTimeout(() => {
        removeMessage(id);
      }, message.duration || 3000);
    }
    
    return id;
  };
  
  // 移除消息
  const removeMessage = (id: string) => {
    const index = messages.value.findIndex(msg => msg.id === id);
    if (index !== -1) {
      messages.value.splice(index, 1);
    }
  };
  
  // 清空所有消息
  const clearMessages = () => {
    messages.value = [];
  };
  
  // 便捷方法
  const success = (contentOrOptions: string | { content: string; description?: string; duration?: number }, duration = 3000) => {
    if (typeof contentOrOptions === 'string') {
      return addMessage({ type: 'success', content: contentOrOptions, duration });
    } else {
      return addMessage({ 
        type: 'success', 
        content: contentOrOptions.content, 
        description: contentOrOptions.description,
        duration: contentOrOptions.duration ?? duration 
      });
    }
  };
  
  const error = (contentOrOptions: string | { content: string; description?: string; duration?: number }, duration = 5000) => {
    if (typeof contentOrOptions === 'string') {
      return addMessage({ type: 'error', content: contentOrOptions, duration });
    } else {
      return addMessage({ 
        type: 'error', 
        content: contentOrOptions.content, 
        description: contentOrOptions.description,
        duration: contentOrOptions.duration ?? duration 
      });
    }
  };
  
  const info = (contentOrOptions: string | { content: string; description?: string; duration?: number }, duration = 3000) => {
    if (typeof contentOrOptions === 'string') {
      return addMessage({ type: 'info', content: contentOrOptions, duration });
    } else {
      return addMessage({ 
        type: 'info', 
        content: contentOrOptions.content, 
        description: contentOrOptions.description,
        duration: contentOrOptions.duration ?? duration 
      });
    }
  };
  
  const warning = (contentOrOptions: string | { content: string; description?: string; duration?: number }, duration = 4000) => {
    if (typeof contentOrOptions === 'string') {
      return addMessage({ type: 'warning', content: contentOrOptions, duration });
    } else {
      return addMessage({ 
        type: 'warning', 
        content: contentOrOptions.content, 
        description: contentOrOptions.description,
        duration: contentOrOptions.duration ?? duration 
      });
    }
  };
  
  return {
    messages,
    addMessage,
    removeMessage,
    clearMessages,
    success,
    error,
    info,
    warning
  };
});