<template>
  <div class="container mx-auto px-4 py-6">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="flex justify-between items-center">
        <h1 class="text-2xl font-bold text-gray-900">操作服务</h1>
        <button 
          @click="createOperation"
          class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          <i class="fas fa-plus mr-2"></i>
          创建操作
        </button>
      </div>
    </div>
    
    <!-- 过滤器 -->
    <div class="bg-white shadow rounded-lg p-6 mb-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <!-- 搜索 -->
        <div>
          <label for="search" class="block text-sm font-medium text-gray-700 mb-1">
            搜索
          </label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <i class="fas fa-search text-gray-400"></i>
            </div>
            <input 
              id="search"
              v-model="filters.search"
              type="text"
              class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              placeholder="搜索操作名称或描述"
              @keyup.enter="applyFilters"
            />
          </div>
        </div>
        
        <!-- 操作类型过滤 -->
        <div>
          <label for="type" class="block text-sm font-medium text-gray-700 mb-1">
            操作类型
          </label>
          <select
            id="type"
            v-model="filters.type"
            class="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
            @change="applyFilters"
          >
            <option value="">全部类型</option>
            <option value="CREATE">创建</option>
            <option value="UPDATE">更新</option>
            <option value="DELETE">删除</option>
            <option value="CUSTOM">自定义</option>
          </select>
        </div>
        
        <!-- 状态过滤 -->
        <div>
          <label for="status" class="block text-sm font-medium text-gray-700 mb-1">
            状态
          </label>
          <select
            id="status"
            v-model="filters.status"
            class="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
            @change="applyFilters"
          >
            <option value="">全部状态</option>
            <option value="ACTIVE">已激活</option>
            <option value="INACTIVE">已停用</option>
            <option value="DRAFT">草稿</option>
          </select>
        </div>
        
        <!-- 重置按钮 -->
        <div class="flex items-end">
          <button
            @click="resetFilters"
            class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <i class="fas fa-sync-alt mr-2"></i>
            重置筛选
          </button>
        </div>
      </div>
    </div>
    
    <!-- 操作列表 -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
      <div v-if="isLoading" class="py-10 text-center">
        <div class="inline-flex items-center px-4 py-2">
          <i class="fas fa-spinner fa-spin mr-2"></i>
          加载中...
        </div>
      </div>

      <div v-else-if="operations.length === 0" class="py-10 text-center">
        <div class="text-gray-500">
          <i class="fas fa-info-circle text-xl mb-2"></i>
          <p>暂无操作数据</p>
          <button
            @click="createOperation"
            class="mt-4 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <i class="fas fa-plus mr-2"></i>
            创建操作
          </button>
        </div>
      </div>
      
      <table v-else class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              名称
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              类型
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              关联查询
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              应用接口
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              状态
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              创建时间
            </th>
            <th scope="col" class="relative px-6 py-3">
              <span class="sr-only">操作</span>
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr v-for="operation in operations" :key="operation.id">
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900">{{ operation.name }}</div>
              <div class="text-sm text-gray-500">{{ operation.description || '无描述' }}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span :class="[
                'px-2 inline-flex text-xs leading-5 font-semibold rounded-full',
                {
                  'bg-green-100 text-green-800': operation.type === 'CREATE',
                  'bg-blue-100 text-blue-800': operation.type === 'UPDATE',
                  'bg-red-100 text-red-800': operation.type === 'DELETE',
                  'bg-purple-100 text-purple-800': operation.type === 'CUSTOM'
                }
              ]">
                {{ getOperationTypeLabel(operation.type) }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              {{ operation.query?.name || operation.queryId }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              {{ operation.apiEndpoint }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span :class="[
                'px-2 inline-flex text-xs leading-5 font-semibold rounded-full',
                {
                  'bg-green-100 text-green-800': operation.status === 'ACTIVE',
                  'bg-gray-100 text-gray-800': operation.status === 'INACTIVE',
                  'bg-yellow-100 text-yellow-800': operation.status === 'DRAFT'
                }
              ]">
                {{ getStatusLabel(operation.status) }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              {{ formatDate(operation.createdAt) }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
              <button 
                @click="viewOperationDetail(operation.id)"
                class="text-indigo-600 hover:text-indigo-900 mr-3"
              >
                详情
              </button>
              <button 
                @click="editOperation(operation.id)"
                class="text-blue-600 hover:text-blue-900 mr-3"
              >
                编辑
              </button>
              <button 
                v-if="operation.status === 'ACTIVE'"
                @click="executeOperation(operation.id)"
                class="text-green-600 hover:text-green-900 mr-3"
              >
                执行
              </button>
              <button 
                @click="confirmDelete(operation)"
                class="text-red-600 hover:text-red-900"
              >
                删除
              </button>
            </td>
          </tr>
        </tbody>
      </table>
      
      <!-- 分页 -->
      <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
        <div class="flex-1 flex justify-between sm:hidden">
          <button 
            @click="prevPage" 
            :disabled="pagination.page === 1"
            :class="[
              'relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white',
              pagination.page === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50'
            ]"
          >
            上一页
          </button>
          <button
            @click="nextPage"
            :disabled="pagination.page >= maxPage"
            :class="[
              'relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white',
              pagination.page >= maxPage ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50'
            ]"
          >
            下一页
          </button>
        </div>
        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
          <div>
            <p class="text-sm text-gray-700">
              显示第 <span class="font-medium">{{ startItem }}</span> 至 <span class="font-medium">{{ endItem }}</span> 条，共 <span class="font-medium">{{ pagination.total }}</span> 条
            </p>
          </div>
          <div>
            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
              <button
                @click="prevPage"
                :disabled="pagination.page === 1"
                :class="[
                  'relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500',
                  pagination.page === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50'
                ]"
              >
                <span class="sr-only">上一页</span>
                <i class="fas fa-chevron-left"></i>
              </button>
              
              <button
                v-for="page in displayPages"
                :key="page"
                @click="goToPage(page)"
                :class="[
                  'relative inline-flex items-center px-4 py-2 border text-sm font-medium',
                  page === pagination.page
                    ? 'z-10 bg-indigo-50 border-indigo-500 text-indigo-600'
                    : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                ]"
              >
                {{ page }}
              </button>
              
              <button
                @click="nextPage"
                :disabled="pagination.page >= maxPage"
                :class="[
                  'relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500',
                  pagination.page >= maxPage ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50'
                ]"
              >
                <span class="sr-only">下一页</span>
                <i class="fas fa-chevron-right"></i>
              </button>
            </nav>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 删除确认弹窗 -->
    <div v-if="showDeleteConfirm" class="fixed z-10 inset-0 overflow-y-auto">
      <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
          <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="sm:flex sm:items-start">
              <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                <i class="fas fa-exclamation-triangle text-red-600"></i>
              </div>
              <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                  确认删除
                </h3>
                <div class="mt-2">
                  <p class="text-sm text-gray-500">
                    确定要删除操作 "{{ operationToDelete?.name }}" 吗？此操作不可撤销。
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button 
              @click="deleteOperation"
              type="button" 
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
            >
              删除
            </button>
            <button 
              @click="showDeleteConfirm = false"
              type="button" 
              class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
            >
              取消
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useOperationStore } from '@/stores/operation';
import type { Operation, OperationType, OperationStatus } from '@/types/operation';

// 路由
const router = useRouter();

// Store
const operationStore = useOperationStore();

// 获取store状态
const operations = computed(() => operationStore.operations);
const isLoading = computed(() => operationStore.isLoading);
const pagination = computed(() => operationStore.pagination);

// 过滤器状态
const filters = ref({
  search: '',
  type: '',
  status: ''
});

// 删除确认
const showDeleteConfirm = ref(false);
const operationToDelete = ref<Operation | null>(null);

// 分页计算属性
const maxPage = computed(() => Math.ceil(pagination.value.total / pagination.value.pageSize));
const startItem = computed(() => ((pagination.value.page - 1) * pagination.value.pageSize) + 1);
const endItem = computed(() => {
  const end = pagination.value.page * pagination.value.pageSize;
  return end > pagination.value.total ? pagination.value.total : end;
});

// 显示分页按钮
const displayPages = computed(() => {
  const totalPages = maxPage.value;
  const currentPage = pagination.value.page;
  const pages = [];
  
  if (totalPages <= 7) {
    for (let i = 1; i <= totalPages; i++) {
      pages.push(i);
    }
  } else {
    if (currentPage <= 4) {
      for (let i = 1; i <= 5; i++) {
        pages.push(i);
      }
      pages.push('...');
      pages.push(totalPages);
    } else if (currentPage >= totalPages - 3) {
      pages.push(1);
      pages.push('...');
      for (let i = totalPages - 4; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      pages.push(1);
      pages.push('...');
      for (let i = currentPage - 1; i <= currentPage + 1; i++) {
        pages.push(i);
      }
      pages.push('...');
      pages.push(totalPages);
    }
  }
  
  return pages;
});

// 生命周期钩子
onMounted(async () => {
  await fetchOperations();
});

// 方法
// 获取操作列表
const fetchOperations = async () => {
  await operationStore.fetchOperations({
    page: pagination.value.page,
    size: pagination.value.pageSize,
    search: filters.value.search,
    type: filters.value.type as OperationType,
    status: filters.value.status as OperationStatus
  });
};

// 应用筛选
const applyFilters = async () => {
  pagination.value.page = 1;
  await fetchOperations();
};

// 重置筛选
const resetFilters = async () => {
  filters.value = {
    search: '',
    type: '',
    status: ''
  };
  
  pagination.value.page = 1;
  await fetchOperations();
};

// 分页方法
const prevPage = async () => {
  if (pagination.value.page > 1) {
    pagination.value.page--;
    await fetchOperations();
  }
};

const nextPage = async () => {
  if (pagination.value.page < maxPage.value) {
    pagination.value.page++;
    await fetchOperations();
  }
};

const goToPage = async (page: number | string) => {
  if (typeof page === 'number') {
    pagination.value.page = page;
    await fetchOperations();
  }
};

// 操作方法
const createOperation = () => {
  router.push({ name: 'OperationCreate' });
};

const editOperation = (id: string) => {
  router.push({ name: 'OperationEdit', params: { id } });
};

const viewOperationDetail = (id: string) => {
  router.push({ name: 'OperationDetail', params: { id } });
};

const executeOperation = (id: string) => {
  router.push({ name: 'OperationDetail', params: { id }, query: { execute: 'true' } });
};

// 确认删除
const confirmDelete = (operation: Operation) => {
  operationToDelete.value = operation;
  showDeleteConfirm.value = true;
};

// 执行删除
const deleteOperation = async () => {
  if (operationToDelete.value) {
    const success = await operationStore.deleteOperation(operationToDelete.value.id);
    
    if (success) {
      showDeleteConfirm.value = false;
      operationToDelete.value = null;
      await fetchOperations();
    }
  }
};

// 格式化方法
const formatDate = (dateString: string) => {
  if (!dateString) return '-';
  
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const getOperationTypeLabel = (type: OperationType) => {
  const typeMap: Record<OperationType, string> = {
    'CREATE': '创建',
    'UPDATE': '更新',
    'DELETE': '删除',
    'CUSTOM': '自定义'
  };
  
  return typeMap[type] || type;
};

const getStatusLabel = (status: OperationStatus) => {
  const statusMap: Record<OperationStatus, string> = {
    'ACTIVE': '已激活',
    'INACTIVE': '已停用',
    'DRAFT': '草稿'
  };
  
  return statusMap[status] || status;
};
</script> 