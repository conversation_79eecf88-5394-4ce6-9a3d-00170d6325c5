<template>
  <div class="container mx-auto px-4 py-6">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="flex items-center">
        <button
          @click="goBack"
          class="mr-4 text-gray-500 hover:text-gray-700"
        >
          <i class="fas fa-arrow-left"></i>
        </button>
        <h1 class="text-2xl font-bold text-gray-900">操作执行历史</h1>
      </div>
    </div>
    
    <!-- 操作信息 -->
    <div v-if="operation" class="bg-white shadow rounded-lg mb-6 p-6">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-lg font-medium text-gray-900">{{ operation.name }}</h2>
          <p class="mt-1 text-sm text-gray-500">{{ operation.description || '无描述' }}</p>
        </div>
        <div class="flex items-center space-x-4">
          <span :class="[
            'px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full',
            {
              'bg-green-100 text-green-800': operation.type === 'CREATE',
              'bg-blue-100 text-blue-800': operation.type === 'UPDATE',
              'bg-red-100 text-red-800': operation.type === 'DELETE',
              'bg-purple-100 text-purple-800': operation.type === 'CUSTOM'
            }
          ]">
            {{ getOperationTypeLabel(operation.type) }}
          </span>
          <span :class="[
            'px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full',
            {
              'bg-green-100 text-green-800': operation.status === 'ACTIVE',
              'bg-gray-100 text-gray-800': operation.status === 'INACTIVE',
              'bg-yellow-100 text-yellow-800': operation.status === 'DRAFT'
            }
          ]">
            {{ getStatusLabel(operation.status) }}
          </span>
          <button
            @click="viewOperationDetail"
            class="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            查看详情
          </button>
        </div>
      </div>
    </div>
    
    <!-- 加载中状态 -->
    <div v-if="isLoading" class="flex justify-center py-12">
      <div class="inline-flex items-center px-4 py-2">
        <i class="fas fa-spinner fa-spin mr-2"></i>
        加载中...
      </div>
    </div>
    
    <!-- 历史记录列表 -->
    <div v-else class="bg-white shadow rounded-lg overflow-hidden">
      <div v-if="executionHistory.length === 0" class="text-center py-12">
        <div class="text-gray-500">
          <i class="fas fa-history text-4xl mb-2"></i>
          <p>暂无执行历史记录</p>
        </div>
      </div>
      
      <div v-else>
        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
          <h2 class="text-lg font-medium text-gray-900">执行历史记录</h2>
          <button
            @click="refreshHistory"
            class="text-sm text-indigo-600 hover:text-indigo-800"
            :disabled="isLoading"
          >
            <i v-if="isLoading" class="fas fa-spinner fa-spin mr-1"></i>
            <i v-else class="fas fa-sync-alt mr-1"></i>
            刷新
          </button>
        </div>
        
        <ul class="divide-y divide-gray-200">
          <li v-for="(record, index) in executionHistory" :key="index" class="px-6 py-5">
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div :class="[
                  'flex-shrink-0 h-10 w-10 rounded-full flex items-center justify-center',
                  {
                    'bg-green-100': record.status === 'SUCCESS',
                    'bg-red-100': record.status === 'FAILED',
                    'bg-yellow-100': record.status === 'PENDING',
                    'bg-blue-100': record.status === 'PROCESSING'
                  }
                ]">
                  <i :class="[
                    'text-lg',
                    {
                      'fas fa-check text-green-600': record.status === 'SUCCESS',
                      'fas fa-times text-red-600': record.status === 'FAILED',
                      'fas fa-clock text-yellow-600': record.status === 'PENDING',
                      'fas fa-spinner fa-spin text-blue-600': record.status === 'PROCESSING'
                    }
                  ]"></i>
                </div>
                <div class="ml-4">
                  <div class="text-sm font-medium text-gray-900">
                    执行 ID: {{ record.id.substring(0, 8) }}
                  </div>
                  <div class="text-sm text-gray-500">
                    {{ formatDate(record.executedAt) }}
                  </div>
                </div>
              </div>
              <div class="flex items-center">
                <span :class="[
                  'px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full mr-3',
                  {
                    'bg-green-100 text-green-800': record.status === 'SUCCESS',
                    'bg-red-100 text-red-800': record.status === 'FAILED',
                    'bg-yellow-100 text-yellow-800': record.status === 'PENDING',
                    'bg-blue-100 text-blue-800': record.status === 'PROCESSING'
                  }
                ]">
                  {{ getExecutionStatusLabel(record.status) }}
                </span>
                <div class="text-sm text-gray-500">
                  耗时: {{ record.executionTime }}ms
                </div>
              </div>
            </div>
            
            <div v-if="record.status === 'FAILED' && record.errorMessage" class="mt-3 ml-14">
              <div class="text-sm text-red-600 bg-red-50 p-3 rounded">
                错误信息: {{ record.errorMessage }}
              </div>
            </div>
            
            <div v-if="record.result" class="mt-3 ml-14">
              <button
                @click="toggleResultDisplay(index)"
                class="text-sm text-indigo-600 hover:text-indigo-800 flex items-center"
              >
                <i :class="[
                  expandedResults.includes(index) ? 'fas fa-chevron-down' : 'fas fa-chevron-right',
                  'mr-1'
                ]"></i>
                {{ expandedResults.includes(index) ? '隐藏结果' : '显示结果' }}
              </button>
              
              <div v-if="expandedResults.includes(index)" class="mt-2 text-sm bg-gray-50 p-3 rounded overflow-x-auto">
                <pre class="text-gray-700">{{ JSON.stringify(record.result, null, 2) }}</pre>
              </div>
            </div>
          </li>
        </ul>
        
        <!-- 分页 -->
        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
          <div class="flex-1 flex justify-between sm:hidden">
            <button 
              @click="prevPage" 
              :disabled="pagination.page === 1"
              :class="[
                'relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white',
                pagination.page === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50'
              ]"
            >
              上一页
            </button>
            <button
              @click="nextPage"
              :disabled="pagination.page >= maxPage"
              :class="[
                'relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white',
                pagination.page >= maxPage ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50'
              ]"
            >
              下一页
            </button>
          </div>
          <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p class="text-sm text-gray-700">
                显示第 <span class="font-medium">{{ startItem }}</span> 至 <span class="font-medium">{{ endItem }}</span> 条，共 <span class="font-medium">{{ pagination.total }}</span> 条
              </p>
            </div>
            <div>
              <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button
                  @click="prevPage"
                  :disabled="pagination.page === 1"
                  :class="[
                    'relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500',
                    pagination.page === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50'
                  ]"
                >
                  <span class="sr-only">上一页</span>
                  <i class="fas fa-chevron-left"></i>
                </button>
                
                <button
                  v-for="page in displayPages"
                  :key="page"
                  @click="goToPage(page)"
                  :class="[
                    'relative inline-flex items-center px-4 py-2 border text-sm font-medium',
                    page === pagination.page
                      ? 'z-10 bg-indigo-50 border-indigo-500 text-indigo-600'
                      : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                  ]"
                >
                  {{ page }}
                </button>
                
                <button
                  @click="nextPage"
                  :disabled="pagination.page >= maxPage"
                  :class="[
                    'relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500',
                    pagination.page >= maxPage ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50'
                  ]"
                >
                  <span class="sr-only">下一页</span>
                  <i class="fas fa-chevron-right"></i>
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useOperationStore } from '@/stores/operation';
import type { Operation, OperationExecution, OperationType, OperationStatus, ExecutionStatus } from '@/types/operation';

// 路由相关
const route = useRoute();
const router = useRouter();
const operationId = computed(() => route.params.id as string);

// Store
const operationStore = useOperationStore();

// 状态
const isLoading = ref(false);
const operation = computed(() => operationStore.currentOperation);
const executionHistory = ref<OperationExecution[]>([]);
const expandedResults = ref<number[]>([]); // 存储已展开的结果索引
const pagination = ref({
  page: 1,
  pageSize: 10,
  total: 0
});

// 分页计算属性
const maxPage = computed(() => Math.ceil(pagination.value.total / pagination.value.pageSize));
const startItem = computed(() => ((pagination.value.page - 1) * pagination.value.pageSize) + 1);
const endItem = computed(() => {
  const end = pagination.value.page * pagination.value.pageSize;
  return end > pagination.value.total ? pagination.value.total : end;
});

// 显示分页按钮
const displayPages = computed(() => {
  const totalPages = maxPage.value;
  const currentPage = pagination.value.page;
  const pages = [];
  
  if (totalPages <= 7) {
    for (let i = 1; i <= totalPages; i++) {
      pages.push(i);
    }
  } else {
    if (currentPage <= 4) {
      for (let i = 1; i <= 5; i++) {
        pages.push(i);
      }
      pages.push('...');
      pages.push(totalPages);
    } else if (currentPage >= totalPages - 3) {
      pages.push(1);
      pages.push('...');
      for (let i = totalPages - 4; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      pages.push(1);
      pages.push('...');
      for (let i = currentPage - 1; i <= currentPage + 1; i++) {
        pages.push(i);
      }
      pages.push('...');
      pages.push(totalPages);
    }
  }
  
  return pages;
});

// 生命周期钩子
onMounted(async () => {
  // 加载操作详情和执行历史
  await loadOperationAndHistory();
});

// 加载操作和历史
const loadOperationAndHistory = async () => {
  try {
    isLoading.value = true;
    
    // 加载操作详情
    await operationStore.fetchOperationById(operationId.value);
    
    // 加载执行历史
    await fetchExecutionHistory();
  } catch (error) {
    console.error('加载数据失败:', error);
  } finally {
    isLoading.value = false;
  }
};

// 获取执行历史
const fetchExecutionHistory = async () => {
  try {
    const response = await operationStore.fetchOperationHistory(
      operationId.value,
      pagination.value.page,
      pagination.value.pageSize
    );
    
    executionHistory.value = response.items;
    pagination.value.total = response.total;
  } catch (error) {
    console.error('加载执行历史失败:', error);
  }
};

// 刷新历史
const refreshHistory = async () => {
  await fetchExecutionHistory();
};

// 切换结果显示
const toggleResultDisplay = (index: number) => {
  const i = expandedResults.value.indexOf(index);
  if (i === -1) {
    expandedResults.value.push(index);
  } else {
    expandedResults.value.splice(i, 1);
  }
};

// 分页方法
const prevPage = async () => {
  if (pagination.value.page > 1) {
    pagination.value.page--;
    await fetchExecutionHistory();
    // 重置展开状态
    expandedResults.value = [];
  }
};

const nextPage = async () => {
  if (pagination.value.page < maxPage.value) {
    pagination.value.page++;
    await fetchExecutionHistory();
    // 重置展开状态
    expandedResults.value = [];
  }
};

const goToPage = async (page: number | string) => {
  if (typeof page === 'number') {
    pagination.value.page = page;
    await fetchExecutionHistory();
    // 重置展开状态
    expandedResults.value = [];
  }
};

// 查看操作详情
const viewOperationDetail = () => {
  router.push({ name: 'OperationDetail', params: { id: operationId.value } });
};

// 返回
const goBack = () => {
  router.back();
};

// 工具方法
const formatDate = (dateString?: string) => {
  if (!dateString) return '-';
  
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

const getOperationTypeLabel = (type: OperationType) => {
  const typeMap: Record<OperationType, string> = {
    'CREATE': '创建',
    'UPDATE': '更新',
    'DELETE': '删除',
    'CUSTOM': '自定义'
  };
  
  return typeMap[type] || type;
};

const getStatusLabel = (status: OperationStatus) => {
  const statusMap: Record<OperationStatus, string> = {
    'ACTIVE': '已激活',
    'INACTIVE': '已停用',
    'DRAFT': '草稿'
  };
  
  return statusMap[status] || status;
};

const getExecutionStatusLabel = (status: ExecutionStatus) => {
  const statusMap: Record<ExecutionStatus, string> = {
    'SUCCESS': '成功',
    'FAILED': '失败',
    'PENDING': '等待中',
    'PROCESSING': '处理中'
  };
  
  return statusMap[status] || status;
};
</script> 