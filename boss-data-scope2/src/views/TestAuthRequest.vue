<script lang="ts" setup>
import { ref } from 'vue';
import AuthRequestModal from '@/components/datasource/AuthRequestModal.vue';

// 测试数据
const showModal = ref(false);
const authType = ref<'datasource' | 'schema' | 'table' | 'column'>('datasource');
const itemId = ref('test-datasource-1');
const itemName = ref('测试数据源');

// 打开弹窗
const openModal = (type: 'datasource' | 'schema' | 'table' | 'column', id: string, name: string) => {
  authType.value = type;
  itemId.value = id;
  itemName.value = name;
  showModal.value = true;
};

// 关闭弹窗
const closeModal = () => {
  showModal.value = false;
};

// 申请成功回调
const onSuccess = () => {
  console.log('申请成功');
  closeModal();
};
</script>

<template>
  <div class="p-6">
    <h1 class="text-2xl font-bold mb-6">权限申请功能测试</h1>

    <div class="space-y-4">
      <div class="bg-white p-4 rounded-lg shadow">
        <h2 class="text-lg font-medium mb-4">测试不同类型的权限申请</h2>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <button
            @click="openModal('datasource', 'ds-001', '生产数据库')"
            class="p-4 border border-blue-300 rounded-lg hover:bg-blue-50 text-left"
          >
            <div class="font-medium text-blue-700">数据源权限申请</div>
            <div class="text-sm text-gray-500 mt-1">申请访问生产数据库</div>
            <div class="text-xs text-blue-600 mt-1">✓ 已启用授权配置</div>
          </button>

          <button
            @click="openModal('schema', 'schema-001', 'user_management')"
            class="p-4 border border-green-300 rounded-lg hover:bg-green-50 text-left"
          >
            <div class="font-medium text-green-700">Schema权限申请</div>
            <div class="text-sm text-gray-500 mt-1">申请访问用户管理Schema</div>
            <div class="text-xs text-green-600 mt-1">✓ 已启用授权配置</div>
          </button>

          <button
            @click="openModal('table', 'table-001', 'users')"
            class="p-4 border border-yellow-300 rounded-lg hover:bg-yellow-50 text-left"
          >
            <div class="font-medium text-yellow-700">表权限申请</div>
            <div class="text-sm text-gray-500 mt-1">申请访问用户表</div>
            <div class="text-xs text-yellow-600 mt-1">✓ 已启用授权配置</div>
          </button>

          <button
            @click="openModal('column', 'column-001', 'phone_number')"
            class="p-4 border border-red-300 rounded-lg hover:bg-red-50 text-left"
          >
            <div class="font-medium text-red-700">列权限申请</div>
            <div class="text-sm text-gray-500 mt-1">申请访问手机号字段</div>
            <div class="text-xs text-red-600 mt-1">✓ 已启用授权配置</div>
          </button>
        </div>

        <div class="mt-6">
          <h3 class="text-lg font-medium mb-4">未启用授权配置的资源示例</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div class="p-4 border border-gray-200 rounded-lg bg-gray-50 text-left opacity-60">
              <div class="font-medium text-gray-500">普通数据源</div>
              <div class="text-sm text-gray-400 mt-1">无需权限申请</div>
              <div class="text-xs text-gray-400 mt-1">✗ 未启用授权配置</div>
            </div>

            <div class="p-4 border border-gray-200 rounded-lg bg-gray-50 text-left opacity-60">
              <div class="font-medium text-gray-500">普通表</div>
              <div class="text-sm text-gray-400 mt-1">无需权限申请</div>
              <div class="text-xs text-gray-400 mt-1">✗ 未启用授权配置</div>
            </div>

            <div class="p-4 border border-gray-200 rounded-lg bg-gray-50 text-left opacity-60">
              <div class="font-medium text-gray-500">普通列</div>
              <div class="text-sm text-gray-400 mt-1">无需权限申请</div>
              <div class="text-xs text-gray-400 mt-1">✗ 未启用授权配置</div>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-gray-50 p-4 rounded-lg">
        <h3 class="font-medium mb-2">功能说明</h3>
        <ul class="text-sm text-gray-600 space-y-1">
          <li>• 只有启用了授权配置（isAuthRequired=true）的资源才会显示"申请授权"按钮</li>
          <li>• 点击上方蓝色按钮可以测试不同类型的权限申请</li>
          <li>• 申请弹窗会显示当前权限状态</li>
          <li>• 支持填写申请理由并提交申请</li>
          <li>• 后端API会处理权限申请的存储和状态管理</li>
          <li>• 灰色区域展示了未启用授权配置的资源，这些资源不会显示申请按钮</li>
        </ul>
      </div>
    </div>

    <!-- 权限申请弹窗 -->
    <AuthRequestModal
      :visible="showModal"
      :type="authType"
      :item-id="itemId"
      :item-name="itemName"
      @close="closeModal"
      @success="onSuccess"
    />
  </div>
</template>

<style scoped>
/* 可以添加自定义样式 */
</style>
