<template>
  <div class="container mx-auto px-4 py-6">
    <div class="page-header mb-6">
      <div class="flex justify-between items-center">
        <h1 class="text-2xl font-bold text-gray-900">{{ isEditMode ? '编辑页面' : '创建页面' }}</h1>
        <div class="flex items-center space-x-2">
          <button 
            type="button" 
            @click="confirmCancel"
            class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            取消
          </button>
          <button 
            type="button"
            @click="submitForm"
            class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            保存
          </button>
          <button 
            v-if="isEditMode" 
            type="button" 
            @click="viewPage"
            class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
          >
            预览
          </button>
        </div>
      </div>
    </div>

    <div class="bg-white shadow rounded-lg p-6">
      <form @submit.prevent="submitForm">
        <!-- 基本信息部分 -->
        <div class="border-b border-gray-200 pb-5 mb-5">
          <h3 class="text-lg font-medium leading-6 text-gray-900">基本信息</h3>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <label for="title" class="block text-sm font-medium text-gray-700 mb-1">页面标题<span class="text-red-500">*</span></label>
            <input
              id="title"
              v-model="formState.title"
              type="text"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              placeholder="请输入页面标题"
              required
            />
          </div>
          
          <div>
            <label for="status" class="block text-sm font-medium text-gray-700 mb-1">页面状态<span class="text-red-500">*</span></label>
            <select
              id="status"
              v-model="formState.status"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              required
            >
              <option value="active">活跃</option>
              <option value="inactive">禁用</option>
              <option value="draft">草稿</option>
            </select>
          </div>
          
          <div>
            <label for="description" class="block text-sm font-medium text-gray-700 mb-1">页面描述</label>
            <textarea
              id="description"
              v-model="formState.description"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              rows="3"
              placeholder="请输入页面描述"
            ></textarea>
          </div>
          
          <div>
            <label for="tags" class="block text-sm font-medium text-gray-700 mb-1">标签</label>
            <div class="flex flex-wrap gap-2">
              <div 
                v-for="(tag, index) in tagOptions" 
                :key="index"
                @click="toggleTag(tag)"
                class="px-3 py-1 rounded-full text-sm cursor-pointer transition-colors"
                :class="formState.tags.includes(tag) 
                  ? 'bg-indigo-100 text-indigo-800 border border-indigo-300' 
                  : 'bg-gray-100 text-gray-600 border border-gray-200 hover:bg-gray-200'"
              >
                {{ tag }}
              </div>
            </div>
          </div>
        </div>

        <!-- 数据源配置部分 -->
        <div class="border-b border-gray-200 pb-5 mb-5">
          <h3 class="text-lg font-medium leading-6 text-gray-900">数据源配置</h3>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <!-- 所属系统选择 - 必填项 -->
          <div>
            <label for="system" class="block text-sm font-medium text-gray-700 mb-1">所属系统<span class="text-red-500">*</span></label>
            <div class="text-xs text-gray-500 mb-2">选择页面所属的系统，将从外部系统获取</div>
            <a-select
              id="system"
              v-model:value="formState.systemId"
              placeholder="请选择所属系统"
              class="w-full"
              :options="systemOptions"
              :loading="loadingSystems"
            />
          </div>
          
          <!-- 系统集成选择 -->
          <div>
            <label for="integration" class="block text-sm font-medium text-gray-700 mb-1">选择系统集成<span class="text-red-500">*</span></label>
            <div class="mb-2 text-xs text-gray-500">可选择多个已启用的系统集成服务</div>
            <a-select
              id="integration"
              v-model:value="formState.integrationIds"
              mode="multiple"
              placeholder="请选择系统集成"
              class="w-full"
              :options="activeIntegrationOptions"
              :loading="loadingIntegrations"
              :filter-option="filterIntegrationOption"
            />
          </div>
        </div>

        <!-- 显示配置 -->
        <div class="border-b border-gray-200 pb-5 mb-5">
          <h3 class="text-lg font-medium leading-6 text-gray-900">显示配置</h3>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <label for="layout" class="block text-sm font-medium text-gray-700 mb-1">布局方式</label>
            <select
              id="layout"
              v-model="formState.displayConfig.layout"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            >
              <option value="standard">标准布局（过滤器+表格）</option>
              <option value="tabs">选项卡布局</option>
              <option value="dashboard">仪表盘布局</option>
            </select>
          </div>
          
          <div>
            <label for="theme" class="block text-sm font-medium text-gray-700 mb-1">主题</label>
            <select
              id="theme"
              v-model="formState.displayConfig.theme"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            >
              <option value="default">默认主题</option>
              <option value="light">浅色主题</option>
              <option value="dark">深色主题</option>
            </select>
          </div>
          
          <div class="flex items-center">
            <input
              id="enable-filter"
              v-model="formState.displayConfig.showFilter"
              type="checkbox"
              class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
            />
            <label for="enable-filter" class="ml-2 block text-sm text-gray-900">
              显示过滤区域
            </label>
          </div>
          
          <div class="flex items-center">
            <input
              id="enable-chart"
              v-model="formState.displayConfig.showChart"
              type="checkbox"
              class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
            />
            <label for="enable-chart" class="ml-2 block text-sm text-gray-900">
              显示图表
            </label>
          </div>
          
          <!-- 页面位置配置 -->
          <div class="col-span-2 mt-3 border-t border-gray-100 pt-3">
            <div class="flex items-center">
              <input
                id="enable-position"
                v-model="formState.displayConfig.position.enabled"
                type="checkbox"
                class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
              />
              <label for="enable-position" class="ml-2 block text-sm font-medium text-gray-700">
                启用页面位置设置
              </label>
              <div class="ml-2 text-xs text-gray-500">（优化在系统集成中的显示位置）</div>
            </div>
            
            <div v-if="formState.displayConfig.position.enabled" class="mt-3 grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label for="position-container" class="block text-sm font-medium text-gray-700 mb-1">容器区域</label>
                <select
                  id="position-container"
                  v-model="formState.displayConfig.position.container"
                  class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
                  <option value="main">主内容区</option>
                  <option value="sidebar">侧边栏</option>
                  <option value="header">顶部区域</option>
                  <option value="footer">底部区域</option>
                </select>
              </div>
              
              <div>
                <label for="position-index" class="block text-sm font-medium text-gray-700 mb-1">显示顺序</label>
                <div class="relative">
                  <input
                    id="position-index"
                    v-model.number="formState.displayConfig.position.index"
                    type="number"
                    min="0"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    placeholder="0"
                  />
                  <div class="mt-1 text-xs text-gray-500">数字越小显示越靠前（0为最前）</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 预览提示信息 -->
        <div v-if="isEditMode" class="mt-4 bg-blue-50 p-3 rounded-md flex items-start">
          <svg class="h-5 w-5 text-blue-400 mt-0.5 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9z" clip-rule="evenodd" />
          </svg>
          <div>
            <p class="text-sm text-blue-700">点击<strong>预览</strong>按钮可以跳转到子系统查看真实集成效果。如果您启用了位置设置，可以在预览中查看页面的实际显示位置。</p>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { reactive } from '@vue/reactivity';
import { useRouter, useRoute } from 'vue-router';
import { message } from '@/services/message';
import { useIntegrationStore } from '@/stores/integration';
import { usePageStore } from '@/stores/page';
import type { Page, PageConfig } from '@/stores/page';
import type { Integration } from '@/types/integration';

// 路由相关
const router = useRouter();
const route = useRoute();
// 使用统一的消息服务
const integrationStore = useIntegrationStore();
const pageStore = usePageStore();
const isEditMode = computed(() => !!route.params.id);

// 标签选项
const tagOptions = ref([
  '数据',
  '分析',
  '报表',
  '管理',
  '配置',
  '系统',
  '用户',
  '产品',
  '销售',
  '库存'
]);

// 集成选项
const integrationOptions = computed<Integration[]>(() => integrationStore.integrations || []);

// 已激活的系统集成选项，转换为选择框需要的格式
const activeIntegrationOptions = computed(() => {
  return integrationOptions.value
    .filter((item: Integration) => item.status === 'ACTIVE')
    .map((item: Integration) => ({
      value: item.id,
      label: item.name
    }));
});

// 加载状态
const loadingIntegrations = computed(() => integrationStore.loading);

// 过滤系统集成选项
const filterIntegrationOption = (input: string, option: any) => {
  return option.label.toLowerCase().includes(input.toLowerCase());
};

// 系统选项 - 模拟数据，将来从外部系统获取
const systemOptions = ref([
  { value: 'system1', label: '数据管理系统' },
  { value: 'system2', label: '用户管理系统' },
  { value: 'system3', label: '订单处理系统' },
  { value: 'system4', label: '产品管理系统' },
  { value: 'system5', label: '内容管理系统' }
]);

// 加载系统选项状态
const loadingSystems = ref(false);

// 表单状态
const formState = reactive({
  title: '',
  status: 'active' as 'active' | 'inactive' | 'draft',
  description: '',
  tags: [] as string[],
  systemId: '', // 所属系统ID
  integrationIds: [] as string[],
  displayConfig: {
    layout: 'standard',
    theme: 'default',
    showFilter: true,
    showChart: false,
    position: {
      enabled: false,
      index: 0,  // 默认位置为0（第一个）
      container: 'main'  // 默认容器为main
    }
  }
});

// 切换标签
const toggleTag = (tag: string) => {
  const index = formState.tags.indexOf(tag);
  if (index === -1) {
    formState.tags.push(tag);
  } else {
    formState.tags.splice(index, 1);
  }
};

// 获取系统列表 - 模拟获取外部系统列表
const fetchSystems = async () => {
  try {
    loadingSystems.value = true;
    
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 这里不需要修改systemOptions，因为我们已经设置了模拟数据
    // 将来替换为真实的外部系统API调用
    // const response = await externalSystemApi.getSystems();
    // systemOptions.value = response.map(item => ({
    //   value: item.id,
    //   label: item.name
    // }));
    
    console.log('获取系统列表成功');
  } catch (error) {
    console.error('获取系统列表失败:', error);
    message.error({
      content: '获取系统列表失败',
      description: '无法连接到系统服务，请检查网络连接或稍后重试',
      duration: 5000
    });
  } finally {
    loadingSystems.value = false;
  }
};

// 加载页面数据
const loadPageData = async () => {
  try {
    // 获取系统列表和集成列表
    await Promise.all([fetchSystems(), fetchIntegrations()]);
    
    if (!isEditMode.value) return;
    
    message.info({
      content: '正在加载页面数据',
      description: '正在从服务器获取页面配置及相关系统集成信息',
      duration: 3000
    });
    
    // 获取页面数据
    const pageData = await pageStore.fetchPageById(route.params.id as string);
    
    if (pageData) {
      // 将API返回的数据映射到表单
      formState.title = pageData.title;
      if (['active', 'inactive', 'draft'].includes(pageData.status)) {
        formState.status = pageData.status as 'active' | 'inactive' | 'draft';
      }
      formState.description = pageData.description || '';
      formState.tags = pageData.tags || [];
      
      // 获取页面配置
      const pageConfig = await pageStore.fetchPageConfig(route.params.id as string);
      
      if (pageConfig) {
        // 从 meta 中获取系统ID，如果存在的话
        if (pageConfig.meta && pageConfig.meta.systemId) {
          formState.systemId = pageConfig.meta.systemId as string;
        }
        
        if (pageConfig.integration) {
          // 提取页面中使用的系统集成
          formState.integrationIds = pageConfig.integration.systems || [];
        }
        
        // 设置显示配置
        if (pageConfig.meta) {
          formState.displayConfig = {
            layout: pageConfig.meta.layout || 'standard',
            theme: pageConfig.meta.theme || 'default',
            showFilter: !!pageConfig.filter,
            showChart: !!pageConfig.chart,
            position: {
              enabled: pageConfig.meta.position?.enabled || false,
              index: pageConfig.meta.position?.index || 0,
              container: pageConfig.meta.position?.container || 'main'
            }
          };
        }
      }
      
      message.success({
        content: '页面数据加载成功',
        description: '页面配置信息已完成加载，可以进行编辑操作',
        duration: 3000
      });
    } else {
      message.error({
        content: '未找到页面数据',
        description: '无法获取指定页面的信息，该页面可能已被删除或目前无法访问',
        duration: 5000
      });
    }
  } catch (error) {
    console.error('加载页面数据失败:', error);
    message.error({
      content: '加载页面数据失败',
      description: '无法从服务器获取页面信息，请检查网络连接或稍后重试',
      duration: 5000
    });
  }
};

// 获取系统集成列表
const fetchIntegrations = async () => {
  try {
    // 获取已激活的系统集成
    await integrationStore.fetchIntegrations({
      status: 'ACTIVE'
    });
    
    if (integrationStore.error) {
      message.warning({
        content: '获取系统集成列表部分失败',
        description: integrationStore.error,
        duration: 4000
      });
    }
    
    if (integrationStore.integrations.length === 0) {
      message.warning({
        content: '没有可用的系统集成',
        description: '没有找到已启用的系统集成，请先创建并启用系统集成，然后再创建页面',
        duration: 4000
      });
    }
  } catch (error) {
    console.error('获取系统集成列表失败:', error);
    message.error({
      content: '获取系统集成列表失败',
      description: error instanceof Error ? error.message : '未知错误，请检查网络连接或刷新页面',
      duration: 5000
    });
  }
};

// 提交表单
const submitForm = async () => {
  try {
    if (!formState.title) {
      message.error({
      content: '验证失败',
      description: '请输入页面标题，标题是必填项',
      duration: 5000
    });
      return;
    }
    
    if (!formState.systemId) {
      message.error({
      content: '验证失败',
      description: '请选择页面所属系统，此信息是必填项',
      duration: 5000
    });
      return;
    }
    
    if (!formState.integrationIds || formState.integrationIds.length === 0) {
      message.error({
      content: '验证失败',
      description: '请选择至少一个系统集成，页面需要至少关联一个集成',
      duration: 5000
    });
      return;
    }
    
    message.info({
      content: '正在保存页面',
      description: '正在将页面信息提交到服务器，请稍等',
      duration: 3000
    });
    
    // 构建页面数据，确保只包含 Page 接口中的字段
    const pageData: Partial<Page> = {
      title: formState.title,
      status: formState.status,
      description: formState.description,
      tags: formState.tags
    };

    // 系统相关信息存储在单独的变量中，之后会用于构建页面配置
    const systemInfo = {
      systemId: formState.systemId
    };
    
    // 构建页面配置数据
    const pageConfig = {
      meta: {
        title: formState.title,
        description: formState.description,
        layout: formState.displayConfig.layout,
        theme: formState.displayConfig.theme,
        systemId: systemInfo.systemId, // 将系统ID放在 meta 中
        position: formState.displayConfig.position.enabled ? {
          enabled: formState.displayConfig.position.enabled,
          index: formState.displayConfig.position.index,
          container: formState.displayConfig.position.container
        } : undefined
      },
      filter: formState.displayConfig.showFilter ? {} : undefined,
      list: {},
      chart: formState.displayConfig.showChart ? {} : undefined,
      integration: {
        systems: formState.integrationIds,
        configs: {}
      }
    };
    
    // 根据是编辑还是新建执行不同操作
    let result;
    if (isEditMode.value) {
      result = await pageStore.updatePage(route.params.id as string, pageData);
      // 实际项目中这里应该还有更新页面配置的API调用
    } else {
      result = await pageStore.createPage(pageData);
      // 实际项目中这里应该还有保存页面配置的API调用
    }
    
    if (result) {
      message.success({
        content: isEditMode.value ? '页面更新成功' : '页面创建成功',
        description: isEditMode.value ? '页面配置已更新并保存到服务器' : '新页面已创建成功并保存到服务器',
        duration: 3000
      });
      router.push('/pages');
    } else {
      throw new Error(pageStore.error || '保存失败');
    }
  } catch (error) {
    console.error('保存页面失败:', error);
    message.error({
      content: '保存页面失败',
      description: error instanceof Error ? error.message : '未知错误，请检查输入数据或稍后重试',
      duration: 5000
    });
  }
};

// 取消操作
const confirmCancel = () => {
  if (confirm('确定要取消吗？未保存的内容将会丢失')) {
    router.back();
  }
};

// 预览页面
const viewPage = () => {
  if (isEditMode.value) {
    // 获取当前域名和协议
    const protocol = window.location.protocol;
    const hostname = window.location.hostname;
    
    // 定义子系统端口 - 使用环境变量或通过配置获取，默认尝试3002
    // 生产环境可能使用相同端口或不同路径前缀
    const pageServicePort = (import.meta.env.VITE_PAGE_SERVICE_PORT as string) || '3002';
    const pageServicePath = (import.meta.env.VITE_PAGE_SERVICE_PATH as string) || '';
    
    // 构建带有集成ID的查询参数，如果已选择系统集成
    let queryParams = '';
    if (formState.integrationIds && formState.integrationIds.length > 0) {
      // 使用第一个选中的系统集成作为预览参数
      queryParams = `?integrationId=${formState.integrationIds[0]}`;
    }
    
    // 构建完整的子系统URL，根据环境使用不同路径策略
    let targetUrl;
    if (import.meta.env.PROD) {
      // 生产环境可能使用路径前缀而非不同端口
      targetUrl = `${protocol}//${hostname}${pageServicePath}/viewer/${route.params.id}${queryParams}`;
    } else {
      // 开发环境使用不同端口
      targetUrl = `${protocol}//${hostname}:${pageServicePort}/viewer/${route.params.id}${queryParams}`;
    }
    
    console.log('预览URL:', targetUrl);
    
    // 在新标签页中打开预览
    window.open(targetUrl, '_blank');
    message.info({
      content: '正在打开预览',
      description: '正在准备页面预览，即将跳转到真实集成环境中查看效果',
      duration: 3000
    });
  }
};

// 在组件挂载时加载数据
onMounted(() => {
  loadPageData();
});
</script>