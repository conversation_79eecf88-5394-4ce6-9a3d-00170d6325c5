import { ref, computed, watch } from 'vue';
import { message } from 'ant-design-vue';
import type { DataSource } from '@/types/datasource';
import { useDataSourceStore } from '@/stores/datasource';
import { metadataService } from '@/services/metadata';

/**
 * 数据源管理可组合函数
 * 提供数据源选择、加载和元数据管理相关功能
 */
export function useDataSourceManagement() {
  // 获取数据源store
  const dataSourceStore = useDataSourceStore();

  // 数据源相关状态
  const selectedDataSourceId = ref<string>('');
  const selectedSchema = ref<string>('');
  const availableSchemas = ref<any[]>([]);
  const isLoadingDataSources = ref(false);
  const isLoadingSchemas = ref(false);
  const isRefreshingMetadata = ref(false);
  const schemaError = ref<string | null>(null);

  // 计算属性：当前选中的数据源
  const selectedDataSource = computed<DataSource | null>(() => {
    if (!selectedDataSourceId.value) return null;
    return dataSourceStore.dataSources.find(ds => ds.id === selectedDataSourceId.value) || null;
  });

  // 计算属性：当前数据源是否可用
  const isDataSourceActive = computed<boolean>(() => {
    if (!selectedDataSource.value) return false;
    return selectedDataSource.value.status === 'active';
  });

  // 计算属性：处理后的schemas
  const processedSchemas = computed<any[]>(() => {
    if (!selectedDataSourceId.value) return [];
    
    const schemas = dataSourceStore.metadataState.schemas.get(selectedDataSourceId.value) || [];
    return schemas.map(schema => {
      if (typeof schema === 'string') {
        return { 
          value: schema, 
          name: schema,
          tablesCount: undefined,
          dataSourceId: selectedDataSourceId.value
        };
      }
      return {
        value: schema.value || schema.name || 'default',
        name: schema.name || schema.value || 'Default Schema',
        tablesCount: (schema as any).tablesCount,
        dataSourceId: selectedDataSourceId.value
      };
    });
  });

  // 加载数据源列表
  const loadDataSources = async () => {
    isLoadingDataSources.value = true;
    try {
      await dataSourceStore.fetchDataSources();
      console.log('数据源列表加载完成');
      
      // 如果没有选择数据源且有活跃数据源，自动选择第一个
      if (!selectedDataSourceId.value && dataSourceStore.activeDataSources.length > 0) {
        selectedDataSourceId.value = dataSourceStore.activeDataSources[0].id;
        console.log('自动选择第一个活跃数据源:', selectedDataSourceId.value);
      }
    } catch (error) {
      console.error('加载数据源列表失败:', error);
      message.error('加载数据源列表失败');
    } finally {
      isLoadingDataSources.value = false;
    }
  };

  // 监听数据源选择变化
  watch(selectedDataSourceId, async (newId, oldId) => {
    if (newId === oldId) return;
    
    // 清空schema相关状态
    selectedSchema.value = '';
    schemaError.value = null;
    
    if (!newId) return;
    
    // 加载schemas
    isLoadingSchemas.value = true;
    try {
      await dataSourceStore.getSchemas(newId);
    } catch (err) {
      schemaError.value = err instanceof Error ? err.message : '加载Schema失败';
    } finally {
      isLoadingSchemas.value = false;
    }
  });

  // 监听数据源变化，获取可用的Schema列表
  watch(selectedDataSourceId, async (newValue) => {
    console.log('数据源ID变更为:', newValue);
    selectedSchema.value = '';
    availableSchemas.value = [];
    
    if (newValue && selectedDataSource.value) {
      console.log('选中的数据源:', selectedDataSource.value);
      
      if (selectedDataSource.value.status === 'active') {
        try {
          console.log('开始加载数据源的schema列表');
          // 使用元数据服务获取schema列表
          const schemas = await metadataService.getSchemas(newValue);
          
          // 处理schema数据
          if (schemas.length === 1 && typeof schemas[0] === 'object') {
            const schema = schemas[0];
            
            // 检查是否为JSON字符串
            if (typeof schema.value === 'string' && schema.value.startsWith('{')) {
              try {
                // 尝试解析JSON
                const schemaObj = JSON.parse(schema.value);
                
                // 使用解析后的对象创建新的schema元数据
                availableSchemas.value = [{
                  value: schemaObj.id || schemaObj.name || 'default',
                  name: schemaObj.name || schemaObj.id || 'Default Schema',
                  dataSourceId: newValue
                }];
              } catch (e) {
                console.error('解析schema JSON失败:', e);
                availableSchemas.value = schemas;
              }
            } else {
              availableSchemas.value = schemas;
            }
          } else if (schemas && schemas.length > 0) {
            availableSchemas.value = schemas;
          }
          
          // 如果数据源有默认schema，则自动选择
          const dataSource = selectedDataSource.value;
          if (dataSource && dataSource.schema) {
            const defaultSchema = availableSchemas.value.find(s => s.value === dataSource.schema);
            if (defaultSchema) {
              selectedSchema.value = defaultSchema.value;
            }
          } else if (availableSchemas.value.length === 1) {
            // 如果只有一个schema，自动选择它
            selectedSchema.value = availableSchemas.value[0].value;
          }
        } catch (error) {
          console.error('获取Schema列表失败:', error);
          message.error('获取Schema列表失败');
        }
      }
    }
  });

  // 刷新元数据
  const refreshMetadata = async () => {
    if (!selectedDataSourceId.value) return;
    
    isRefreshingMetadata.value = true;
    schemaError.value = null;
    
    try {
      // 清除缓存
      dataSourceStore.clearMetadataCache(selectedDataSourceId.value);
      
      // 重新加载schemas
      await dataSourceStore.getSchemas(selectedDataSourceId.value);
      
      // 如果当前已选择schema，重新加载tables
      if (selectedSchema.value) {
        await dataSourceStore.getTables(selectedDataSourceId.value, selectedSchema.value);
      }
      
      message.success('元数据已刷新');
    } catch (err) {
      console.error('刷新元数据失败:', err);
      schemaError.value = err instanceof Error ? err.message : '刷新元数据失败';
      message.error('刷新元数据失败');
    } finally {
      isRefreshingMetadata.value = false;
    }
  };

  // 处理Schema变更
  const handleSchemaChange = async (schema: string) => {
    selectedSchema.value = schema;
    schemaError.value = null;
    
    if (!schema || !selectedDataSourceId.value) return;
    
    try {
      // 加载表数据
      await dataSourceStore.getTables(selectedDataSourceId.value, schema);
    } catch (err) {
      schemaError.value = err instanceof Error ? err.message : '加载表数据失败';
    }
  };

  return {
    // 状态
    selectedDataSourceId,
    selectedSchema,
    availableSchemas,
    isLoadingDataSources,
    isLoadingSchemas,
    isRefreshingMetadata,
    schemaError,
    
    // 计算属性
    selectedDataSource,
    isDataSourceActive,
    processedSchemas,
    
    // 方法
    loadDataSources,
    refreshMetadata,
    handleSchemaChange,
    
    // 数据源store提供访问
    dataSourceStore
  };
}