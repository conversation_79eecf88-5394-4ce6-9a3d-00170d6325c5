import { ref } from 'vue';
import type { Ref } from 'vue';
import { message } from 'ant-design-vue';
import type { QueryType } from '@/types/query';

/**
 * 查询草稿管理可组合函数
 * 负责处理草稿状态、保存和加载草稿等功能
 */
export function useQueryDraft() {
  // 草稿相关状态
  const lastDraftSaveAt = ref<string | null>(null);
  const isDraftSaving = ref(false);
  const hasDraftChanges = ref(false);
  
  /**
   * 保存查询草稿
   * @param params 保存草稿所需参数
   */
  const saveDraft = async (params: {
    queryId: Ref<string | null>;
    queryText: string;
    queryType: QueryType;
    dataSourceId: string;
  }) => {
    try {
      if (isDraftSaving.value) return;
      
      isDraftSaving.value = true;
      const statusMessage = '正在保存草稿...';
      
      // 验证是否有内容
      if (!params.queryText.trim()) {
        message.info('无内容可保存');
        isDraftSaving.value = false;
        return;
      }
      
      // 更新最后保存草稿时间
      const now = new Date().toISOString();
      lastDraftSaveAt.value = now;
      
      // 根据是否有查询ID决定操作
      if (params.queryId.value) {
        // 已有查询，更新草稿
        // 构造保存草稿的请求数据
        const draftData = {
          id: params.queryId.value,
          queryText: params.queryText,
          queryType: params.queryType,
          dataSourceId: params.dataSourceId
        };
        
        // 这里可以调用API保存草稿
        // 目前使用模拟延时代替实际API调用
        await simulateDelay(500, 1000);
        
        message.success('草稿已保存');
      } else {
        // 新查询，首次保存草稿，静默存储到本地
        localStorage.setItem('query_draft_text', params.queryText);
        localStorage.setItem('query_draft_type', params.queryType);
        localStorage.setItem('query_draft_datasource', params.dataSourceId);
        localStorage.setItem('query_draft_timestamp', now);
        
        message.success('草稿已临时保存');
      }
      
      // 设置保存成功后的状态
      hasDraftChanges.value = false;
    } catch (error) {
      console.error('保存草稿失败:', error);
      message.error('保存草稿失败: ' + (error instanceof Error ? error.message : String(error)));
    } finally {
      isDraftSaving.value = false;
    }
  };
  
  /**
   * 加载最近的草稿
   */
  const loadRecentDraft = () => {
    try {
      const draftText = localStorage.getItem('query_draft_text');
      const draftType = localStorage.getItem('query_draft_type');
      const draftTimestamp = localStorage.getItem('query_draft_timestamp');
      const draftDataSource = localStorage.getItem('query_draft_datasource');
      
      if (draftText && draftTimestamp) {
        lastDraftSaveAt.value = draftTimestamp;
        
        return {
          text: draftText,
          type: draftType as QueryType,
          dataSourceId: draftDataSource || '',
          timestamp: draftTimestamp
        };
      }
      
      return null;
    } catch (error) {
      console.error('加载草稿失败:', error);
      return null;
    }
  };
  
  /**
   * 清除草稿
   */
  const clearDraft = () => {
    localStorage.removeItem('query_draft_text');
    localStorage.removeItem('query_draft_type');
    localStorage.removeItem('query_draft_timestamp');
    localStorage.removeItem('query_draft_datasource');
    lastDraftSaveAt.value = null;
    hasDraftChanges.value = false;
  };
  
  /**
   * 格式化最后保存时间
   * @param dateString 日期字符串
   */
  const formatLastSavedTime = (dateString: string | number | Date | null): string => {
    if (!dateString) return '未知时间';
    
    try {
      const date = new Date(dateString);
      
      // 格式化为"今天 12:34"或"2023-01-01 12:34"格式
      const now = new Date();
      const isToday = date.getDate() === now.getDate() && 
                    date.getMonth() === now.getMonth() && 
                    date.getFullYear() === now.getFullYear();
      
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      
      if (isToday) {
        return `今天 ${hours}:${minutes}`;
      } else {
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        return `${year}-${month}-${day} ${hours}:${minutes}`;
      }
    } catch (e) {
      console.error('日期格式化错误:', e);
      return String(dateString);
    }
  };
  
  /**
   * 设置草稿有变更
   */
  const setDraftChanged = () => {
    hasDraftChanges.value = true;
  };
  
  /**
   * 模拟延时函数
   */
  const simulateDelay = (min: number, max: number) => {
    const delay = Math.floor(Math.random() * (max - min + 1)) + min;
    return new Promise(resolve => setTimeout(resolve, delay));
  };
  
  return {
    lastDraftSaveAt,
    isDraftSaving,
    hasDraftChanges,
    saveDraft,
    loadRecentDraft,
    clearDraft,
    formatLastSavedTime,
    setDraftChanged
  };
}