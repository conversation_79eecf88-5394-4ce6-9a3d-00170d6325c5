import { computed, ref } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import type { RouteLocationRaw } from 'vue-router';
import { message } from 'ant-design-vue';

/**
 * 查询导航可组合函数
 * 负责处理查询页面的路由导航、退出提示等功能
 */
export function useQueryNavigation() {
  const router = useRouter();
  const route = useRoute();
  
  // 状态
  const isNavigating = ref(false);
  const hasUnsavedChanges = ref(false);
  
  /**
   * 计算返回URL
   */
  const backUrl = computed(() => {
    // 如果是从详情页过来，则返回详情页
    if (route.query.from === "detail" && route.query.id) {
      return `/query/detail/${route.query.id}`;
    }
    // 默认返回列表页
    return "/query/list";
  });
  
  /**
   * 返回列表页
   */
  const returnToList = () => {
    navigateTo('/query/list');
  };
  
  /**
   * 返回查询详情页
   * @param queryId 查询ID
   */
  const returnToDetail = (queryId: string) => {
    navigateTo(`/query/detail/${queryId}`);
  };
  
  /**
   * 导航到查询版本管理页面
   * @param queryId 查询ID
   */
  const navigateToVersions = (queryId: string) => {
    navigateTo(`/query/version/management/${queryId}`);
  };
  
  /**
   * 导航到指定页面
   * @param to 目标路由
   */
  const navigateTo = (to: RouteLocationRaw) => {
    if (isNavigating.value) return;
    
    isNavigating.value = true;
    
    try {
      // 如果有未保存的更改，显示确认对话框
      if (hasUnsavedChanges.value) {
        const confirmed = window.confirm('有未保存的更改，确定要离开吗？');
        if (!confirmed) {
          isNavigating.value = false;
          return;
        }
      }
      
      // 执行导航
      router.push(to);
    } catch (error) {
      console.error('导航失败:', error);
      message.error('导航失败: ' + (error instanceof Error ? error.message : String(error)));
    } finally {
      isNavigating.value = false;
    }
  };
  
  /**
   * 更新URL参数而不导航
   * @param params 参数对象
   */
  const updateQueryParams = (params: Record<string, string>) => {
    // 获取当前查询参数
    const currentQuery = { ...route.query };
    
    // 合并新参数
    Object.entries(params).forEach(([key, value]) => {
      if (value) {
        currentQuery[key] = value;
      } else {
        delete currentQuery[key];
      }
    });
    
    // 替换当前URL，不执行实际跳转
    router.replace({ 
      path: route.path, 
      query: currentQuery 
    });
  };
  
  /**
   * 设置是否有未保存的更改
   * @param value 是否有未保存的更改
   */
  const setHasUnsavedChanges = (value: boolean) => {
    hasUnsavedChanges.value = value;
  };
  
  /**
   * 获取URL参数
   * @param name 参数名
   * @param defaultValue 默认值
   */
  const getUrlParam = (name: string, defaultValue: string = '') => {
    return route.query[name] as string || defaultValue;
  };
  
  return {
    backUrl,
    isNavigating,
    hasUnsavedChanges,
    returnToList,
    returnToDetail,
    navigateToVersions,
    navigateTo,
    updateQueryParams,
    setHasUnsavedChanges,
    getUrlParam
  };
}