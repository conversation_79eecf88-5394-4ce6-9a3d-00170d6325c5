import { ref, computed } from 'vue';
import { message } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import { useQueryStore } from '@/stores/query';
import type { QueryType } from '@/types/query';

/**
 * 查询保存可组合函数
 * 提供查询保存、发布等相关功能
 */
export function useQuerySave() {
  // 状态
  const isComponentMounted = ref(true);
  const isSaveModalVisible = ref(false);
  const isSaving = ref(false);
  const isPublishing = ref(false);
  const statusMessage = ref<string | null>(null);
  const currentQueryId = ref<string | null>(null);
  const hasChanges = ref(false);
  
  // 路由管理
  const router = useRouter();
  
  // 查询store
  const queryStore = useQueryStore();
  
  // 打开保存对话框
  const openSaveModal = (validationFn?: () => boolean) => {
    // 检查组件是否已卸载
    if (!isComponentMounted.value) return;
    
    // 如果有验证函数，先执行验证
    if (validationFn && !validationFn()) {
      return;
    }
    
    console.log('打开保存对话框');
    
    // 显示保存对话框
    isSaveModalVisible.value = true;
  };
  
  // 显示状态消息
  const showStatusMessage = (msg: string, duration: number = 3000) => {
    statusMessage.value = msg;
    setTimeout(() => {
      if (isComponentMounted.value) {
        statusMessage.value = null;
      }
    }, duration);
  };
  
  // 处理保存查询
  const handleSaveQuery = async (saveData: any, queryData: any) => {
    // 在保存前确保数据源ID正确
    console.log('保存查询 - 从对话框接收到的saveData:', saveData);
    console.log('保存查询 - 从页面接收到的queryData:', queryData);
    
    // 核对数据源ID并确保使用编辑页面的ID
    if (saveData.dataSourceId !== queryData.dataSourceId) {
      console.warn('数据源ID不一致！对话框ID=', saveData.dataSourceId, '页面ID=', queryData.dataSourceId);
      // 使用页面的数据源ID覆盖对话框的ID
      saveData.dataSourceId = queryData.dataSourceId;
      console.log('已强制更新保存数据的dataSourceId为:', queryData.dataSourceId);
    }
    try {
      // 如果是从保存对话框调用，关闭对话框
      isSaveModalVisible.value = false;
      isSaving.value = true;
      statusMessage.value = '正在保存查询...';
      
      // 验证必填字段
      if (!queryData.dataSourceId) {
        throw new Error('请选择数据源');
      }
      
      // 确定查询类型与内容
      let queryText = '';
      let queryType = '';
      
      if (queryData.queryType === 'sql') {
        queryText = queryData.sql;
        queryType = 'sql';
        if (!queryText) {
          throw new Error('SQL查询不能为空');
        }
      } else {
        queryText = queryData.naturalLanguageQuery;
        queryType = 'natural-language';
        if (!queryText) {
          throw new Error('自然语言查询不能为空');
        }
      }
      
      // 准备保存的数据
      const data = {
        id: saveData?.id || currentQueryId.value, // 确保ID被正确传递
        name: saveData?.name || queryData.name,
        description: saveData?.description || queryData.description,
        tags: saveData?.tags || queryData.tags,
        dataSourceId: queryData.dataSourceId,
        sql: queryData.queryType === 'sql' ? queryText : '',
        naturalLanguageQuery: queryData.queryType === 'natural-language' ? queryText : '',
        queryType,
        isPublished: false
      };
      
      console.log('保存查询数据:', JSON.stringify(data));
      
      let result;
      
      // 根据是否有查询ID决定创建还是更新
      if (data.id) {
        // 更新现有查询
        result = await queryStore.saveQuery(data);
      } else {
        // 创建新查询
        result = await queryStore.createQuery(data);
      }
      
      // 处理保存成功
      return handleSaveSuccess(result, data.name);
    } catch (error) {
      console.error('保存查询失败:', error);
      
      let errorMsg = '';
      if (error instanceof Error) {
        errorMsg = error.message;
      } else if (typeof error === 'string') {
        errorMsg = error;
      } else {
        errorMsg = '保存查询时发生未知错误';
      }
      
      message.error(errorMsg);
      statusMessage.value = `保存失败: ${errorMsg}`;
      
      setTimeout(() => {
        if (isComponentMounted.value) {
          statusMessage.value = null;
        }
      }, 5000);
      
      return null;
    } finally {
      isSaving.value = false;
    }
  };
  
  // 处理保存成功
  const handleSaveSuccess = (result: any, queryName: string) => {
    if (!result) {
      message.error('保存查询失败: 未收到响应');
      return null;
    }
    
    // 更新查询ID（如果是新创建的）
    if (result.id && !currentQueryId.value) {
      currentQueryId.value = result.id;
      
      // 更新URL，添加查询ID参数
      const currentRoute = router.currentRoute.value;
      router.replace({ 
        path: currentRoute.path, 
        query: { ...currentRoute.query, id: result.id } 
      });
      console.log(`查询已保存，ID: ${result.id}，已更新URL参数`);
    } else if (result.id) {
      // 确保更新现有查询时也更新URL
      const currentRoute = router.currentRoute.value;
      if (!currentRoute.query.id || currentRoute.query.id !== result.id) {
        router.replace({
          path: currentRoute.path,
          query: { ...currentRoute.query, id: result.id }
        });
        console.log(`更新现有查询，ID: ${result.id}，已更新URL参数`);
      }
    }
    
    // 重置变更状态
    hasChanges.value = false;
    
    // 显示成功消息
    message.success('查询已保存');
    statusMessage.value = '查询已成功保存';
    
    setTimeout(() => {
      if (isComponentMounted.value) {
        statusMessage.value = null;
      }
    }, 3000);
    
    return result;
  };
  
  // 发布查询
  const publishQuery = async (queryId: string, queryData: any) => {
    if (!queryId) {
      message.error('无法发布未保存的查询');
      return null;
    }
    
    isPublishing.value = true;
    
    try {
      statusMessage.value = '正在发布查询...';
      
      // 使用API发布查询
      const result = await queryStore.publishQuery(queryId);
      
      message.success('查询已发布');
      statusMessage.value = '查询已发布';
      
      setTimeout(() => {
        if (isComponentMounted.value) {
          statusMessage.value = null;
        }
      }, 3000);
      
      // 重置变更状态
      hasChanges.value = false;
      
      return result;
    } catch (error) {
      console.error('发布查询失败:', error);
      
      let errorMsg = '';
      if (error instanceof Error) {
        errorMsg = error.message;
      } else if (typeof error === 'string') {
        errorMsg = error;
      } else {
        errorMsg = '发布查询时发生未知错误';
      }
      
      message.error(errorMsg);
      statusMessage.value = `发布失败: ${errorMsg}`;
      
      setTimeout(() => {
        if (isComponentMounted.value) {
          statusMessage.value = null;
        }
      }, 5000);
      
      return null;
    } finally {
      isPublishing.value = false;
    }
  };
  
  // 保存草稿
  const saveDraft = async (queryType: QueryType, queryText: string, dataSourceId: string, queryId: string | null) => {
    try {
      statusMessage.value = '正在保存草稿...';
      
      // 验证是否有内容
      if (!queryText.trim()) {
        statusMessage.value = '无内容可保存';
        setTimeout(() => {
          if (isComponentMounted.value) {
            statusMessage.value = null;
          }
        }, 3000);
        return;
      }
      
      // 根据是否有查询ID决定操作
      if (queryId) {
        // 已有查询，更新草稿
        const draftData = {
          id: queryId,
          queryText,
          queryType
        };
        
        // 调用API保存草稿
        await queryStore.saveQueryDraft(draftData);
        
        message.success('草稿已保存');
        statusMessage.value = '草稿已保存';
      } else {
        // 新查询，首次保存草稿，静默存储到本地
        localStorage.setItem('query_draft_text', queryText);
        localStorage.setItem('query_draft_type', queryType);
        localStorage.setItem('query_draft_timestamp', new Date().toISOString());
        
        message.success('草稿已临时保存');
        statusMessage.value = '草稿已临时保存';
      }
      
      // 重置变更状态
      hasChanges.value = false;
      
      setTimeout(() => {
        if (isComponentMounted.value) {
          statusMessage.value = null;
        }
      }, 3000);
      
      return true;
    } catch (error) {
      console.error('保存草稿失败:', error);
      message.error('保存草稿失败');
      statusMessage.value = '保存草稿失败';
      
      setTimeout(() => {
        if (isComponentMounted.value) {
          statusMessage.value = null;
        }
      }, 5000);
      
      return false;
    }
  };
  
  // 组件卸载时清理资源
  const cleanup = () => {
    isComponentMounted.value = false;
  };
  
  return {
    // 状态
    isSaveModalVisible,
    isSaving,
    isPublishing,
    statusMessage,
    currentQueryId,
    hasChanges,
    
    // 方法
    openSaveModal,
    handleSaveQuery,
    publishQuery,
    saveDraft,
    showStatusMessage,
    cleanup,
    
    // 查询store提供访问
    queryStore
  };
}