import { ref, computed, watch, nextTick } from 'vue';
import { useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import { debounce } from 'lodash-es';

import type { QueryType } from '@/types/query';

/**
 * 查询状态管理可组合函数
 * 提供查询表单状态、编辑状态和变更检测等功能
 */
export function useQueryState() {
  // 路由
  const route = useRoute();
  
  // 基本状态
  const activeTab = ref<string>('editor'); // 改为与QueryEditorPage中使用的标签值匹配
  const sql = ref<string>('');
  const naturalLanguageQuery = ref<string>('');
  const name = ref<string>('');
  const description = ref<string>('');
  const tags = ref<string[]>([]);
  const results = ref<any[]>([]);
  const hasChanges = ref<boolean>(false);
  const lastEditedAt = ref<string | null>(null);
  const selectedDataSourceId = ref<string>('');
  const selectedSchema = ref<string>('');
  const selectedTable = ref<string>('');
  const tableData = ref<any[]>([]);
  const lastSavedTime = ref<string | null>(null);
  const isNewQuery = ref<boolean>(true);
  const isFormDisabled = ref<boolean>(false);

  // 查询类型计算属性
  const queryType = computed<QueryType>(() => {
    return activeTab.value === 'editor' || activeTab.value === 'sql' ? 'SQL' : 'NATURAL_LANGUAGE';
  });

  // 当前查询文本计算属性
  const queryText = computed<string>(() => {
    return queryType.value === 'SQL' ? sql.value : naturalLanguageQuery.value;
  });

  // 输入框高度自适应
  const adjustTextAreaHeight = () => {
    nextTick(() => {
      const textareas = document.querySelectorAll('textarea.auto-height');
      textareas.forEach((textarea: HTMLTextAreaElement) => {
        textarea.style.height = 'auto';
        textarea.style.height = `${textarea.scrollHeight}px`;
      });
    });
  };

  // 监视查询文本变化
  watch([sql, naturalLanguageQuery], () => {
    hasChanges.value = true;
    lastEditedAt.value = new Date().toISOString();
    adjustTextAreaHeight();
  });

  // 监视其他表单字段变化
  watch([name, description, tags, selectedDataSourceId], () => {
    hasChanges.value = true;
  });

  // 创建防抖版本的调整高度函数
  const debouncedAdjustHeight = debounce(adjustTextAreaHeight, 200);

  // 切换标签页
  const switchTab = (tab: string) => {
    activeTab.value = tab;
    debouncedAdjustHeight();
  };

  // 重置表单状态
  const resetForm = () => {
    sql.value = '';
    naturalLanguageQuery.value = '';
    name.value = '';
    description.value = '';
    tags.value = [];
    results.value = [];
    hasChanges.value = false;
    lastEditedAt.value = null;
    selectedDataSourceId.value = '';
    selectedSchema.value = '';
    selectedTable.value = '';
    tableData.value = [];
    lastSavedTime.value = null;
    isNewQuery.value = true;
  };

  // 初始化查询状态
  const initQueryState = (queryData: any = null) => {
    if (!queryData) {
      resetForm();
      return;
    }

    // 设置基本信息
    name.value = queryData.name || '';
    description.value = queryData.description || '';
    tags.value = queryData.tags || [];
    selectedDataSourceId.value = queryData.dataSourceId || '';
    
    // 设置查询类型和文本
    if (queryData.queryType === 'NATURAL_LANGUAGE') {
      naturalLanguageQuery.value = queryData.naturalLanguageQuery || queryData.queryText || '';
      activeTab.value = 'nlq';
    } else {
      sql.value = queryData.sql || queryData.queryText || '';
      activeTab.value = 'editor';
    }

    // 更新其他状态
    isNewQuery.value = !queryData.id;
    lastSavedTime.value = queryData.updatedAt || null;
    hasChanges.value = false;
    
    // 调整文本区域高度
    nextTick(() => {
      adjustTextAreaHeight();
    });
  };

  // 格式化最后保存时间
  const formatLastSavedTime = (timestamp: string | null): string => {
    if (!timestamp) return '未保存';
    
    try {
      const date = new Date(timestamp);
      return date.toLocaleString();
    } catch (e) {
      console.error('格式化时间戳失败:', e);
      return '时间格式错误';
    }
  };

  // 检测URL中的查询ID
  const getQueryIdFromRoute = (): string | null => {
    return route.params.id as string || null;
  };

  // 导入查询示例
  const importQueryExample = (example: any) => {
    if (!example) return;
    
    try {
      if (example.queryType === 'sql' || example.queryType === 'SQL') {
        sql.value = example.query || '';
        activeTab.value = 'editor';
      } else {
        naturalLanguageQuery.value = example.query || '';
        activeTab.value = 'nlq';
      }
      
      selectedDataSourceId.value = example.dataSourceId || selectedDataSourceId.value;
      hasChanges.value = true;
      
      message.success('示例已导入');
      
      nextTick(() => {
        adjustTextAreaHeight();
      });
    } catch (error) {
      console.error('导入示例失败:', error);
      message.error('导入示例失败');
    }
  };

  // 从本地存储加载草稿
  const loadDraftFromLocalStorage = () => {
    try {
      const draftText = localStorage.getItem('query_draft_text');
      const draftType = localStorage.getItem('query_draft_type');
      const draftTimestamp = localStorage.getItem('query_draft_timestamp');
      
      if (!draftText || !draftType || !draftTimestamp) {
        return false;
      }
      
      // 确保草稿不超过24小时
      const timestamp = new Date(draftTimestamp).getTime();
      const now = new Date().getTime();
      const hoursDiff = (now - timestamp) / (1000 * 60 * 60);
      
      if (hoursDiff > 24) {
        // 草稿过期，清除
        localStorage.removeItem('query_draft_text');
        localStorage.removeItem('query_draft_type');
        localStorage.removeItem('query_draft_timestamp');
        return false;
      }
      
      // 加载草稿
      if (draftType === 'sql' || draftType === 'SQL') {
        sql.value = draftText;
        activeTab.value = 'editor';
      } else {
        naturalLanguageQuery.value = draftText;
        activeTab.value = 'nlq';
      }
      
      lastEditedAt.value = draftTimestamp;
      hasChanges.value = true;
      
      nextTick(() => {
        adjustTextAreaHeight();
      });
      
      return true;
    } catch (error) {
      console.error('加载本地草稿失败:', error);
      return false;
    }
  };

  // 禁用/启用表单
  const setFormEnabled = (enabled: boolean) => {
    isFormDisabled.value = !enabled;
  };

  return {
    // 状态
    activeTab,
    sql,
    naturalLanguageQuery,
    name,
    description,
    tags,
    results,
    hasChanges,
    lastEditedAt,
    selectedDataSourceId,
    selectedSchema,
    selectedTable,
    tableData,
    lastSavedTime,
    isNewQuery,
    isFormDisabled,
    
    // 计算属性
    queryType,
    queryText,
    
    // 方法
    switchTab,
    resetForm,
    initQueryState,
    formatLastSavedTime,
    getQueryIdFromRoute,
    importQueryExample,
    loadDraftFromLocalStorage,
    setFormEnabled,
    adjustTextAreaHeight
  };
}