import { ref } from 'vue';
import { message } from 'ant-design-vue';
import type { QueryType } from '@/types/query';

/**
 * 查询验证可组合函数
 * 负责处理查询验证、错误消息管理等功能
 */
export function useQueryValidation() {
  // 错误状态
  const queryError = ref<string | null>(null);
  const errorMessage = ref<string | null>(null);
  const statusMessage = ref<string | null>(null);
  
  /**
   * 验证SQL查询
   * @param queryText SQL查询文本
   * @returns 错误消息，无错误返回null
   */
  const validateSqlQuery = (queryText: string): string | null => {
    const trimmedSQL = queryText.trim().toLowerCase();
    
    // 检查是否是简单的SELECT *
    if (trimmedSQL === 'select *') {
      return '不完整的SQL语句，请指定表名';
    }
    
    // 检查SELECT * FROM 后是否有表名
    if (trimmedSQL.startsWith('select * from') && trimmedSQL.split(' ').length <= 3) {
      return '请在FROM子句后指定表名';
    }
    
    // 检查SQL语句是否有基本语法要素
    if (!trimmedSQL.includes('select')) {
      return 'SQL语句需要包含SELECT关键字';
    }
    
    if (!trimmedSQL.includes('from') && trimmedSQL.includes('select')) {
      return 'SQL语句可能缺少FROM子句';
    }
    
    // 括号匹配检查
    const openCount = (trimmedSQL.match(/\(/g) || []).length;
    const closeCount = (trimmedSQL.match(/\)/g) || []).length;
    if (openCount !== closeCount) {
      return '括号不匹配，请检查SQL语法';
    }
    
    // 没有错误
    return null;
  };
  
  /**
   * 验证自然语言查询
   * @param queryText 自然语言查询文本
   * @returns 错误消息，无错误返回null
   */
  const validateNaturalLanguageQuery = (queryText: string): string | null => {
    if (!queryText || queryText.trim().length === 0) {
      return '自然语言查询不能为空';
    }
    
    if (queryText.trim().length < 5) {
      return '请输入更详细的问题，至少5个字符';
    }
    
    return null;
  };
  
  /**
   * 验证查询执行条件
   * @param params 验证参数
   * @returns 是否通过验证
   */
  const validateQueryExecution = (params: {
    dataSourceId: string;
    activeTab: 'editor' | 'nlq' | 'builder';
    sqlQuery: string;
    naturalLanguageQuery: string;
    builderQuery: string;
  }): boolean => {
    // 检查数据源
    if (!params.dataSourceId) {
      setErrorMessage('请先选择数据源');
      return false;
    }
    
    // 根据当前活动标签页验证查询内容
    if (params.activeTab === 'editor') {
      if (!params.sqlQuery || params.sqlQuery.trim().length === 0) {
        setErrorMessage('SQL查询不能为空');
        return false;
      }
      
      // 进行SQL语法简单验证
      const sqlError = validateSqlQuery(params.sqlQuery);
      if (sqlError) {
        setErrorMessage(sqlError);
        return false;
      }
    } else if (params.activeTab === 'nlq') {
      if (!params.naturalLanguageQuery || params.naturalLanguageQuery.trim().length === 0) {
        setErrorMessage('自然语言查询不能为空');
        return false;
      }
      
      // 验证自然语言查询
      const nlqError = validateNaturalLanguageQuery(params.naturalLanguageQuery);
      if (nlqError) {
        setErrorMessage(nlqError);
        return false;
      }
    } else if (params.activeTab === 'builder') {
      if (!params.builderQuery || params.builderQuery.trim().length === 0) {
        setErrorMessage('查询构建器未生成有效的查询');
        return false;
      }
    }
    
    // 验证通过
    return true;
  };
  
  /**
   * 处理查询错误
   * @param error 错误对象
   */
  const handleQueryError = (error: any) => {
    console.error('查询执行错误:', error);
    
    let errorMsg = '';
    
    // 处理特定类型的错误
    if (error instanceof Error) {
      errorMsg = error.message;
    } else if (typeof error === 'object' && error !== null) {
      // 处理API错误响应
      if (error.message && error.message.includes("Field 'version_id' doesn't have a default value")) {
        errorMsg = '查询版本信息不完整，请确保选择了正确的版本';
      } else if (error.message) {
        errorMsg = error.message;
      } else {
        errorMsg = '执行查询时发生错误';
      }
    } else if (typeof error === 'string') {
      errorMsg = error;
    } else {
      errorMsg = '未知错误';
    }
    
    queryError.value = errorMsg;
    statusMessage.value = `执行失败：${errorMsg}`;
    
    // 设置顶部错误消息
    setErrorMessage(`执行查询失败: ${errorMsg}`);
    
    // 显示错误提示
    message.error(errorMsg);
  };
  
  /**
   * 设置错误消息
   * @param error 错误消息
   */
  const setErrorMessage = (error: string) => {
    errorMessage.value = error;
    
    // 自动在5秒后清除错误
    setTimeout(clearErrorMessage, 5000);
  };
  
  /**
   * 清除错误消息
   */
  const clearErrorMessage = () => {
    errorMessage.value = null;
  };
  
  /**
   * 清除所有错误状态
   */
  const clearAllErrors = () => {
    queryError.value = null;
    errorMessage.value = null;
    statusMessage.value = null;
  };
  
  /**
   * 设置状态消息
   * @param message 状态消息
   * @param duration 显示时长 (毫秒)
   */
  const setStatusMessage = (msg: string, duration = 3000) => {
    statusMessage.value = msg;
    
    if (duration > 0) {
      setTimeout(() => {
        if (statusMessage.value === msg) {
          statusMessage.value = null;
        }
      }, duration);
    }
  };
  
  return {
    queryError,
    errorMessage,
    statusMessage,
    validateSqlQuery,
    validateNaturalLanguageQuery,
    validateQueryExecution,
    handleQueryError,
    setErrorMessage,
    clearErrorMessage,
    clearAllErrors,
    setStatusMessage
  };
}