# QueryEditor组件重构计划

## 当前问题

QueryEditor.vue组件有以下问题：
- 文件超过1800行代码，维护困难
- 混合了多种职责（UI渲染、状态管理、API调用等）
- 重复的逻辑和函数定义
- 存在类型错误和未使用的导入
- 存在多处冗余代码

## 重构目标

1. 将大型组件分解为多个小型、专注的组件
2. 提取复用逻辑到composable函数中
3. 改进类型定义，减少类型错误
4. 消除重复代码
5. 遵循单一职责原则
6. 提高组件可测试性

## 拆分计划

### 1. 核心组件拆分

将QueryEditor.vue拆分为以下组件：

- **QueryEditorPage.vue** - 页面容器，负责加载和协调其他组件
- **QueryEditorToolbar.vue** - 工具栏组件，包含执行、取消、保存等操作按钮
- **QueryStatusBar.vue** - 状态栏组件，显示执行状态、错误信息等
- **QueryVersionBar.vue** - 版本管理栏，显示和管理当前查询版本

### 2. 可组合函数拆分

将业务逻辑提取到以下composable函数中：

- **useQueryState.ts** - 管理查询状态（已创建）
- **useQuerySave.ts** - 处理查询保存逻辑（已创建）
- **useQueryExecution.ts** - 处理查询执行逻辑（已创建）
- **useQueryVersion.ts** - 处理版本管理（已创建）
- **useQueryDraft.ts** - 新增：处理草稿保存和加载
- **useQueryValidation.ts** - 新增：处理查询验证逻辑
- **useQueryNavigation.ts** - 新增：处理导航和路由逻辑

### 3. 服务层拆分

提取API调用到专门的服务：

- **queryService.ts** - 处理查询相关API调用
- **versionService.ts** - 处理版本相关API调用
- **datasourceService.ts** - 处理数据源相关API调用

### 4. 类型定义改进

创建更详细的类型定义文件：

- **types/query.ts** - 查询相关类型
- **types/queryVersion.ts** - 版本相关类型
- **types/queryEditor.ts** - 编辑器相关类型

## 实施路线图

### 阶段1：准备工作

1. 创建必要的类型定义文件
2. 调整现有的composable函数以适应新结构
3. 创建新的服务文件

### 阶段2：提取组件

1. 创建QueryEditorPage.vue作为主容器
2. 将工具栏逻辑提取到QueryEditorToolbar.vue
3. 将状态显示逻辑提取到QueryStatusBar.vue
4. 将版本管理UI提取到QueryVersionBar.vue

### 阶段3：连接集成

1. 在主页面组件中引用和组装所有子组件
2. 通过props和事件在组件间建立通信
3. 使用provide/inject处理深层组件通信

### 阶段4：测试与优化

1. 为每个组件编写单元测试
2. 进行集成测试
3. 性能优化

## 文件结构

重构后的文件结构建议：

```
src/
  views/
    query/
      QueryEditorPage.vue         # 主页面容器
      components/                  # 子组件
        QueryEditorToolbar.vue
        QueryStatusBar.vue
        QueryVersionBar.vue
      composables/                 # 可组合函数
        useQueryState.ts           # 已创建
        useQuerySave.ts            # 已创建
        useQueryExecution.ts       # 已创建
        useQueryDraft.ts           # 新增
        useQueryValidation.ts      # 新增
        useQueryNavigation.ts      # 新增
      services/                    # 服务层
        queryService.ts
        versionService.ts
        datasourceService.ts
  composables/
    useQueryVersion.ts             # 已创建
  types/
    query.ts                       # 已有
    queryVersion.ts                # 已有
    queryEditor.ts                 # 新增
```

## 工作量评估

- 总行数：约1800行
- 预计拆分为：8-10个组件，每个约100-200行
- 预计工作时间：3-5天

## 收益

- 提高代码可维护性
- 降低修改风险
- 提升组件复用性
- 更好的代码组织和结构
- 更容易理解的业务逻辑
- 更好的测试覆盖