<template>
  <div class="container mx-auto px-4 py-6">
    <!-- 页面标题和操作栏 -->
    <div class="page-header mb-6">
      <div class="flex justify-between items-center">
        <h1 class="text-2xl font-bold text-gray-900">查询服务</h1>

        <div class="flex space-x-2">
          <button
            @click="navigateToFavorites"
            class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <i class="fas fa-star text-yellow-500 mr-2"></i>我的收藏
          </button>

          <button
            @click="navigateToEditor"
            class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <i class="fas fa-plus mr-2"></i>新建查询
          </button>
        </div>
      </div>
    </div>

    <!-- 筛选区域 -->
    <div class="mb-6 bg-white p-4 rounded-lg shadow">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <!-- 搜索框 -->
        <div>
          <label for="search" class="block text-sm font-medium text-gray-700 mb-1">
            搜索
          </label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <i class="fas fa-search text-gray-400"></i>
            </div>
            <input
              type="text"
              name="search"
              id="search"
              v-model="searchTerm"
              class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              placeholder="搜索查询名称或描述"
            />
          </div>
        </div>

        <!-- 查询类型筛选 -->
        <div>
          <label for="queryTypeFilter" class="block text-sm font-medium text-gray-700 mb-1">
            查询类型
          </label>
          <div class="relative select-container">
            <select
              id="queryTypeFilter"
              v-model="queryTypeFilter"
              class="block w-full py-2 pl-3 pr-10 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md appearance-none"
            >
              <option value="">所有类型</option>
              <option value="SQL">SQL</option>
            </select>
          </div>
        </div>

        <!-- 服务状态筛选 -->
        <div>
          <label for="serviceStatusFilter" class="block text-sm font-medium text-gray-700 mb-1">
            服务状态
          </label>
          <div class="relative select-container">
            <select
              id="serviceStatusFilter"
              v-model="serviceStatusFilter"
              class="block w-full py-2 pl-3 pr-10 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md appearance-none"
            >
              <option value="">所有状态</option>
              <option value="ENABLED">已启用</option>
              <option value="DISABLED">已禁用</option>
            </select>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex items-end space-x-2">
          <button
            @click="clearFilters"
            class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <i class="fas fa-sync-alt mr-2"></i>
            重置筛选
          </button>
          <button
            @click="fetchQueries"
            class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <i class="fas fa-search mr-2"></i>
            查询
          </button>
        </div>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="bg-white shadow rounded-lg">
      <!-- 加载中 -->
      <div v-if="isLoading" class="p-10 text-center">
        <i class="fas fa-circle-notch fa-spin text-indigo-500 text-3xl mb-4"></i>
        <p class="text-gray-500">正在加载查询服务列表...</p>
      </div>

      <!-- 无数据时 -->
      <div v-else-if="queries.length === 0" class="p-10 text-center">
        <div class="rounded-full bg-gray-100 h-16 w-16 flex items-center justify-center mx-auto mb-4">
          <i class="fas fa-search text-gray-400 text-2xl"></i>
        </div>
        <h3 class="text-sm font-medium text-gray-900">暂无查询服务</h3>
        <p class="mt-1 text-sm text-gray-500">
          {{ !!searchTerm || !!serviceStatusFilter ? '没有符合筛选条件的查询服务' : '暂无数据' }}
        </p>
        <div class="mt-6">
          <button
            @click="navigateToEditor"
            class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <i class="fas fa-plus mr-2"></i>
            新建查询
          </button>
        </div>
      </div>

      <!-- 数据表格 -->
      <div v-else class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                名称
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                数据源
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                查询类型
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                服务状态
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                创建时间
              </th>
              <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="query in queries" :key="query.id" @click="viewQueryDetail(query)" class="hover:bg-gray-50 cursor-pointer">
              <td class="px-6 py-4 whitespace-nowrap" @click.stop>
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-10 w-10 flex items-center justify-center rounded-md bg-indigo-100 text-indigo-600">
                    <i class="fas fa-database"></i>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-indigo-600 hover:text-indigo-800 cursor-pointer" @click="viewQueryDetail(query)">
                      {{ query.name || '未命名查询' }}
                    </div>
                    <div v-if="query.description" class="text-sm text-gray-500 max-w-md truncate">
                      {{ query.description }}
                    </div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap" @click.stop>
                <div class="text-sm text-gray-900">{{ query.dataSourceName || '未指定' }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap" @click.stop>
                <div class="flex items-center">
                  <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-indigo-100 text-indigo-800">
                    {{ query.queryType === 'SQL' ? 'SQL' : '自然语言' }}
                  </span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap" @click.stop>
                <div class="flex items-center">
                  <span
                    class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                    :class="{
                      'bg-blue-100 text-blue-800': query.serviceStatus && query.serviceStatus.toUpperCase() === 'ENABLED',
                      'bg-gray-100 text-gray-800': !query.serviceStatus || query.serviceStatus.toUpperCase() !== 'ENABLED'
                    }"
                  >
                    {{ query.serviceStatus && query.serviceStatus.toUpperCase() === 'ENABLED' ? '已启用' : '已禁用' }}
                  </span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" @click.stop>
                {{ formatDate(query.createdAt) }}
                <div v-if="query.lastExecutedAt" class="text-xs text-gray-500 mt-1">
                  <span class="font-medium">上次执行:</span> {{ formatRelativeTime(query.lastExecutedAt) }}
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium" @click.stop>
                <button
                  @click.stop="viewQueryDetail(query)"
                  class="text-blue-600 hover:text-blue-900 mx-1"
                  title="查看详情"
                >
                  <i class="fas fa-eye"></i>
                </button>
                <button
                  @click.stop="editQuery(query)"
                  class="text-indigo-600 hover:text-indigo-900 mx-1"
                  title="编辑查询"
                >
                  <i class="fas fa-edit"></i>
                </button>
                <button
                  @click.stop="toggleQueryStatus(query)"
                  :class="[
                    'mx-1',
                    query.serviceStatus && query.serviceStatus.toUpperCase() === 'ENABLED' ? 'text-gray-600 hover:text-gray-900' : 'text-green-600 hover:text-green-900'
                  ]"
                  :title="query.serviceStatus && query.serviceStatus.toUpperCase() === 'ENABLED' ? '禁用' : '启用'"
                >
                  <i :class="[
                    query.serviceStatus && query.serviceStatus.toUpperCase() === 'ENABLED' ? 'fas fa-pause-circle' : 'fas fa-play-circle'
                  ]"></i>
                </button>
                <button
                  @click.stop="confirmDelete(query)"
                  class="text-red-600 hover:text-red-900 mx-1"
                  title="删除"
                >
                  <i class="fas fa-trash-alt"></i>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页区域 -->
      <div v-if="queries.length > 0" class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
          <div>
            <p class="text-sm text-gray-700">
              显示
              <span class="font-medium">{{ (currentPage - 1) * pageSize + 1 }}</span>
              至
              <span class="font-medium">{{ Math.min(currentPage * pageSize, queryStore.pagination.total || queries.length) }}</span>
              条，共
              <span class="font-medium">{{ queryStore.pagination.total || queries.length }}</span>
              条记录
            </p>
          </div>
          <div>
            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
              <!-- 上一页 -->
              <button
                @click="prevPage"
                :disabled="currentPage === 1"
                :class="[
                  'relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium',
                  currentPage === 1 ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-50'
                ]"
              >
                <i class="fas fa-chevron-left"></i>
              </button>

              <!-- 页码 -->
              <template v-for="(page, index) in pageNumbers" :key="index">
                <span
                  v-if="page === '...'"
                  class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700"
                >
                  ...
                </span>
                <button
                  v-else
                  @click="goToPage(page)"
                  :class="[
                    'relative inline-flex items-center px-4 py-2 border text-sm font-medium',
                    page === currentPage
                      ? 'z-10 bg-indigo-50 border-indigo-500 text-indigo-600'
                      : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                  ]"
                >
                  {{ page }}
                </button>
              </template>

              <!-- 下一页 -->
              <button
                @click="nextPage"
                :disabled="currentPage >= totalPages"
                :class="[
                  'relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium',
                  currentPage >= totalPages ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-50'
                ]"
              >
                <i class="fas fa-chevron-right"></i>
              </button>
            </nav>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 删除确认对话框 -->
  <div v-if="showDeleteConfirm" class="fixed inset-0 z-10 overflow-y-auto">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <div class="fixed inset-0 transition-opacity" aria-hidden="true">
        <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
      </div>

      <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="sm:flex sm:items-start">
            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
              <i class="fas fa-exclamation-triangle text-red-600"></i>
            </div>
            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
              <h3 class="text-lg leading-6 font-medium text-gray-900">
                删除查询
              </h3>
              <div class="mt-2">
                <p class="text-sm text-gray-500">
                  确定要删除"{{ queryToDelete?.name || '未命名查询' }}"吗？此操作无法撤销。
                </p>
              </div>
            </div>
          </div>
        </div>
        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button
            @click="deleteQueryConfirmed"
            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
          >
            删除
          </button>
          <button
            @click="cancelDelete"
            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
          >
            取消
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 状态变更确认对话框 -->
  <div v-if="showStatusConfirm" class="fixed inset-0 z-10 overflow-y-auto">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <div class="fixed inset-0 transition-opacity" aria-hidden="true">
        <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
      </div>

      <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="sm:flex sm:items-start">
            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
              <i class="fas fa-info-circle text-blue-600"></i>
            </div>
            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
              <h3 class="text-lg leading-6 font-medium text-gray-900">
                {{ newStatus === 'ENABLED' ? '启用' : '禁用' }}查询
              </h3>
              <div class="mt-2">
                <p class="text-sm text-gray-500">
                  确定要{{ newStatus === 'ENABLED' ? '启用' : '禁用' }}查询"{{ queryToToggle?.name || '未命名查询' }}"吗？
                </p>
              </div>
            </div>
          </div>
        </div>
        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button
            @click="confirmStatusChange"
            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
          >
            确认
          </button>
          <button
            @click="cancelStatusChange"
            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
          >
            取消
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useQueryStore } from '@/stores/query'
import { formatRelativeTime } from '@/utils/formatters'
import { useMessageService } from '@/services/message'
import type { Query, QueryStatus, QueryServiceStatus } from '@/types/query'
import { dataSourceService } from '@/services/datasource'
import type { DataSource } from '@/types/datasource'
import { getQueryApiUrl } from '@/services/apiUtils'
import { http } from '@/utils/http'
import instance from "@/utils/axios";

// 路由和Store
const router = useRouter()
const queryStore = useQueryStore()
const messageService = useMessageService()

// 当前页码
const currentPage = ref(1)

// 每页条数
const pageSize = ref(10)

// 是否加载中
const isLoading = ref(false)

// 列表自动重试相关
let retryCount = 0
const shouldAutoRetry = true

// 加载的查询列表
const queries = ref<any[]>([])

// 搜索条件
const searchTerm = ref('')
const serviceStatusFilter = ref('')
const queryTypeFilter = ref('')

// 添加状态对话框相关状态
const showStatusConfirm = ref(false)
const queryToToggle = ref<any>(null)
const newStatus = ref<QueryServiceStatus>('DISABLED')

// 删除查询对话框相关状态
const showDeleteConfirm = ref(false)
const queryToDelete = ref<any>(null)

// 数据源缓存
const dataSourceCache = ref<DataSource[]>([])

// 监听筛选条件变化，重新获取数据
watch([searchTerm, serviceStatusFilter], () => {
  // 移除自动查询，改为通过按钮点击触发
  // fetchQueries();
});

// 计算属性：总页数
const totalPages = computed(() => {
  const total = queryStore.pagination.total || 0;
  return Math.ceil(total / pageSize.value);
})

// 计算属性：页码数组
const pageNumbers = computed(() => {
  if (totalPages.value <= 7) {
    return Array.from({ length: totalPages.value }, (_, i) => i + 1)
  }

  let pages = []
  const current = currentPage.value

  pages.push(1)

  if (current > 3) {
    pages.push('...')
  }

  // 当前页附近的页码
  for (let i = Math.max(2, current - 1); i <= Math.min(totalPages.value - 1, current + 1); i++) {
    pages.push(i)
  }

  if (current < totalPages.value - 2) {
    pages.push('...')
  }

  if (totalPages.value > 1) {
    pages.push(totalPages.value)
  }

  return pages
})

// 格式化日期
const formatDate = (dateString: string | undefined | null): string => {
  if (!dateString) return '未知'

  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 初始化加载数据
onMounted(async () => {
  // 先加载数据源信息
  await loadDataSourceCache();
  // 然后加载查询列表
  await fetchQueries();
})

// 预加载数据源缓存
const loadDataSourceCache = async () => {
  try {
    if (dataSourceCache.value.length === 0) {
      const response = await dataSourceService.getDataSources({
        page: 1,
        size: 100 // 获取足够多的数据源
      });
      dataSourceCache.value = response.items;
      console.log('已缓存数据源列表:', dataSourceCache.value.length);
    }
  } catch (error) {
    console.error('预加载数据源缓存失败:', error);
  }
};

// 获取查询列表数据
const fetchQueries = async () => {
  try {
    isLoading.value = true;
    console.log('开始获取查询列表，筛选条件:', {
      searchTerm: searchTerm.value,
      queryType: 'SQL',
      serviceStatus: serviceStatusFilter.value,
      page: currentPage.value,
      size: pageSize.value
    });

    // 调用API获取数据，传入筛选条件，确保参数名与后端一致
    const result = await queryStore.fetchQueries({
      search: searchTerm.value,
      queryType: 'SQL',
      serviceStatus: serviceStatusFilter.value,
      page: currentPage.value,
      size: pageSize.value,
      sortBy: 'updatedAt',
      sortDir: 'desc',
      includeDrafts: true
    });

    // 详细调试日志
    console.log('=== 查询列表结果 ===');
    console.log('API返回查询列表类型:', Object.prototype.toString.call(result));
    console.log('查询列表数据结构:', result);

    // 检查是否有直接的数据数组返回
    if (Array.isArray(result) && result.length > 0) {
      queries.value = result;
      console.log(`成功加载查询数据数组: ${queries.value.length} 条记录`);
    }
    // 如果是从store获取的数据
    else if (queryStore.queries && queryStore.queries.length > 0) {
      queries.value = queryStore.queries;
      console.log(`从store获取查询数据: ${queries.value.length} 条记录`);
    }
    // 处理标准响应格式
    else if (result && typeof result === 'object') {
      // 使用类型断言
      const apiResult = result as any;

      // 尝试从result直接获取items
      if (apiResult.items && Array.isArray(apiResult.items)) {
        queries.value = apiResult.items;
        console.log(`从result.items获取数据: ${queries.value.length} 条记录`);
      }
      // 如果响应包含分页信息
      else {
        queries.value = [];
        console.error('未能从响应中提取查询数据');
      }

      // 如果有分页信息
      if (apiResult.pagination) {
        currentPage.value = apiResult.pagination.page || 1;
        pageSize.value = apiResult.pagination.size || 10;
      }
    }
    else {
      console.error('API返回结果格式异常或无查询数据');
      messageService.info('没有查询数据或返回格式异常');
      queries.value = [];
      return;
    }

    // 添加单条记录的完整调试信息
    if (queries.value.length > 0) {
      const sampleQuery = queries.value[0];
      console.log('查询示例详情:', JSON.stringify(sampleQuery, null, 2));
    } else {
      console.log('查询列表为空');
    }

    // 补充数据源信息
    await enrichDataSourceInfo();
  } catch (error) {
    console.error('获取查询列表失败:', error);
    messageService.error('获取查询列表失败: ' + (error instanceof Error ? error.message : String(error)));
    queries.value = [];
  } finally {
    isLoading.value = false;
    // 重置重试计数
    retryCount = 0;
  }
};

// 补充数据源信息
const enrichDataSourceInfo = async () => {
  try {
    // 使用已缓存的数据源列表
    const dataSources = dataSourceCache.value;

    // 为每个查询补充数据源名称和查询类型
    queries.value.forEach(query => {
      // 打印版本相关信息，用于调试
      console.log(`查询 ${query.id} 版本详情:`, {
        currentVersionId: query.currentVersionId,
        versionNumber: query.versionNumber,
        versionStatus: query.versionStatus
      });

      // 尝试查找对应的数据源
      const matchedDataSource = dataSources.find(ds => ds.id === query.dataSourceId);

      // 如果找到对应的数据源，补充数据源名称
      if (matchedDataSource) {
        query.dataSourceName = matchedDataSource.name;
      } else {
        query.dataSourceName = '未知数据源';
      }

      // 确保查询类型大写
      if (query.queryType) {
        query.queryType = query.queryType.toUpperCase();
      }

      // 确保serviceStatus大写
      if (query.serviceStatus) {
        query.serviceStatus = query.serviceStatus.toUpperCase();
        console.log(`已将查询 ${query.id} 的serviceStatus标准化为大写: ${query.serviceStatus}`);
      }

      // 记录版本状态，用于调试
      console.log(`查询${query.id} (${query.name}) 版本状态:`, {
        currentVersionId: query.currentVersionId || '无',
        status: query.status,
        serviceStatus: query.serviceStatus
      });
    });

    // 强制使用最新状态更新列表显示
    queries.value = [...queries.value];
  } catch (error) {
    console.error('补充数据源信息失败:', error);
  }
};

// 清除筛选条件
const clearFilters = () => {
  searchTerm.value = ''
  serviceStatusFilter.value = ''

  // 添加操作提示
  messageService.info('已重置所有筛选条件')
}

// 前往创建新查询
const navigateToEditor = () => {
  router.push('/query/editor')
}

// 编辑查询
const editQuery = (query: any) => {
  router.push({
    path: '/query/editor',
    query: { id: query.id }
  })
}

// 执行查询
const executeQuery = (query: any) => {
  router.push(`/query/detail/${query.id}`)
}

// 查看版本历史
const viewVersions = (query: any) => {
  router.push(`/query/version/management/${query.id}`)
}

// 查看执行历史
const viewExecutionHistory = (query: any) => {
  router.push(`/query/history/${query.id}`)
}

// 收藏/取消收藏
const toggleFavorite = async (query: any) => {
  try {
    if (query.isFavorite) {
      await queryStore.unfavoriteQuery(query.id)
      messageService.info(`已取消收藏"${query.name || '未命名查询'}"`)
    } else {
      await queryStore.favoriteQuery(query.id)
      messageService.success(`已收藏"${query.name || '未命名查询'}"`)
    }
    query.isFavorite = !query.isFavorite
  } catch (error) {
    console.error('操作收藏失败:', error)
    messageService.error('更新收藏状态失败')
  }
}

// 修改切换查询状态处理函数
const toggleQueryStatus = async (query: any) => {
  try {
    // 设置要更新的查询
    queryToToggle.value = query;

    // 直接基于当前服务状态切换服务状态
    newStatus.value = query.serviceStatus === 'ENABLED' ? 'DISABLED' : 'ENABLED';

    // 显示确认对话框
    showStatusConfirm.value = true;
  } catch (error) {
    console.error('准备更新查询状态失败:', error);
    messageService.error('更新查询状态失败');
  }
}

// 确认状态变更
const confirmStatusChange = async () => {
  if (!queryToToggle.value) return;

  try {
    console.log('确认更改查询状态:', {
      id: queryToToggle.value.id,
      from: queryToToggle.value.serviceStatus,
      to: newStatus.value
    });

    // 显示加载指示器
    isLoading.value = true;

    // 直接传递serviceStatus参数给updateQueryStatus方法
    const result = await queryStore.updateQueryStatus(queryToToggle.value.id, newStatus.value);

    console.log('查询状态更新结果:', result);

    // 检查更新是否成功 - 逻辑更加灵活，支持多种成功响应格式
    const isSuccess = result && (
      // 标准状态对象
      result.status === 'ENABLED' ||
      result.status === 'DISABLED' ||
      // 匹配请求的状态
      result.status === newStatus.value ||
      // 支持简单的成功响应结构
      (typeof result === 'object' && result.success === true) ||
      // 支持布尔值响应
      result === true
    );

    if (isSuccess) {
      console.log('查询状态更新完成');

      // 不需要重新获取整个列表，只需在本地更新当前项的状态
      // 查找对应查询并确保状态一致
      const index = queries.value.findIndex(q => q.id === queryToToggle.value!.id);
      if (index >= 0) {
        queries.value[index].serviceStatus = newStatus.value;
        // 强制刷新 UI
        queries.value = [...queries.value];
      }

      // 保留这行代码，因为queryStore.updateQueryStatus中的消息已被移除
      messageService.success(`查询状态已${newStatus.value === 'ENABLED' ? '启用' : '禁用'}`, undefined, false);
    } else if (result && typeof result === 'object' && 'status' in result && result.status === 'ERROR') {
      // 识别明确的错误状态对象
      const errorResult = result as { status: string; error?: string };
      throw new Error(errorResult.error || '状态更新失败');
    } else {
      // 无法识别的响应格式 - 尝试获取可能的错误消息
      console.error('无法识别的状态更新响应:', result);

      let errorMsg = '状态更新失败';

      // 尝试从不同的错误格式中提取错误信息
      if (result && typeof result === 'object') {
        if ('message' in result && result.message) {
          errorMsg = result.message;
        } else if ('error' in result && result.error) {
          errorMsg = typeof result.error === 'string' ? result.error : JSON.stringify(result.error);
        } else if ('code' in result && result.code !== 200) {
          errorMsg = `状态更新失败 (错误码: ${result.code})`;
        }
      }

      throw new Error(errorMsg);
    }

    // 关闭确认对话框
    showStatusConfirm.value = false;
    queryToToggle.value = null;
  } catch (error) {
    console.error('更新查询状态失败:', error);
    messageService.error('更新查询状态失败: ' + (error instanceof Error ? error.message : String(error)));
  } finally {
    isLoading.value = false;
  }
};

// 取消状态变更
const cancelStatusChange = () => {
  showStatusConfirm.value = false
  queryToToggle.value = null
}

// 删除确认
const confirmDelete = (query: any) => {
  queryToDelete.value = query
  showDeleteConfirm.value = true
}

// 取消删除
const cancelDelete = () => {
  showDeleteConfirm.value = false
  queryToDelete.value = null
}

// 确认删除
const deleteQueryConfirmed = async () => {
  if (!queryToDelete.value) return;

  try {
    // 调用store中的deleteQuery方法
    await queryStore.deleteQuery(queryToDelete.value.id);

    // 从列表中移除该项
    queries.value = queries.value.filter(q => q.id !== queryToDelete.value!.id);

    // 关闭确认对话框
    showDeleteConfirm.value = false;
    queryToDelete.value = null;

    // 不需要显示消息，因为store的deleteQuery方法已经通过全局消息服务显示了提示
  } catch (error) {
    console.error('删除查询失败:', error);
    // 不需要显示消息，因为store的deleteQuery方法已经通过全局消息服务显示了提示
  }
}

// 前一页
const prevPage = () => {
  console.log('[QueryListView] 点击上一页，当前页:', currentPage.value);
  if (currentPage.value > 1) {
    currentPage.value--;
    fetchQueries();
  }
}

// 下一页
const nextPage = () => {
  console.log('[QueryListView] 点击下一页，当前页:', currentPage.value);
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
    fetchQueries();
  }
}

// 跳转到指定页
const goToPage = (page: number | string) => {
  console.log('[QueryListView] 跳转到指定页:', page);
  if (typeof page === 'string') return;
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page;
    fetchQueries();
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'DRAFT':
      return '草稿'
    case 'PUBLISHED':
      return '已发布'
    case 'DEPRECATED':
      return '已废弃'
    default:
      return status || '未知'
  }
}

// 查看查询详情
const viewQueryDetail = (query: any) => {
  // 先将查询ID保存以便处理
  const queryId = query.id;

  // 预加载查询详情数据，但避免加载其他不必要的数据
  queryStore.getQuery(queryId)
    .then(() => {
      router.push(`/query/detail/${queryId}`);
    })
    .catch(error => {
      console.error('加载查询详情失败:', error);
      messageService.error('加载查询详情失败');
      // 如果加载失败也继续导航，让详情页面处理错误情况
      router.push(`/query/detail/${queryId}`);
    });
}

// 添加格式化相对日期和数字的方法
// 格式化相对日期时间（如"2天前"）
const formatRelativeDate = (dateString: string) => {
  if (!dateString) return '';

  try {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSec = Math.round(diffMs / 1000);
    const diffMin = Math.round(diffSec / 60);
    const diffHour = Math.round(diffMin / 60);
    const diffDay = Math.round(diffHour / 24);

    if (diffSec < 60) {
      return '刚刚';
    }
    if (diffMin < 60) {
      return `${diffMin}分钟前`;
    }
    if (diffHour < 24) {
      return `${diffHour}小时前`;
    }
    if (diffDay < 30) {
      return `${diffDay}天前`;
    }

    // 超过30天显示完整日期
    return formatDate(dateString);
  } catch (e) {
    return dateString;
  }
};

// 格式化数字（添加千位分隔符）
const formatNumber = (num: number) => {
  if (num === undefined || num === null) return '';
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
};

// 前往收藏列表
const navigateToFavorites = () => {
  router.push('/query/favorites')
}

// 格式化版本ID，现在支持直接使用versionNumber字段
const formatVersionId = (query: any) => {
  // 如果有明确的版本号，直接使用
  if (query.versionNumber) {
    return query.versionNumber;
  }

  // 否则尝试从版本ID中提取
  if (query.currentVersionId) {
    const parts = query.currentVersionId.split('-');
    const versionNumber = parts[parts.length - 1];
    return isNaN(Number(versionNumber)) ? '1' : versionNumber;
  }

  return '-';
}

// 直接调用API检查特定查询的状态
const checkQueryStatus = async (queryId: string) => {
  try {
    console.log(`正在检查查询状态: ${queryId}`);
    const url = getQueryApiUrl('detail', { id: queryId }) + `?_t=${Date.now()}`;
    const response = await instance.get(url);

    if (!response || response.status !== 200) {
      throw new Error(`API响应错误: ${response?.status || '未知错误'}`);
    }

    console.log(`API返回查询状态:`, response.data);

    if (response.data.success && response.data.data) {
      // 找到对应查询并更新状态
      const index = queries.value.findIndex(q => q.id === queryId);
      if (index >= 0) {
        const query = queries.value[index];
        // 更新本地状态
        query.serviceStatus = response.data.data.serviceStatus;
        query.status = response.data.data.status;

        console.log(`已更新查询状态:`, {
          id: queryId,
          serviceStatus: query.serviceStatus,
          status: query.status,
          isEnabled: query.serviceStatus === 'ENABLED'
        });

        // 强制刷新UI
        queries.value = [...queries.value];
      }
    }
  } catch (error) {
    console.error(`检查查询状态出错:`, error);
  }
}

// 获取版本状态文本
const getVersionStatusText = (status: string) => {
  switch (status) {
    case 'PUBLISHED':
      return '已发布';
    case 'DRAFT':
      return '草稿';
    case 'DEPRECATED':
      return '已废弃';
    default:
      return status || '未知';
  }
}
</script>
