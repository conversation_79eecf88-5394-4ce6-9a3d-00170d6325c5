<template>
  <div class="query-editor-header">
    <div class="flex justify-between items-center mb-4">
      <div class="flex items-center">
        <a-button
          @click="router.push('/query')"
          class="mr-2"
          :disabled="loading"
        >
          <template #icon><arrow-left-outlined /></template>
          返回列表
        </a-button>
        <h1 class="text-xl font-bold mb-0 ml-2">
          {{ isEdit ? '编辑查询' : '新建查询' }}
        </h1>
      </div>
      <div class="flex items-center">
        <a-button
          type="primary"
          :loading="saving"
          :disabled="loading || executing"
          @click="handleSave"
          class="mr-2"
        >
          保存
        </a-button>
        <a-button
          type="primary"
          :loading="publishing"
          :disabled="loading || executing || !isEdit"
          @click="handlePublish"
        >
          发布新版本
        </a-button>
      </div>
    </div>

    <div v-if="errorMessage" class="bg-red-50 border border-red-200 text-red-800 px-4 py-3 mb-4 rounded">
      {{ errorMessage }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useRouter } from 'vue-router';
import { ArrowLeftOutlined } from '@ant-design/icons-vue';
import type { QueryEditorHeaderProps } from '@/views/query/types/queryEditor';

const props = defineProps<QueryEditorHeaderProps>();
const emit = defineEmits(['save', 'publish']);
const router = useRouter();

// 计算是否为编辑模式
const isEdit = computed(() => !!props.queryId);

// 处理保存按钮点击
const handleSave = () => {
  emit('save');
};

// 处理发布按钮点击
const handlePublish = () => {
  emit('publish');
};
</script>

<style scoped>
.query-editor-header {
  margin-bottom: 16px;
}
</style>
