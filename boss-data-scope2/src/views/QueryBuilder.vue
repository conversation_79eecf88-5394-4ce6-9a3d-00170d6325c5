<script setup lang="ts">
import { ref, watch } from 'vue'
import { NCard, NSpace, NDivider, NButton, NAlert } from 'naive-ui'
import DataSourceSelector from '@/components/query/DataSourceSelector.vue'
import SchemaSelector from '@/components/query/SchemaSelector.vue'
import type { DataSourceMetadata } from '@/types/metadata'

// 状态管理
const dataSourceId = ref('')
const schemaId = ref('')
const isLoading = ref(false)
const error = ref<{ type: string; message: string } | null>(null)
const dataSources = ref<DataSourceMetadata[]>([])

// 处理数据源加载完成
const handleDataSourcesLoaded = (loadedDataSources: DataSourceMetadata[]) => {
  dataSources.value = loadedDataSources
  console.log(`QueryBuilder - 已加载 ${loadedDataSources.length} 个数据源`)
}

// 处理数据源变更
const handleDataSourceChange = (newDataSourceId: string) => {
  dataSourceId.value = newDataSourceId
  // 当数据源变更时，重置 schema
  schemaId.value = ''
}

// 处理 schema 变更
const handleSchemaChange = (newSchemaId: string) => {
  schemaId.value = newSchemaId
  console.log(`QueryBuilder - 已选择 Schema: ${newSchemaId}`)
}

// 处理错误
const handleError = (err: { type: string; message: string }) => {
  error.value = err
}

// 清除错误
const clearError = () => {
  error.value = null
}

// 重置表单
const resetForm = () => {
  dataSourceId.value = ''
  schemaId.value = ''
  clearError()
}

// 运行查询（示例占位）
const runQuery = () => {
  console.log('运行查询:', {
    dataSourceId: dataSourceId.value,
    schemaId: schemaId.value
  })
  // 这里添加实际运行查询的逻辑
}

// 监听数据源变化，清除错误
watch(() => dataSourceId.value, () => {
  clearError()
})
</script>

<template>
  <div class="query-builder">
    <NCard title="查询构建器" class="w-full">
      <NAlert v-if="error" :type="error.type" closable @close="clearError">
        {{ error.message }}
      </NAlert>

      <NSpace vertical size="large">
        <!-- 数据源选择器 -->
        <DataSourceSelector
          :current-data-source-id="dataSourceId"
          @update:data-source="handleDataSourceChange"
          @dataSources-loaded="handleDataSourcesLoaded"
          @error="handleError"
          :disabled="isLoading"
        />

        <!-- Schema 选择器 -->
        <SchemaSelector
          :data-source-id="dataSourceId"
          :current-schema-id="schemaId"
          @update:schema="handleSchemaChange"
          @error="handleError"
          :disabled="!dataSourceId || isLoading"
        />

        <NDivider />

        <!-- 操作按钮 -->
        <div class="flex justify-end space-x-4">
          <NButton @click="resetForm" :disabled="isLoading">重置</NButton>
          <NButton
            type="primary"
            @click="runQuery"
            :disabled="!dataSourceId || !schemaId || isLoading"
          >
            运行查询
          </NButton>
        </div>
      </NSpace>
    </NCard>
  </div>
</template>

<style scoped>
.query-builder {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}
</style>