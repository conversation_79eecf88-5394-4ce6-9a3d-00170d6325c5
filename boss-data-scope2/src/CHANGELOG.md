# 变更日志

## 2023-11-29

### 接口优化

- 修复查询版本管理API调用路径
  - 将 `/queries/versions/management/{queryId}` 替换为标准路径 `/queries/{queryId}/versions`
  - 修复查询版本详情页使用错误API路径的问题
  - 添加版本发布和废弃功能的标准API支持
  - 统一版本API调用方式，提高代码可维护性

## 2023-11-20

### 系统重构

- 完成子系统功能迁移到主系统
  - 将 `page-service-system/src/pages/editor/PageEditor.vue` 迁移到主系统 `src/views/pages/PageEdit.vue`
  - 优化页面编辑组件的UI布局，将操作按钮移至顶部，与系统集成页面布局保持一致
  - 修复页面编辑组件中vue reactive导入问题，确保组件正常运行
  - 简化页面编辑流程，提高用户体验

## 2023-11-01

### 新增

- 优化系统页面列表操作功能
  - 新增嵌入功能，支持页面外部嵌入
  - 新增嵌入代码复制功能
  - 新增操作下拉菜单，优化界面布局
  - 添加更多交互反馈

### 修复

- 修复系统页面列表操作按钮不完整问题
- 添加更好的视觉反馈和悬停效果
- 统一了操作按钮风格

## 2023-10-23

### 新增

- 新增 `PageStore` 模块，提供页面数据状态管理功能
  - 实现页面列表获取、分页、筛选功能
  - 实现单个页面详情获取功能
  - 实现页面配置信息获取功能
  - 实现页面创建、更新、删除功能
  - 实现页面标签获取功能
  - 增加开发环境下的模拟数据支持

### 变更

- 简化页面编辑组件
  - 移除复杂的过滤器、表格、图表配置界面
  - 优化为基于系统集成的简单引用方式
  - 添加了简化的显示配置选项
  - 使用 Tailwind CSS 统一界面风格
  - 修复预览功能，正确指向查看器路径

### 修复

- 修复页面预览功能跳转路径问题
- 修复页面接口模拟数据返回
- 统一了页面列表与系统集成的界面风格

## [Unreleased]

### 修复

- 修复了保存查询对话框中的数据源下拉框默认值与编辑页面当前选择的数据源不一致的问题