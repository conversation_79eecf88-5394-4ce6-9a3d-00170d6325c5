import { test, expect } from '@playwright/test';
import { 
  waitForLoadingToDisappear, 
  expectPageTitle, 
  expectElementVisible,
  waitForNotification,
  randomWait
} from '../utils/test-helpers';

/**
 * 数据源列表页面测试套件
 */
test.describe('数据源列表页面', () => {
  test.beforeEach(async ({ page }) => {
    // 在每个测试前访问数据源列表页面
    await page.goto('/datasource');
    // 等待页面加载完成
    await waitForLoadingToDisappear(page);
    await randomWait(500, 1000);
  });

  /**
   * 基本功能测试 - 页面加载
   */
  test('应该成功加载数据源列表页面', async ({ page }) => {
    // 验证页面标题
    await expectPageTitle(page, '数据源管理');
    
    // 验证列表表格存在
    await expectElementVisible(page, 'table');
    
    // 验证添加数据源按钮存在
    const addButton = page.locator('button:has-text("添加数据源")');
    await expect(addButton).toBeVisible();
    
    // 截图
    await page.screenshot({ path: 'tests/e2e/screenshots/datasource-list.png' });
  });
  
  /**
   * 基本功能测试 - 添加数据源按钮
   */
  test('点击添加数据源按钮应导航到添加数据源表单', async ({ page }) => {
    // 点击添加数据源按钮
    await page.click('button:has-text("添加数据源")');
    await waitForLoadingToDisappear(page);
    
    // 验证页面切换到表单视图
    await expectPageTitle(page, '添加数据源');
    
    // 验证表单元素存在
    // await expectElementVisible(page, 'form');
    
    // 回到列表页
    await page.click('button:has-text("返回列表")');
    await waitForLoadingToDisappear(page);
    
    // 验证回到列表页
    await expectPageTitle(page, '数据源管理');
  });
  
  /**
   * 基本功能测试 - 查看数据源详情
   */
  test('应该能查看数据源详情', async ({ page }) => {
    // 等待数据加载完成
    await waitForLoadingToDisappear(page);
    
    // 检查是否有数据源项
    const hasDataSources = await page.locator('table tbody tr').count() > 0;
    
    if (hasDataSources) {
      // 点击第一行查看详情
      await page.click('table tbody tr >> nth=0');
      await waitForLoadingToDisappear(page);
      
      // 验证进入详情页
      await expect(page.locator('h2.text-2xl.font-bold')).toContainText('数据源管理');
      
      // 验证详情页元素存在
      // await expectElementVisible(page, '.bg-white.shadow.rounded-lg');
      
      // 回到列表页
      await page.click('button:has-text("返回列表")');
      await waitForLoadingToDisappear(page);
      
      // 验证回到列表页
      await expectPageTitle(page, '数据源管理');
    } else {
      console.log('跳过详情测试 - 没有可用的数据源');
      test.skip();
    }
  });
  
  /**
   * 基本功能测试 - 表格排序
   */
  test('表格应支持排序', async ({ page }) => {
    // 等待数据加载完成
    await waitForLoadingToDisappear(page);
    
    // 检查是否有数据源项
    const rowCount = await page.locator('table tbody tr').count();
    
    if (rowCount > 1) {
      // 获取排序前的第一个数据源名称
      const firstRowTextBefore = await page.locator('table tbody tr >> nth=0').textContent();
      
      // 点击名称列头进行排序
      await page.click('table th:has-text("名称")');
      await waitForLoadingToDisappear(page);
      await randomWait(500, 1000);
      
      // 获取排序后的第一个数据源名称
      const firstRowTextAfter = await page.locator('table tbody tr >> nth=0').textContent();
      
      // 验证排序是否生效（名称可能会变化）
      console.log(`排序前第一行: ${firstRowTextBefore}`);
      console.log(`排序后第一行: ${firstRowTextAfter}`);
      
      // 再次点击名称列头，反向排序
      await page.click('table th:has-text("名称")');
      await waitForLoadingToDisappear(page);
      await randomWait(500, 1000);
      
      // 获取反向排序后的第一个数据源名称
      const firstRowTextReverse = await page.locator('table tbody tr >> nth=0').textContent();
      console.log(`反向排序后第一行: ${firstRowTextReverse}`);
    } else {
      console.log('跳过排序测试 - 数据源不足');
      test.skip();
    }
  });
  
  /**
   * 用户交互测试 - 搜索功能
   */
  test('应支持搜索数据源', async ({ page }) => {
    // 等待数据加载完成
    await waitForLoadingToDisappear(page);
    
    // 检查是否有搜索框
    const searchInput = page.locator('input[type="search"], input[placeholder*="搜索"]');
    const hasSearchBox = await searchInput.count() > 0;
    
    if (hasSearchBox) {
      // 获取原始行数
      const originalRowCount = await page.locator('table tbody tr').count();
      console.log(`原始行数: ${originalRowCount}`);
      
      if (originalRowCount > 0) {
        // 获取第一行的部分文本作为搜索词
        const firstRowText = await page.locator('table tbody tr >> nth=0').textContent() || '';
        const searchTerm = firstRowText.substring(0, Math.min(4, firstRowText.length)); // 取前几个字符作为搜索词
        
        // 输入搜索词
        await searchInput.fill(searchTerm);
        await page.keyboard.press('Enter');
        await waitForLoadingToDisappear(page);
        await randomWait(500, 1000);
        
        // 获取搜索后的行数
        const filteredRowCount = await page.locator('table tbody tr').count();
        console.log(`搜索"${searchTerm}"后的行数: ${filteredRowCount}`);
        
        // 清除搜索
        await searchInput.fill('');
        await page.keyboard.press('Enter');
        await waitForLoadingToDisappear(page);
        
        // 验证恢复到初始状态
        const resetRowCount = await page.locator('table tbody tr').count();
        console.log(`清除搜索后的行数: ${resetRowCount}`);
        expect(resetRowCount).toBeGreaterThanOrEqual(filteredRowCount);
      } else {
        console.log('跳过搜索测试 - 没有数据源');
        test.skip();
      }
    } else {
      console.log('跳过搜索测试 - 搜索框不存在');
      test.skip();
    }
  });
  
  /**
   * 用户交互测试 - 分页功能
   */
  test('表格应支持分页', async ({ page }) => {
    // 等待数据加载完成
    await waitForLoadingToDisappear(page);
    
    // 检查是否有分页器
    const hasPagination = await page.locator('.pagination, [aria-label="pagination"]').count() > 0;
    
    if (hasPagination) {
      // 获取当前页码
      const currentPage = await page.locator('.pagination .active, [aria-current="page"]').textContent();
      console.log(`当前页码: ${currentPage}`);
      
      // 检查是否有下一页按钮
      const nextPageButton = page.locator('button[aria-label="next page"], .pagination-next');
      const hasNextPage = await nextPageButton.count() > 0 && await nextPageButton.isEnabled();
      
      if (hasNextPage) {
        // 记录第一页第一条数据
        const firstRowTextPage1 = await page.locator('table tbody tr >> nth=0').textContent();
        
        // 点击下一页
        await nextPageButton.click();
        await waitForLoadingToDisappear(page);
        
        // 获取第二页第一条数据
        const firstRowTextPage2 = await page.locator('table tbody tr >> nth=0').textContent();
        
        // 验证两页数据不同
        console.log(`第一页首行: ${firstRowTextPage1}`);
        console.log(`第二页首行: ${firstRowTextPage2}`);
        expect(firstRowTextPage1).not.toEqual(firstRowTextPage2);
        
        // 点击上一页
        await page.locator('button[aria-label="previous page"], .pagination-prev').click();
        await waitForLoadingToDisappear(page);
        
        // 验证回到第一页
        const firstRowTextBackToPage1 = await page.locator('table tbody tr >> nth=0').textContent();
        expect(firstRowTextBackToPage1).toEqual(firstRowTextPage1);
      } else {
        console.log('跳过分页测试 - 无下一页');
        test.skip();
      }
    } else {
      console.log('跳过分页测试 - 分页器不存在');
      test.skip();
    }
  });
}); 