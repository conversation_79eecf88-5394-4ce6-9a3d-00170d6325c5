import { test, expect } from '@playwright/test';
import { 
  waitForLoadingToDisappear, 
  expectPageTitle, 
  waitForNotification,
  randomWait
} from '../utils/test-helpers';
import { DataSourceMockData } from '../utils/mock-data';

/**
 * 数据源错误场景测试套件
 */
test.describe('数据源错误场景', () => {
  
  /**
   * 表单验证错误 - 必填字段缺失
   */
  test('表单缺少必填字段应显示错误提示', async ({ page }) => {
    // 访问添加数据源页面
    await page.goto('/datasource/create');
    await waitForLoadingToDisappear(page);
    
    // 立即点击保存按钮，不填写任何字段
    await page.click('button[type="button"], button:has-text(" 保存修改 ")');
    await randomWait(500, 1000);
    
    // 验证错误提示出现
    const errorMessages = page.locator('.text-red-600, .ant-form-item-explain-error, .el-form-item__error');
    await expect(errorMessages).toBeVisible();
    
    // 验证错误消息数量
    const errorCount = await errorMessages.count();
    expect(errorCount).toBeGreaterThan(0);
    console.log(`发现 ${errorCount} 个表单错误提示`);
    
    // 截图
    await page.screenshot({ path: 'tests/e2e/screenshots/datasource-form-errors.png' });
  });
  
  /**
   * 表单验证错误 - 非法输入
   */
  test('输入无效的端口号应显示错误提示', async ({ page }) => {
    // 访问添加数据源页面
    await page.goto('/datasource/create');
    await waitForLoadingToDisappear(page);
    
    // 填写表单，但输入无效的端口号
    const testData = DataSourceMockData.validDataSource();
    
    // 填写名称和类型等其他字段
    await page.fill('input[name="name"]', testData.name);
    
    // 尝试选择类型
    try {
      await page.click('select[name="type"], [data-test="type-select"]');
      await page.click(`option[value="${testData.type}"], li:has-text("${testData.type.toUpperCase()}")`);
    } catch (e) {
      console.log('类型选择失败，继续测试');
    }
    
    // 填写主机
    await page.fill('input[name="host"]', testData.host);
    
    // 填写无效的端口号
    await page.fill('input[name="port"]', 'abc');
    
    // 点击提交按钮
    await page.click('button[type="submit"], button:has-text("保存")');
    await randomWait(500, 1000);
    
    // 验证端口号字段的错误消息
    const portErrorMessage = page.locator('input[name="port"] + .text-red-600, input[name="port"] ~ .ant-form-item-explain-error, input[name="port"] ~ .el-form-item__error');
    const hasPortError = await portErrorMessage.count() > 0;
    
    if (hasPortError) {
      await expect(portErrorMessage).toBeVisible();
      
      // 截图
      await page.screenshot({ path: 'tests/e2e/screenshots/datasource-invalid-port.png' });
    } else {
      // 查找可能的其他错误提示
      const otherErrorMessages = page.locator('.text-red-600, .ant-form-item-explain-error, .el-form-item__error');
      await expect(otherErrorMessages).toBeVisible();
      
      // 截图
      await page.screenshot({ path: 'tests/e2e/screenshots/datasource-invalid-port-alt.png' });
    }
  });
  
  /**
   * 连接错误 - 测试连接失败
   */
  test('测试无效的数据库连接应显示错误消息', async ({ page }) => {
    // 访问添加数据源页面
    await page.goto('/datasource/create');
    await waitForLoadingToDisappear(page);
    
    // 填写有效表单但使用不存在的主机
    const testData = DataSourceMockData.validDataSource();
    
    // 修改为不存在的主机
    testData.host = 'non-existent-host.example.com';
    
    // 填写表单
    await page.fill('input[name="name"]', testData.name);
    
    // 尝试选择类型
    try {
      await page.click('select[name="type"], [data-test="type-select"]');
      await page.click(`option[value="${testData.type}"], li:has-text("${testData.type.toUpperCase()}")`);
    } catch (e) {
      console.log('类型选择失败，继续测试');
    }
    
    // 填写主机信息
    await page.fill('input[name="host"]', testData.host);
    await page.fill('input[name="port"]', testData.port.toString());
    
    // 填写数据库名称，注意可能有两种表单字段名
    try {
      await page.fill('input[name="databaseName"]', testData.databaseName);
    } catch (e) {
      try {
        await page.fill('input[name="database"]', testData.databaseName);
      } catch (e2) {
        console.log('找不到数据库名称字段，跳过填写');
      }
    }
    
    // 填写用户名密码
    await page.fill('input[name="username"]', testData.username);
    await page.fill('input[name="password"]', testData.password);
    
    // 点击测试连接按钮
    await page.click('button:has-text("测试连接")');
    
    // 等待加载状态消失
    await waitForLoadingToDisappear(page);
    
    // 等待错误消息出现 (可能是消息通知或内嵌错误)
    try {
      // 首先尝试等待消息通知
      await waitForNotification(page);
      
      // 截图
      await page.screenshot({ path: 'tests/e2e/screenshots/datasource-connection-error.png' });
    } catch (e) {
      console.log('未检测到消息通知，尝试查找其他错误提示');
      
      // 尝试查找其他形式的错误提示
      const errorElement = page.locator('.error-message, .ant-alert-error, .el-alert--error');
      const hasError = await errorElement.count() > 0;
      
      if (hasError) {
        await expect(errorElement).toBeVisible();
      } else {
        console.log('未找到预期的错误提示，可能使用了不同的错误展示方式');
      }
      
      // 截图
      await page.screenshot({ path: 'tests/e2e/screenshots/datasource-connection-error-alt.png' });
    }
  });
  
  /**
   * 超长输入边界测试
   */
  test('应正确处理超长输入值', async ({ page }) => {
    // 访问添加数据源页面
    await page.goto('/datasource/create');
    await waitForLoadingToDisappear(page);
    
    // 获取带超长名称的测试数据
    const testData = DataSourceMockData.dataSourceWithLongName();
    
    // 填写超长名称
    await page.fill('input[name="name"]', testData.name);
    
    // 填写超长描述
    try {
      await page.fill('textarea[name="description"]', testData.description);
    } catch (e) {
      console.log('找不到描述字段，跳过填写');
    }
    
    // 尝试选择类型
    try {
      await page.click('select[name="type"], [data-test="type-select"]');
      await page.click(`option[value="${testData.type}"], li:has-text("${testData.type.toUpperCase()}")`);
    } catch (e) {
      console.log('类型选择失败，继续测试');
    }
    
    // 填写主机信息
    await page.fill('input[name="host"]', testData.host);
    await page.fill('input[name="port"]', testData.port.toString());
    
    // 截图
    await page.screenshot({ path: 'tests/e2e/screenshots/datasource-long-input.png' });
    
    // 点击提交按钮
    await page.click('button[type="submit"], button:has-text("保存")');
    await waitForLoadingToDisappear(page);
    
    // 检查可能的错误消息
    const errorMessages = page.locator('.text-red-600, .ant-form-item-explain-error, .el-form-item__error');
    const hasErrors = await errorMessages.count() > 0;
    
    if (hasErrors) {
      // 可能存在长度限制，此时会显示错误
      console.log('检测到输入长度错误提示');
      await expect(errorMessages).toBeVisible();
    } else {
      // 如果没有错误，应该进行了截断处理或允许超长输入
      console.log('未检测到错误提示，表单可能接受了超长输入或进行了截断');
    }
  });
  
  /**
   * SQL注入尝试处理
   */
  test('应安全处理SQL注入尝试', async ({ page }) => {
    // 访问添加数据源页面
    await page.goto('/datasource/create');
    await waitForLoadingToDisappear(page);
    
    // 获取带SQL注入尝试的测试数据
    const testData = DataSourceMockData.dataSourceWithSqlInjection();
    
    // 填写表单
    await page.fill('input[name="name"]', testData.name);
    
    // 填写描述
    try {
      await page.fill('textarea[name="description"]', testData.description);
    } catch (e) {
      console.log('找不到描述字段，跳过填写');
    }
    
    // 尝试选择类型
    try {
      await page.click('select[name="type"], [data-test="type-select"]');
      await page.click(`option[value="${testData.type}"], li:has-text("${testData.type.toUpperCase()}")`);
    } catch (e) {
      console.log('类型选择失败，继续测试');
    }
    
    // 填写其他必要字段
    await page.fill('input[name="host"]', testData.host);
    await page.fill('input[name="port"]', testData.port.toString());
    
    // 截图
    await page.screenshot({ path: 'tests/e2e/screenshots/datasource-sql-injection.png' });
    
    // 提交表单
    await page.click('button[type="submit"], button:has-text("保存")');
    await waitForLoadingToDisappear(page);
    
    // 如果有验证错误
    const errorMessages = page.locator('.text-red-600, .ant-form-item-explain-error, .el-form-item__error');
    const hasErrors = await errorMessages.count() > 0;
    
    if (hasErrors) {
      // 可能存在特殊字符过滤
      console.log('检测到特殊字符错误提示');
      await expect(errorMessages).toBeVisible();
    } else {
      // 如果没有错误，应该进行了安全处理
      console.log('未检测到错误提示，表单可能接受了SQL注入尝试但进行了安全处理');
      
      // 检查是否成功提交（导航到列表页）
      const isOnListPage = await page.locator('h2:has-text("数据源管理")').count() > 0;
      
      if (isOnListPage) {
        // 验证是否真的创建了数据源
        const tableContent = await page.locator('table').textContent() || '';
        const nameInTable = tableContent.includes(testData.name);
        
        if (nameInTable) {
          console.log('SQL注入尝试数据源已创建，但应该经过了安全处理');
        } else {
          console.log('SQL注入尝试可能被拒绝，但未显示错误消息');
        }
      }
    }
  });
  
  /**
   * 访问不存在的数据源详情
   */
  test('访问不存在的数据源ID应显示错误', async ({ page }) => {
    // 访问一个不存在的数据源ID
    await page.goto('/datasource/non-existent-id');
    await waitForLoadingToDisappear(page);
    
    // 检查是否重定向到列表页
    const onListPage = await page.locator('h2:has-text("数据源管理")').count() > 0;
    
    if (onListPage) {
      console.log('已重定向到列表页');
      
      // 检查是否显示错误消息
      try {
        // 尝试等待错误消息通知
        await waitForNotification(page);
        
        // 截图
        await page.screenshot({ path: 'tests/e2e/screenshots/datasource-not-found.png' });
      } catch (e) {
        console.log('未检测到错误消息通知');
      }
    } else {
      // 检查是否显示自定义错误页面或组件
      const errorElement = page.locator('.error-page, .error-message, .not-found');
      const hasErrorElement = await errorElement.count() > 0;
      
      if (hasErrorElement) {
        await expect(errorElement).toBeVisible();
        console.log('显示自定义错误页面或组件');
      } else {
        console.log('未显示自定义错误页面，也未重定向到列表页');
      }
      
      // 截图
      await page.screenshot({ path: 'tests/e2e/screenshots/datasource-not-found-alt.png' });
    }
  });
}); 