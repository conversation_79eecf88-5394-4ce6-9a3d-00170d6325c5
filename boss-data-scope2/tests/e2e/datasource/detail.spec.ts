import { test, expect } from '@playwright/test';
import { 
  waitForLoadingToDisappear, 
  expectPageTitle, 
  expectElementVisible,
  waitForNotification,
  randomWait
} from '../utils/test-helpers';

/**
 * 数据源详情页面测试套件
 */
test.describe('数据源详情页面', () => {
  /**
   * 测试准备，获取一个数据源的ID
   */
  async function getFirstDataSourceId(page): Promise<string | undefined> {
    // 访问数据源列表页面
    await page.goto('/datasource');
    await waitForLoadingToDisappear(page);
    
    // 检查是否有数据源
    const hasDataSources = await page.locator('table tbody tr').count() > 0;
    
    if (hasDataSources) {
      // 点击第一行查看详情
      await page.click('table tbody tr >> nth=0');
      await waitForLoadingToDisappear(page);
      
      // 从URL获取数据源ID
      const url = page.url();
      const match = url.match(/\/datasource\/(\w+-?\w+)/);
      
      return match ? match[1] : undefined;
    }
    
    return undefined;
  }
  
  /**
   * 基本功能测试 - 加载详情页
   */
  test('应该成功加载数据源详情页面', async ({ page }) => {
    // 获取第一个数据源ID
    const dataSourceId = await getFirstDataSourceId(page);
    
    if (!dataSourceId) {
      console.log('跳过详情页测试 - 没有可用的数据源');
      test.skip();
      return;
    }
    
    // 验证页面标题包含'详情'
    await expect(page.locator('h2.text-2xl.font-bold')).toContainText('详情');
    
    // 验证基本信息区域存在
    await expectElementVisible(page, '.bg-white.shadow.rounded-lg');
    
    // 验证操作按钮存在
    await expectElementVisible(page, 'button:has-text("编辑"), [title="编辑"]');
    await expectElementVisible(page, 'button:has-text("返回列表")');
    
    // 截图
    await page.screenshot({ path: 'tests/e2e/screenshots/datasource-detail.png' });
  });
  
  /**
   * 基本功能测试 - 显示数据源基本信息
   */
  test('应显示数据源的基本信息', async ({ page }) => {
    // 获取第一个数据源ID
    const dataSourceId = await getFirstDataSourceId(page);
    
    if (!dataSourceId) {
      console.log('跳过详情页测试 - 没有可用的数据源');
      test.skip();
      return;
    }
    
    // 验证详情区域包含关键信息
    const detailContent = await page.locator('.bg-white.shadow.rounded-lg').textContent() || '';
    
    // 检查详情内容包含以下关键字段
    expect(detailContent).toContain('主机');
    expect(detailContent).toContain('端口');
    expect(detailContent).toContain('数据库');
    expect(detailContent).toContain('用户名');
    
    // 验证数据源状态信息显示
    const hasStatusInfo = (
      detailContent.includes('状态') || 
      detailContent.includes('同步频率') || 
      detailContent.includes('最后同步')
    );
    
    expect(hasStatusInfo).toBe(true);
  });
  
  /**
   * 元数据功能测试 - 显示表和视图
   */
  test('应显示数据源的元数据信息', async ({ page }) => {
    // 获取第一个数据源ID
    const dataSourceId = await getFirstDataSourceId(page);
    
    if (!dataSourceId) {
      console.log('跳过详情页测试 - 没有可用的数据源');
      test.skip();
      return;
    }
    
    // 尝试寻找元数据相关UI元素
    // 元数据区可能有不同的展现形式，这里尝试多种可能的选择器
    const metadataSelectors = [
      'text=元数据',
      'text=表',
      'text=视图',
      'text=Metadata',
      'text=Tables',
      'text=Views',
      '[role="tab"]:has-text("表")',
      '[role="tab"]:has-text("视图")'
    ];
    
    // 查找是否存在任一元数据相关元素
    let foundMetadataElement = false;
    for (const selector of metadataSelectors) {
      const count = await page.locator(selector).count();
      if (count > 0) {
        foundMetadataElement = true;
        console.log(`找到元数据相关元素: ${selector}`);
        break;
      }
    }
    
    // 如果找到了元数据元素，继续测试
    if (foundMetadataElement) {
      // 尝试点击"表"标签 (如果存在)
      try {
        await page.click('[role="tab"]:has-text("表"), text=表');
        await randomWait(500, 1000);
        
        // 检查表格或树形结构是否显示
        const hasTableList = await page.isVisible('.tree, table, .tree-node, [role="treeitem"]');
        
        console.log(`表列表显示: ${hasTableList}`);
        
        // 截图
        await page.screenshot({ path: 'tests/e2e/screenshots/datasource-metadata-tables.png' });
        
        // 尝试点击"视图"标签 (如果存在)
        try {
          await page.click('[role="tab"]:has-text("视图"), text=视图');
          await randomWait(500, 1000);
          
          // 截图
          await page.screenshot({ path: 'tests/e2e/screenshots/datasource-metadata-views.png' });
        } catch (e) {
          console.log('视图标签不可用或无法点击');
        }
      } catch (e) {
        console.log('表标签不可用或无法点击');
      }
    } else {
      console.log('未找到元数据相关元素');
    }
  });
  
  /**
   * 功能测试 - 同步元数据操作
   */
  test('应支持同步元数据操作', async ({ page }) => {
    // 获取第一个数据源ID
    const dataSourceId = await getFirstDataSourceId(page);
    
    if (!dataSourceId) {
      console.log('跳过同步测试 - 没有可用的数据源');
      test.skip();
      return;
    }
    
    // 查找同步按钮
    const syncButton = page.locator('button:has-text("同步元数据"), button:has-text("同步"), [title="同步元数据"], [title="同步"]');
    const hasSyncButton = await syncButton.count() > 0;
    
    if (hasSyncButton) {
      // 点击同步按钮
      await syncButton.click();
      
      // 等待加载完成
      await waitForLoadingToDisappear(page);
      
      // 等待同步完成通知
      await waitForNotification(page);
      
      // 截图
      await page.screenshot({ path: 'tests/e2e/screenshots/datasource-sync-metadata.png' });
    } else {
      console.log('同步元数据按钮不可用');
      test.skip();
    }
  });
  
  /**
   * 功能测试 - 测试连接操作
   */
  test('应支持测试连接操作', async ({ page }) => {
    // 获取第一个数据源ID
    const dataSourceId = await getFirstDataSourceId(page);
    
    if (!dataSourceId) {
      console.log('跳过测试连接测试 - 没有可用的数据源');
      test.skip();
      return;
    }
    
    // 查找测试连接按钮
    const testConnButton = page.locator('button:has-text("测试连接"), [title="测试连接"]');
    const hasTestConnButton = await testConnButton.count() > 0;
    
    if (hasTestConnButton) {
      // 点击测试连接按钮
      await testConnButton.click();
      
      // 等待加载完成
      await waitForLoadingToDisappear(page);
      
      // 等待测试连接结果通知
      await waitForNotification(page);
      
      // 截图
      await page.screenshot({ path: 'tests/e2e/screenshots/datasource-detail-test-connection.png' });
    } else {
      console.log('测试连接按钮不可用');
      test.skip();
    }
  });
  
  /**
   * 导航测试 - 编辑按钮
   */
  test('应支持从详情页跳转到编辑页', async ({ page }) => {
    // 获取第一个数据源ID
    const dataSourceId = await getFirstDataSourceId(page);
    
    if (!dataSourceId) {
      console.log('跳过编辑测试 - 没有可用的数据源');
      test.skip();
      return;
    }
    
    // 查找编辑按钮
    const editButton = page.locator('button:has-text("编辑"), [title="编辑"]');
    
    // 点击编辑按钮
    await editButton.click();
    await waitForLoadingToDisappear(page);
    
    // 验证页面切换到编辑表单
    await expectPageTitle(page, '编辑数据源');
    
    // 验证表单元素存在
    await expectElementVisible(page, 'form');
    
    // 验证返回按钮
    await expectElementVisible(page, 'button:has-text("返回列表")');
    
    // 返回列表页
    await page.click('button:has-text("返回列表")');
    await waitForLoadingToDisappear(page);
    
    // 验证回到列表页
    await expectPageTitle(page, '数据源管理');
  });
  
  /**
   * 功能测试 - 删除数据源操作
   * 注意：此测试会实际删除数据，可能需要在测试环境中执行
   */
  test.skip('应支持删除数据源操作', async ({ page }) => {
    // 获取第一个数据源ID
    const dataSourceId = await getFirstDataSourceId(page);
    
    if (!dataSourceId) {
      console.log('跳过删除测试 - 没有可用的数据源');
      test.skip();
      return;
    }
    
    // 获取数据源名称
    const dataSourceName = await page.locator('h2.text-2xl.font-bold').textContent();
    console.log(`准备删除数据源: ${dataSourceName}`);
    
    // 查找删除按钮
    const deleteButton = page.locator('button:has-text("删除"), [title="删除"]');
    const hasDeleteButton = await deleteButton.count() > 0;
    
    if (hasDeleteButton) {
      // 点击删除按钮
      await deleteButton.click();
      await randomWait(500, 1000);
      
      // 确认删除对话框应出现
      await expectElementVisible(page, '.modal, .dialog, .ant-modal, .el-dialog');
      
      // 查找确认按钮
      const confirmButton = page.locator('.modal button:has-text("确认"), .dialog button:has-text("确认"), .ant-modal button:has-text("确认"), .el-dialog button:has-text("确定")');
      
      // 截图
      await page.screenshot({ path: 'tests/e2e/screenshots/datasource-delete-confirm.png' });
      
      // 如果这是实际测试，这里会执行确认操作：
      /*
      // 点击确认删除
      await confirmButton.click();
      await waitForLoadingToDisappear(page);
      
      // 验证是否返回到列表页
      await expectPageTitle(page, '数据源管理');
      
      // 验证列表中不包含已删除的数据源名称
      const tableContent = await page.locator('table').textContent();
      expect(tableContent).not.toContain(dataSourceName);
      */
      
      // 点击取消，不实际执行删除
      const cancelButton = page.locator('.modal button:has-text("取消"), .dialog button:has-text("取消"), .ant-modal button:has-text("取消"), .el-dialog button:has-text("取消")');
      await cancelButton.click();
      await randomWait(500, 1000);
    } else {
      console.log('删除按钮不可用');
      test.skip();
    }
  });
}); 