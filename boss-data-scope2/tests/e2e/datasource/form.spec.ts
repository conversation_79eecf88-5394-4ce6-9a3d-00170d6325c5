import { test, expect } from '@playwright/test';
import { 
  waitForLoadingToDisappear, 
  expectPageTitle, 
  expectElementVisible,
  waitForNotification,
  randomWait,
  fillFormField,
  selectDropdownOption
} from '../utils/test-helpers';
import { DataSourceMockData } from '../utils/mock-data';

/**
 * 数据源表单页面测试套件
 */
test.describe('数据源表单页面', () => {
  
  /**
   * 访问添加数据源表单页面测试
   */
  test('应该成功加载添加数据源表单页面', async ({ page }) => {
    // 访问添加数据源页面
    await page.goto('/datasource/create');
    // 等待页面加载完成
    await waitForLoadingToDisappear(page);
    
    // 验证页面标题
    await expectPageTitle(page, '添加数据源');
    
    // 验证表单元素存在
    // await expectElementVisible(page, 'form');
    
    // 验证必填字段存在
    await expectElementVisible(page, 'input[name="name"]');
    await expectElementVisible(page, 'select[name="type"], [data-test="type-select"]');
    await expectElementVisible(page, 'input[name="host"]');
    await expectElementVisible(page, 'input[name="port"]');
    await expectElementVisible(page, 'input[name="username"]');
    
    // 验证保存按钮存在
    await expectElementVisible(page, 'button[type="submit"], button:has-text("保存")');
    
    // 验证测试连接按钮存在
    await expectElementVisible(page, 'button:has-text("测试连接")');
    
    // 验证取消按钮存在
    await expectElementVisible(page, 'button:has-text("取消")');
    
    // 截图
    await page.screenshot({ path: 'tests/e2e/screenshots/datasource-form-add.png' });
  });

  /**
   * 表单验证测试 - 必填字段
   */
  test('表单应验证必填字段', async ({ page }) => {
    // 访问添加数据源页面
    await page.goto('/datasource/create');
    await waitForLoadingToDisappear(page);
    
    // 直接点击保存按钮，不填写任何字段
    await page.click('button[type="submit"], button:has-text("保存修改")');
    
    // 等待验证消息出现
    await randomWait(500, 1000);
    
    // 验证错误提示出现
    // 根据不同UI库，错误消息可能有不同的展现方式
    const errorMessages = page.locator('.text-red-600, .ant-form-item-explain-error, .el-form-item__error');
    await expect(errorMessages).toBeVisible();
    
    // 截图
    await page.screenshot({ path: 'tests/e2e/screenshots/datasource-form-validation.png' });
  });

  /**
   * 添加有效数据源测试 - 完整表单提交
   */
  test('应成功添加一个有效的数据源', async ({ page }) => {
    // 访问添加数据源页面
    await page.goto('/datasource/create');
    await waitForLoadingToDisappear(page);
    
    // 生成一个有效的数据源数据
    const testData = DataSourceMockData.validDataSource('mysql');
    
    // 填写表单
    await page.fill('input[name="name"]', testData.name);
    
    // 选择类型
    await page.click('select[name="type"], [data-test="type-select"]');
    await page.click(`option[value="${testData.type}"], li:has-text("${testData.type.toUpperCase()}")`);
    
    // 填写主机信息
    await page.fill('input[name="host"]', testData.host);
    await page.fill('input[name="port"]', testData.port.toString());
    
    // 填写数据库名称，注意可能有两种表单字段名
    try {
      await page.fill('input[name="databaseName"]', testData.databaseName);
    } catch (e) {
      try {
        await page.fill('input[name="database"]', testData.databaseName);
      } catch (e2) {
        console.log('找不到数据库名称字段，跳过填写');
      }
    }
    
    // 填写用户名密码
    await page.fill('input[name="username"]', testData.username);
    await page.fill('input[name="password"]', testData.password);
    
    // 选择同步频率
    try {
      await page.click('select[name="syncFrequency"]');
      await page.click(`option[value="${testData.syncFrequency}"], li:has-text("${testData.syncFrequency}")`);
    } catch (e) {
      console.log('找不到同步频率字段，跳过选择');
    }
    
    // 测试连接（可选）
    try {
      await page.click('button:has-text("测试连接")');
      await waitForLoadingToDisappear(page);
      await waitForNotification(page, '连接成功');
    } catch (e) {
      console.log('测试连接失败或不可用，继续保存');
    }
    
    // 截图表单填写状态
    await page.screenshot({ path: 'tests/e2e/screenshots/datasource-form-filled.png' });
    
    // 提交表单
    await page.click('button[type="submit"], button:has-text("保存")');
    
    // 等待保存操作完成
    await waitForLoadingToDisappear(page);
    
    // 验证是否返回到列表页
    await expectPageTitle(page, '数据源管理');
    
    // 验证列表中是否包含新增的数据源
    const tableContent = await page.locator('table').textContent();
    expect(tableContent).toContain(testData.name);
  });

  /**
   * 编辑数据源测试
   */
  test('应能够编辑现有数据源', async ({ page }) => {
    // 首先访问数据源列表页面
    await page.goto('/datasource');
    await waitForLoadingToDisappear(page);
    
    // 检查是否有数据源
    const hasDataSources = await page.locator('table tbody tr').count() > 0;
    
    if (hasDataSources) {
      // 获取第一个数据源的名称，用于后续验证
      const firstDataSourceName = await page.locator('table tbody tr >> nth=0 >> td >> nth=0').textContent() || '';
      
      // 找到并点击编辑按钮
      await page.click('table tbody tr >> nth=0 >> button:has-text("编辑"), table tbody tr >> nth=0 [title="编辑"]');
      await waitForLoadingToDisappear(page);
      
      // 验证页面已切换到编辑表单
      await expectPageTitle(page, '编辑数据源');
      
      // 验证名称字段已填充现有数据
      const nameInput = page.locator('input[name="name"]');
      await expect(nameInput).toHaveValue(firstDataSourceName.trim());
      
      // 修改数据源描述信息
      const newDescription = `更新的描述 ${new Date().toISOString()}`;
      await page.fill('textarea[name="description"]', newDescription);
      
      // 截图
      await page.screenshot({ path: 'tests/e2e/screenshots/datasource-form-edit.png' });
      
      // 提交表单
      await page.click('button[type="submit"], button:has-text("保存")');
      
      // 等待保存操作完成
      await waitForLoadingToDisappear(page);
      
      // 验证是否返回到列表页
      await expectPageTitle(page, '数据源管理');
      
      // 验证列表中包含更新后的数据源名称
      const tableContent = await page.locator('table').textContent();
      expect(tableContent).toContain(firstDataSourceName.trim());
    } else {
      console.log('跳过编辑测试 - 没有可用的数据源');
      test.skip();
    }
  });

  /**
   * 测试连接功能测试
   */
  test('测试连接功能应正常工作', async ({ page }) => {
    // 访问添加数据源页面
    await page.goto('/datasource/create');
    await waitForLoadingToDisappear(page);
    
    // 生成一个有效的数据源数据
    const testData = DataSourceMockData.validDataSource('mysql');
    
    // 只填写必要的连接信息
    await page.fill('input[name="name"]', testData.name);
    
    // 选择类型
    await page.click('select[name="type"], [data-test="type-select"]');
    await page.click(`option[value="${testData.type}"], li:has-text("${testData.type.toUpperCase()}")`);
    
    // 填写主机信息
    await page.fill('input[name="host"]', testData.host);
    await page.fill('input[name="port"]', testData.port.toString());
    
    // 填写数据库名称
    try {
      await page.fill('input[name="databaseName"]', testData.databaseName);
    } catch (e) {
      try {
        await page.fill('input[name="database"]', testData.databaseName);
      } catch (e2) {
        console.log('找不到数据库名称字段，跳过填写');
      }
    }
    
    // 填写用户名密码
    await page.fill('input[name="username"]', testData.username);
    await page.fill('input[name="password"]', testData.password);
    
    // 点击测试连接按钮
    await page.click('button:has-text("测试连接")');
    
    // 等待加载状态消失
    await waitForLoadingToDisappear(page);
    
    // 等待测试连接结果消息
    try {
      // 检查成功或失败消息
      await page.waitForSelector('.ant-message-notice, .el-message, .notification', { timeout: 5000 });
      const messageVisible = await page.isVisible('.ant-message-notice, .el-message, .notification');
      expect(messageVisible).toBe(true);
      
      // 截图
      await page.screenshot({ path: 'tests/e2e/screenshots/datasource-test-connection.png' });
    } catch (e) {
      console.log('未检测到测试连接结果消息');
      test.fail();
    }
  });

  /**
   * 取消添加数据源测试
   */
  test('取消添加数据源应返回列表页', async ({ page }) => {
    // 访问添加数据源页面
    await page.goto('/datasource/create');
    await waitForLoadingToDisappear(page);
    
    // 生成并填写部分数据
    const testData = DataSourceMockData.validDataSource();
    await page.fill('input[name="name"]', testData.name);
    
    // 点击取消按钮
    await page.click('button:has-text("取消")');
    
    // 等待导航完成
    await waitForLoadingToDisappear(page);
    
    // 验证返回到列表页
    await expectPageTitle(page, '数据源管理');
    
    // 验证表格数据中不包含取消的数据源名称
    const tableContent = await page.locator('table').textContent();
    expect(tableContent || '').not.toContain(testData.name);
  });

  /**
   * 边界情况测试 - 特殊字符
   */
  test('应正确处理特殊字符输入', async ({ page }) => {
    // 访问添加数据源页面
    await page.goto('/datasource/create');
    await waitForLoadingToDisappear(page);
    
    // 生成带特殊字符的测试数据
    const testData = DataSourceMockData.dataSourceWithSpecialChars();
    
    // 填写表单
    await page.fill('input[name="name"]', testData.name);
    
    // 选择类型
    await page.click('select[name="type"], [data-test="type-select"]');
    await page.click(`option[value="${testData.type}"], li:has-text("${testData.type.toUpperCase()}")`);
    
    // 填写主机信息
    await page.fill('input[name="host"]', testData.host);
    await page.fill('input[name="port"]', testData.port.toString());
    
    // 填写用户名等信息
    await page.fill('input[name="username"]', testData.username);
    await page.fill('input[name="password"]', testData.password);
    
    // 检查特殊字符是否被正确接受
    const nameValue = await page.inputValue('input[name="name"]');
    expect(nameValue).toBe(testData.name);
    
    const usernameValue = await page.inputValue('input[name="username"]');
    expect(usernameValue).toBe(testData.username);
    
    // 截图
    await page.screenshot({ path: 'tests/e2e/screenshots/datasource-special-chars.png' });
  });
}); 