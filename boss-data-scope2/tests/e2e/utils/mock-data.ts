import { faker } from '@faker-js/faker/locale/zh_CN';

/**
 * 数据源类型定义
 */
type DataSourceType = 'mysql' | 'postgresql' | 'oracle' | 'sqlserver' | 'mongodb' | 'elasticsearch';

/**
 * 数据源对象接口
 */
interface DataSource {
  name: string;
  type: string;
  host: string;
  port: number;
  databaseName: string;
  username: string;
  password: string;
  description: string;
}

/**
 * 测试使用的模拟数据
 */
export class DataSourceMockData {
  /**
   * 生成一个有效的数据源对象
   */
  static validDataSource(): DataSource {
    const timestamp = new Date().getTime();
    return {
      name: `测试数据源-${timestamp}`,
      type: 'mysql', // 可选值: mysql, postgresql, oracle 等
      host: 'localhost',
      port: 3306,
      databaseName: 'test_db',
      username: 'testuser',
      password: 'password123',
      description: '这是一个自动化测试创建的数据源'
    };
  }

  /**
   * 生成一个有效的数据源对象，用于更新测试
   */
  static updatedDataSource(): DataSource {
    const timestamp = new Date().getTime();
    return {
      name: `已更新-测试数据源-${timestamp}`,
      type: 'mysql',
      host: 'localhost',
      port: 3306,
      databaseName: 'updated_test_db',
      username: 'updateduser',
      password: 'newpassword456',
      description: '这是一个已更新的测试数据源'
    };
  }

  /**
   * 生成带有超长名称的数据源对象
   */
  static dataSourceWithLongName(): DataSource {
    const longName = 'A'.repeat(256); // 生成一个非常长的名称
    const longDescription = 'B'.repeat(1000); // 生成一个非常长的描述
    
    return {
      name: longName,
      type: 'mysql',
      host: 'localhost',
      port: 3306,
      databaseName: 'test_db',
      username: 'testuser',
      password: 'password123',
      description: longDescription
    };
  }

  /**
   * 生成包含SQL注入尝试的数据源对象
   */
  static dataSourceWithSqlInjection(): DataSource {
    return {
      name: "Test'--DROP TABLE users;",
      type: 'mysql',
      host: "localhost'; DROP TABLE datasources; --",
      port: 3306,
      databaseName: "test_db'; SELECT * FROM users; --",
      username: "admin'--",
      password: "' OR '1'='1",
      description: "<script>alert('XSS')</script>"
    };
  }

  /**
   * 生成具有不同数据库类型的数据源对象
   */
  static dataSourceByType(type: string): DataSource {
    const timestamp = new Date().getTime();
    const baseConfig = {
      name: `${type}-数据源-${timestamp}`,
      type: type.toLowerCase(),
      host: 'localhost',
      username: 'testuser',
      password: 'password123',
      description: `这是一个${type}类型的测试数据源`
    };

    // 根据数据库类型设置不同的默认端口和数据库名称
    switch (type.toLowerCase()) {
      case 'mysql':
        return {
          ...baseConfig,
          port: 3306,
          databaseName: 'mysql_test'
        };
      case 'postgresql':
        return {
          ...baseConfig,
          port: 5432,
          databaseName: 'postgres_test'
        };
      case 'oracle':
        return {
          ...baseConfig,
          port: 1521,
          databaseName: 'ORCL'
        };
      case 'sqlserver':
        return {
          ...baseConfig,
          port: 1433,
          databaseName: 'master'
        };
      default:
        return {
          ...baseConfig,
          port: 3306,
          databaseName: 'test_db'
        };
    }
  }

  /**
   * 生成多个不同类型的数据源对象
   */
  static multipleDataSources(count = 3): DataSource[] {
    const types = ['mysql', 'postgresql', 'oracle', 'sqlserver'];
    const result: DataSource[] = [];
    
    for (let i = 0; i < count; i++) {
      const type = types[i % types.length];
      result.push(this.dataSourceByType(type));
    }
    
    return result;
  }
} 