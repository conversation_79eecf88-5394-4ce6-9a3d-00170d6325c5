import { chromium, FullConfig } from '@playwright/test';
import { existsSync, mkdirSync } from 'fs';
import path from 'path';

/**
 * 全局测试设置
 * 在所有测试开始前执行一次
 */
async function globalSetup(config: FullConfig) {
  console.log('⚙️ 全局设置 - 开始');

  // 创建必要的目录
  const storagePath = path.join(process.cwd(), 'tests/e2e/storage');
  if (!existsSync(storagePath)) {
    mkdirSync(storagePath, { recursive: true });
    console.log(`✅ 创建存储目录: ${storagePath}`);
  }

  // 准备测试状态
  const baseURL = (config.projects[0].use as any).baseURL;
  console.log(`🌐 使用基础URL: ${baseURL}`);

  // 启动浏览器进行认证准备
  const browser = await chromium.launch();
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // 初始化认证状态
    await page.goto(baseURL);
    
    // 如果需要身份验证，可以在这里添加登录代码
    // 例如：
    // await page.fill('#username', 'testuser');
    // await page.fill('#password', 'password');
    // await page.click('button[type="submit"]');
    
    // 保存认证状态到storage
    await context.storageState({ path: path.join(storagePath, 'auth.json') });
    console.log('✅ 认证状态已保存');
  } catch (error) {
    console.error('❌ 全局设置失败:', error);
    throw error;
  } finally {
    await browser.close();
  }

  console.log('⚙️ 全局设置 - 完成');
}

export default globalSetup; 