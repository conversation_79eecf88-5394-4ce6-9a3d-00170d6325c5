import { mount } from '@vue/test-utils'
import { describe, it, expect, beforeEach, vi } from 'vitest'
import QueryResults from '@/components/query/QueryResults.vue'

describe('QueryResults组件', () => {
  // 模拟console防止测试输出太多日志
  beforeEach(() => {
    vi.spyOn(console, 'log').mockImplementation(() => {})
    vi.spyOn(console, 'warn').mockImplementation(() => {})
  })

  it('应该正确处理API返回的标准数据格式', async () => {
    // 模拟API返回的标准数据格式
    const results = {
      id: 'test-id',
      queryId: 'query-id',
      status: 'COMPLETED',
      executionTime: 114,
      rowCount: 3,
      rows: [
        { id: '1', name: 'Test 1', type: 'A' },
        { id: '2', name: 'Test 2', type: 'B' },
        { id: '3', name: 'Test 3', type: 'C' }
      ],
      columns: ['id', 'name', 'type']
    }
    
    const wrapper = mount(QueryResults, {
      props: {
        results,
        isLoading: false,
        error: null
      }
    })
    
    // 验证列标题
    const headers = wrapper.findAll('thead th')
    expect(headers.length).toBe(3)
    expect(headers[0].text()).toBe('id')
    expect(headers[1].text()).toBe('name')
    expect(headers[2].text()).toBe('type')
    
    // 验证行数据
    const rows = wrapper.findAll('tbody tr')
    expect(rows.length).toBe(3)
    
    // 验证记录总数显示
    const totalRecordsText = wrapper.find('.text-sm.text-gray-600').text()
    expect(totalRecordsText).toContain('共 3 条结果')
  })
  
  it('应该处理API返回的嵌套data-fields-rows数据格式', async () => {
    // 模拟API返回的嵌套data-fields-rows格式
    const results = {
      id: 'test-id',
      queryId: 'query-id',
      status: 'COMPLETED',
      executionTime: 114,
      data: {
        id: 'test-id',
        queryId: 'query-id',
        status: 'COMPLETED',
        executionTime: 114,
        rowCount: 3,
        fields: [
          { name: 'id', type: 'string' },
          { name: 'name', type: 'string' },
          { name: 'type', type: 'string' }
        ],
        rows: [
          { id: '1', name: 'Test 1', type: 'A' },
          { id: '2', name: 'Test 2', type: 'B' },
          { id: '3', name: 'Test 3', type: 'C' }
        ]
      }
    }
    
    const wrapper = mount(QueryResults, {
      props: {
        results,
        isLoading: false,
        error: null
      }
    })
    
    // 验证列标题
    const headers = wrapper.findAll('thead th')
    expect(headers.length).toBe(3)
    expect(headers[0].text()).toBe('id')
    expect(headers[1].text()).toBe('name')
    expect(headers[2].text()).toBe('type')
    
    // 验证行数据
    const rows = wrapper.findAll('tbody tr')
    expect(rows.length).toBe(3)
    
    // 验证记录总数显示
    const totalRecordsText = wrapper.find('.text-sm.text-gray-600').text()
    expect(totalRecordsText).toContain('共 3 条结果')
  })
  
  it('应该正确处理空值和特殊格式的值', async () => {
    // 模拟包含特殊值的数据
    const results = {
      id: 'test-id',
      queryId: 'query-id',
      status: 'COMPLETED',
      executionTime: 114,
      rowCount: 3,
      rows: [
        { id: '1', name: null, type: 'A', json: '{"key": "value"}' },
        { id: '2', name: '', type: null, json: null },
        { id: '3', name: 'Test 3', type: '', json: '[]' }
      ],
      columns: ['id', 'name', 'type', 'json']
    }
    
    const wrapper = mount(QueryResults, {
      props: {
        results,
        isLoading: false,
        error: null
      }
    })
    
    // 验证单元格值格式化
    const cells = wrapper.findAll('tbody td')
    
    // 检查NULL值显示
    expect(cells[1].text()).toBe('NULL')
    expect(cells[6].text()).toBe('NULL')
    expect(cells[7].text()).toBe('NULL')
    
    // 检查空字符串显示
    expect(cells[5].text()).toBe('(空字符串)')
    expect(cells[10].text()).toBe('(空字符串)')
    
    // 检查JSON格式化
    expect(cells[3].text()).not.toBe('{"key": "value"}')
    expect(cells[11].text()).not.toBe('[]')
  })
}) 