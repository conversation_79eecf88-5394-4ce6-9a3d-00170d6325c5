import { describe, it, expect } from 'vitest';

describe('解密配置数据结构测试', () => {
  it('应该正确生成AES解密配置', () => {
    const aesConfig = {
      aes: {
        key: 'test-aes-key-123456'
      }
    };

    expect(aesConfig).toEqual({
      aes: {
        key: 'test-aes-key-123456'
      }
    });

    expect(aesConfig.aes.key).toBe('test-aes-key-123456');
  });

  it('应该正确生成国密整体解密配置', () => {
    const gmFullConfig = {
      gm: {}
    };

    expect(gmFullConfig).toEqual({
      gm: {}
    });

    expect(gmFullConfig.gm).toBeDefined();
    expect(Object.keys(gmFullConfig.gm)).toHaveLength(0);
  });

  it('应该正确生成国密部分字段解密配置', () => {
    const gmPartialConfig = {
      gm: {
        encrypt_json_key: ['name', 'age']
      }
    };

    expect(gmPartialConfig).toEqual({
      gm: {
        encrypt_json_key: ['name', 'age']
      }
    });

    expect(gmPartialConfig.gm.encrypt_json_key).toEqual(['name', 'age']);
    expect(gmPartialConfig.gm.encrypt_json_key).toHaveLength(2);
  });

  it('应该正确验证配置格式', () => {
    // 测试有效的AES配置
    const validAesConfig = {
      aes: {
        key: 'valid-key'
      }
    };
    expect(validAesConfig.aes).toBeDefined();
    expect(validAesConfig.aes.key).toBeTruthy();

    // 测试有效的国密整体解密配置
    const validGmFullConfig = {
      gm: {}
    };
    expect(validGmFullConfig.gm).toBeDefined();

    // 测试有效的国密部分解密配置
    const validGmPartialConfig = {
      gm: {
        encrypt_json_key: ['field1', 'field2']
      }
    };
    expect(validGmPartialConfig.gm).toBeDefined();
    expect(Array.isArray(validGmPartialConfig.gm.encrypt_json_key)).toBe(true);
    expect(validGmPartialConfig.gm.encrypt_json_key.length).toBeGreaterThan(0);
  });

  it('应该正确判断是否启用了解密', () => {
    // 空配置 - 未启用解密
    const emptyConfig = {};
    const isEncryptedEmpty = !!(emptyConfig.aes || emptyConfig.gm);
    expect(isEncryptedEmpty).toBe(false);

    // AES配置 - 启用解密
    const aesConfig = {
      aes: {
        key: 'test-key'
      }
    };
    const isEncryptedAes = !!(aesConfig.aes || aesConfig.gm);
    expect(isEncryptedAes).toBe(true);

    // 国密配置 - 启用解密
    const gmConfig = {
      gm: {
        encrypt_json_key: ['name']
      }
    };
    const isEncryptedGm = !!(gmConfig.aes || gmConfig.gm);
    expect(isEncryptedGm).toBe(true);
  });

  it('应该正确处理配置的序列化和反序列化', () => {
    const originalConfig = {
      gm: {
        encrypt_json_key: ['name', 'age', 'email']
      }
    };

    // 序列化
    const serialized = JSON.stringify(originalConfig);
    expect(typeof serialized).toBe('string');

    // 反序列化
    const deserialized = JSON.parse(serialized);
    expect(deserialized).toEqual(originalConfig);
    expect(deserialized.gm.encrypt_json_key).toEqual(['name', 'age', 'email']);
  });

  it('应该正确处理边界情况', () => {
    // 空的encrypt_json_key数组
    const emptyArrayConfig = {
      gm: {
        encrypt_json_key: []
      }
    };
    expect(emptyArrayConfig.gm.encrypt_json_key).toHaveLength(0);

    // 单个字段的encrypt_json_key
    const singleFieldConfig = {
      gm: {
        encrypt_json_key: ['onlyField']
      }
    };
    expect(singleFieldConfig.gm.encrypt_json_key).toHaveLength(1);
    expect(singleFieldConfig.gm.encrypt_json_key[0]).toBe('onlyField');

    // 空字符串密钥（应该被视为无效）
    const emptyKeyConfig = {
      aes: {
        key: ''
      }
    };
    expect(emptyKeyConfig.aes.key).toBe('');
    expect(emptyKeyConfig.aes.key.trim()).toBe('');
  });
});
