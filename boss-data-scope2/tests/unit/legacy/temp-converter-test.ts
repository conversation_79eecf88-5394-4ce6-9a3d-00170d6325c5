import { convertToStandardConfig, convertFromStandardConfig } from '../src/utils/configConverter';

// 测试数据 - 模拟集成编辑页面的数据结构
const testData = {
  meta: {
    database: "测试数据库",
    schema: "测试schema",
    table: "测试表",
    pageCode: "testPage",
    apis: {
      query: { method: "GET", path: "/test-query" }
    }
  },
  tableConfig: {
    columns: [
      {
        field: "id",
        label: "ID",
        type: "TEXT"
      },
      {
        field: "name",
        label: "名称", 
        type: "TEXT"
      }
    ],
    pagination: {
      enabled: true,
      pageSize: 20
    },
    export: {
      enabled: true,
      formats: ["CSV", "EXCEL"]
    },
    actions: [
      {
        label: "新建",
        action: "create",
        icon: "plus",
        enabled: true,
        confirmationRequired: true,
        confirmationTitle: "确认创建",
        confirmationMessage: "确定要创建新记录吗？"
      }
    ],
    rowActions: [
      {
        label: "编辑",
        action: "edit",
        icon: "edit",
        enabled: true
      },
      {
        label: "删除",
        action: "delete",
        icon: "delete",
        enabled: true,
        confirmationRequired: true,
        confirmationTitle: "确认删除",
        confirmationMessage: "此操作不可撤销，确定要删除吗？",
        permissions: ["admin", "manager"]
      }
    ],
    batchActions: [
      {
        id: "batch_export",
        label: "批量导出",
        action: "exportSelected",
        icon: "download",
        enabled: true
      },
      {
        id: "batch_delete",
        label: "批量删除",
        action: "deleteSelected",
        icon: "delete",
        enabled: true,
        confirmationRequired: true,
        confirmationTitle: "批量删除确认",
        confirmationMessage: "确定要删除所选记录吗？此操作不可撤销。",
        permissions: ["admin"]
      }
    ]
  }
};

// 运行测试
function runTest() {
  console.log("======= 批量操作转换测试开始 =======");
  
  // 转换为标准配置
  const standardConfig = convertToStandardConfig(testData);
  console.log("标准化后batchEnable:", standardConfig.operation.batchEnable);
  console.log("标准化后batchActions数量:", standardConfig.operation.batchActions ? standardConfig.operation.batchActions.length : 0);
  
  if (standardConfig.operation.batchActions && standardConfig.operation.batchActions.length > 0) {
    console.log("第一个批量操作按钮名称:", standardConfig.operation.batchActions[0].name);
    console.log("第二个批量操作按钮名称:", standardConfig.operation.batchActions[1]?.name);
    
    // 检查确认对话框支持
    const hasBatchDeleteConfirm = standardConfig.operation.batchActions.some(
      action => action.hybridEvent === 'deleteSelected' && action.confirm && action.confirm.title
    );
    console.log("批量删除是否支持确认对话框:", hasBatchDeleteConfirm);
    
    // 检查权限控制支持
    const hasBatchDeletePermissions = standardConfig.operation.batchActions.some(
      action => action.hybridEvent === 'deleteSelected' && action.permissions && action.permissions.includes('admin')
    );
    console.log("批量删除是否支持权限控制:", hasBatchDeletePermissions);
  }
  
  // 再转换回内部格式
  const convertedBack = convertFromStandardConfig(standardConfig);
  console.log("还原后batchActions数量:", convertedBack.tableConfig.batchActions ? convertedBack.tableConfig.batchActions.length : 0);
  
  if (convertedBack.tableConfig.batchActions && convertedBack.tableConfig.batchActions.length > 0) {
    console.log("第一个批量操作按钮标签:", convertedBack.tableConfig.batchActions[0].label);
    console.log("第二个批量操作按钮标签:", convertedBack.tableConfig.batchActions[1]?.label);
    
    // 检查确认对话框支持
    const hasBatchDeleteConfirm = convertedBack.tableConfig.batchActions.some(
      action => action.action === 'deleteSelected' && action.confirmationRequired
    );
    console.log("批量删除是否支持确认对话框:", hasBatchDeleteConfirm);
    
    // 检查权限控制支持
    const hasBatchDeletePermissions = convertedBack.tableConfig.batchActions.some(
      action => action.action === 'deleteSelected' && action.permissions && action.permissions.includes('admin')
    );
    console.log("批量删除是否支持权限控制:", hasBatchDeletePermissions);
  }
  
  console.log("======= 批量操作转换测试结束 =======");
}

runTest();