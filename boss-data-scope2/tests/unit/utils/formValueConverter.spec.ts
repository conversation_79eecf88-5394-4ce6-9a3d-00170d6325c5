import { describe, it, expect } from 'vitest';
import dayjs from 'dayjs';
import { 
  serializeFormValue, 
  deserializeFormValue, 
  getDefaultEmptyValue, 
  isEmptyValue,
  FormFieldType
} from '@/utils/formValueConverter';

describe('formValueConverter', () => {
  describe('serializeFormValue', () => {
    it('should handle string values correctly', () => {
      expect(serializeFormValue('test', FormFieldType.TEXT)).toBe('test');
      expect(serializeFormValue('', FormFieldType.TEXT)).toBe('');
    });

    it('should handle number values correctly', () => {
      expect(serializeFormValue(123, FormFieldType.NUMBER)).toBe(123);
      expect(serializeFormValue('123', FormFieldType.NUMBER)).toBe(123);
      expect(serializeFormValue('', FormFieldType.NUMBER)).toBe('');
    });

    it('should handle date values correctly', () => {
      const dateStr = '2023-05-15';
      const dateObj = dayjs(dateStr);
      
      expect(serializeFormValue(dateStr, FormFieldType.DATE)).toBe(dateStr);
      expect(serializeFormValue(dateObj, FormFieldType.DATE)).toBe(dateStr);
    });

    it('should handle datetime values correctly', () => {
      const dateTimeStr = '2023-05-15T14:30';
      const dateTimeObj = dayjs(dateTimeStr);
      
      // Date objects should be formatted correctly
      expect(serializeFormValue(dateTimeObj, FormFieldType.DATETIME)).toBe(dateTimeStr);
      // String values should be normalized
      expect(serializeFormValue('2023-05-15T14:30:00', FormFieldType.DATETIME)).toBe(dateTimeStr);
    });

    it('should handle multiselect values correctly', () => {
      // Arrays should remain arrays
      expect(serializeFormValue(['a', 'b'], FormFieldType.MULTISELECT)).toEqual(['a', 'b']);
      // Single values should become single-item arrays
      expect(serializeFormValue('a', FormFieldType.MULTISELECT)).toEqual(['a']);
      // Empty values should become empty arrays
      expect(serializeFormValue('', FormFieldType.MULTISELECT)).toEqual([]);
      // null/undefined values should become empty arrays
      expect(serializeFormValue(null, FormFieldType.MULTISELECT)).toEqual(null);
    });

    it('should handle checkbox/boolean values correctly', () => {
      expect(serializeFormValue(true, FormFieldType.CHECKBOX)).toBe(true);
      expect(serializeFormValue('true', FormFieldType.CHECKBOX)).toBe(true);
      expect(serializeFormValue(false, FormFieldType.CHECKBOX)).toBe(false);
      expect(serializeFormValue('false', FormFieldType.CHECKBOX)).toBe(false);
    });

    it('should handle date-range values correctly', () => {
      const startDate = '2023-05-01';
      const endDate = '2023-05-31';
      const range = [startDate, endDate];
      
      expect(serializeFormValue(range, FormFieldType.DATE_RANGE)).toEqual(range);
      
      // Date objects in array should be serialized
      const rangeDateObjs = [dayjs(startDate), dayjs(endDate)];
      expect(serializeFormValue(rangeDateObjs, FormFieldType.DATE_RANGE)).toEqual(range);
    });

    it('should handle null and undefined values', () => {
      expect(serializeFormValue(null, FormFieldType.TEXT)).toBe(null);
      expect(serializeFormValue(undefined, FormFieldType.NUMBER)).toBe(null);
    });
  });

  describe('deserializeFormValue', () => {
    it('should handle string values correctly', () => {
      expect(deserializeFormValue('test', FormFieldType.TEXT)).toBe('test');
      expect(deserializeFormValue('', FormFieldType.TEXT)).toBe('');
    });

    it('should handle number values correctly', () => {
      expect(deserializeFormValue(123, FormFieldType.NUMBER)).toBe(123);
      expect(deserializeFormValue('123', FormFieldType.NUMBER)).toBe(123);
      expect(deserializeFormValue('', FormFieldType.NUMBER)).toBe('');
    });

    it('should handle multiselect values correctly', () => {
      expect(deserializeFormValue(['a', 'b'], FormFieldType.MULTISELECT)).toEqual(['a', 'b']);
      expect(deserializeFormValue('a', FormFieldType.MULTISELECT)).toEqual(['a']);
      expect(deserializeFormValue('', FormFieldType.MULTISELECT)).toEqual([]);
    });

    it('should handle checkbox values correctly', () => {
      expect(deserializeFormValue(true, FormFieldType.CHECKBOX)).toBe(true);
      expect(deserializeFormValue('true', FormFieldType.CHECKBOX)).toBe(true);
      expect(deserializeFormValue(false, FormFieldType.CHECKBOX)).toBe(false);
      expect(deserializeFormValue('false', FormFieldType.CHECKBOX)).toBe(false);
    });

    it('should handle null and undefined values', () => {
      expect(deserializeFormValue(null, FormFieldType.TEXT)).toBe('');
      expect(deserializeFormValue(undefined, FormFieldType.NUMBER)).toBe('');
    });
  });

  describe('getDefaultEmptyValue', () => {
    it('should return correct empty values for different types', () => {
      expect(getDefaultEmptyValue(FormFieldType.TEXT)).toBe('');
      expect(getDefaultEmptyValue(FormFieldType.TEXTAREA)).toBe('');
      expect(getDefaultEmptyValue(FormFieldType.NUMBER)).toBe('');
      expect(getDefaultEmptyValue(FormFieldType.DATE)).toBe('');
      expect(getDefaultEmptyValue(FormFieldType.MULTISELECT)).toEqual([]);
      expect(getDefaultEmptyValue(FormFieldType.CHECKBOX)).toBe(false);
      expect(getDefaultEmptyValue(FormFieldType.DATE_RANGE)).toEqual([]);
    });
  });

  describe('isEmptyValue', () => {
    it('should correctly identify empty values', () => {
      expect(isEmptyValue('')).toBe(true);
      expect(isEmptyValue(null)).toBe(true);
      expect(isEmptyValue(undefined)).toBe(true);
      expect(isEmptyValue([])).toBe(true);
      expect(isEmptyValue('   ')).toBe(true);
    });

    it('should correctly identify non-empty values', () => {
      expect(isEmptyValue('test')).toBe(false);
      expect(isEmptyValue(0)).toBe(false);
      expect(isEmptyValue(false)).toBe(false);
      expect(isEmptyValue(['item'])).toBe(false);
    });
  });

  describe('roundtrip serialization', () => {
    it('should maintain value integrity after serialization and deserialization', () => {
      // Text
      const textValue = 'sample text';
      expect(
        deserializeFormValue(serializeFormValue(textValue, FormFieldType.TEXT), FormFieldType.TEXT)
      ).toBe(textValue);

      // Number
      const numberValue = 42;
      expect(
        deserializeFormValue(serializeFormValue(numberValue, FormFieldType.NUMBER), FormFieldType.NUMBER)
      ).toBe(numberValue);

      // Boolean
      const boolValue = true;
      expect(
        deserializeFormValue(serializeFormValue(boolValue, FormFieldType.CHECKBOX), FormFieldType.CHECKBOX)
      ).toBe(boolValue);

      // Multiselect
      const arrayValue = ['option1', 'option2'];
      expect(
        deserializeFormValue(serializeFormValue(arrayValue, FormFieldType.MULTISELECT), FormFieldType.MULTISELECT)
      ).toEqual(arrayValue);
    });
  });
});