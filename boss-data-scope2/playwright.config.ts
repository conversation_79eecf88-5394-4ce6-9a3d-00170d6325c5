import { defineConfig, devices } from '@playwright/test';
import path from 'path';
import { fileURLToPath } from 'url';

// 在ES模块中获取当前文件路径
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Playwright测试配置
 * @see https://playwright.dev/docs/test-configuration
 */
export default defineConfig({
  testDir: './tests/e2e',
  
  /* 最大测试失败次数 */
  maxFailures: 5,
  
  /* 每个测试的超时时间 */
  timeout: 10000,
  
  /* 测试之间的预期输出 */
  expect: {
    timeout: 3000,
  },
  
  /* 测试并发运行 */
  fullyParallel: false,
  
  /* 默认报告器配置 */
  reporter: [
    ['html', { outputFolder: 'playwright-report' }],
    ['json', { outputFile: 'playwright-report/test-results.json' }]
  ],
  
  /* 共享全局设置 */
  use: {
    /* 自动拍摄截图 */
    screenshot: 'only-on-failure',
    
    /* 收集跟踪以帮助调试 */
    trace: 'on-first-retry',
    
    /* 在每个测试中启动新的浏览器上下文 */
    contextOptions: {
      ignoreHTTPSErrors: true,
    },
    
    /* 基本URL用于相对导航 */
    baseURL: process.env.BASE_URL || 'http://localhost:3000',
    
    /* 启用视频录制 */
    video: 'on-first-retry',
    
    /* 操作超时设置 */
    actionTimeout: 3000,
    navigationTimeout: 6000,
  },
  
  /* 配置项目以并行运行测试 */
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    // {
    //   name: 'firefox',
    //   use: { ...devices['Desktop Firefox'] },
    // },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
    /* 移动浏览器测试 */
    // {
    //   name: 'mobile-chrome',
    //   use: { ...devices['Pixel 5'] },
    // },
    // {
    //   name: 'mobile-safari',
    //   use: { ...devices['iPhone 12'] },
    // },
  ],
  
  /* 打包本地服务器所需的文件夹 */
  webServer: {
    command: 'npm run dev', // 启动本地服务器的命令
    port: 3000,
    reuseExistingServer: !process.env.CI,
    timeout: 120000, // 等待服务器启动的超时时间
  },
}); 