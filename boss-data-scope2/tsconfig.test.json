{"extends": "./tsconfig.json", "compilerOptions": {"composite": true, "lib": ["es2022", "dom"], "types": ["node", "@playwright/test"], "module": "commonjs", "target": "es2022", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "allowJs": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@tests/*": ["tests/*"]}}, "include": ["tests/**/*.ts", "tests/**/*.js"], "exclude": ["node_modules"]}