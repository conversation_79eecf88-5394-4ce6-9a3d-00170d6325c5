# DataScope UI 组件库使用指南

## 概述

DataScope前端使用了一套完整的UI组件库，基于Vue 3和Tailwind CSS构建。本文档提供组件库的使用指南，帮助开发人员正确、一致地使用这些组件。

## 组件列表

DataScope UI库包含以下核心组件：

| 组件名称 | 描述 | 文件路径 |
|---------|------|---------|
| PageHeader | 页面标题和操作按钮 | src/components/common/PageHeader.vue |
| Button | 标准化按钮 | src/components/common/Button.vue |
| DataTable | 数据表格 | src/components/common/DataTable.vue |
| StatusBadge | 状态标签 | src/components/common/StatusBadge.vue |
| Modal | 模态对话框 | src/components/common/Modal.vue |
| ConfirmModal | 确认对话框 | src/components/common/ConfirmModal.vue |
| Pagination | 分页控件 | src/components/common/Pagination.vue |
| Icon | 图标组件 | src/components/common/Icon.vue |
| LoadingSpinner | 加载动画 | src/components/common/LoadingSpinner.vue |
| BaseForm | 表单基础组件 | src/components/common/BaseForm.vue |
| DateRangePicker | 日期范围选择器 | src/components/common/DateRangePicker.vue |
| MessageAlert | 消息提醒 | src/components/common/MessageAlert.vue |

## 主要组件使用示例

### PageHeader - 页面标题组件

PageHeader组件用于所有页面顶部，提供一致的标题显示和操作按钮区域。

```vue
<template>
  <PageHeader 
    title="数据源管理"
    :actionItems="[
      {
        text: '添加数据源',
        type: 'primary',
        onClick: showAddForm
      },
      {
        text: '刷新',
        type: 'default',
        onClick: refreshData
      }
    ]"
  />
</template>

<script setup>
import PageHeader from '@/components/common/PageHeader.vue';

const showAddForm = () => {
  // 处理添加数据源
};

const refreshData = () => {
  // 处理刷新数据
};
</script>
```

### Button - 按钮组件

Button组件提供统一的按钮样式和行为，支持多种变体和尺寸。

```vue
<template>
  <div class="space-x-2">
    <!-- 主要按钮 -->
    <Button variant="primary">主要按钮</Button>
    
    <!-- 次要按钮 -->
    <Button variant="secondary">次要按钮</Button>
    
    <!-- 成功按钮 -->
    <Button variant="success" icon="check">成功</Button>
    
    <!-- 危险按钮 -->
    <Button variant="danger" icon="trash">删除</Button>
    
    <!-- 文本按钮 -->
    <Button variant="text">文本按钮</Button>
    
    <!-- 不同尺寸 -->
    <Button size="sm">小按钮</Button>
    <Button size="md">中按钮</Button>
    <Button size="lg">大按钮</Button>
    
    <!-- 加载状态 -->
    <Button :loading="true">加载中</Button>
    
    <!-- 禁用状态 -->
    <Button :disabled="true">禁用按钮</Button>
    
    <!-- 块级按钮 -->
    <Button :block="true">块级按钮</Button>
  </div>
</template>

<script setup>
import Button from '@/components/common/Button.vue';
</script>
```

### DataTable - 数据表格组件

DataTable组件用于展示表格数据，支持排序、筛选、分页等功能。

```vue
<template>
  <DataTable
    :columns="columns"
    :data-source="dataSource"
    :pagination="pagination"
    :row-selection="{
      selectedRowKeys,
      onChange: (keys) => selectedRowKeys = keys
    }"
    @change="handleTableChange"
  >
    <template #action="{ record }">
      <Button variant="text" icon="edit" @click="handleEdit(record)">编辑</Button>
      <Button variant="text" icon="trash" @click="handleDelete(record)">删除</Button>
    </template>
  </DataTable>
</template>

<script setup>
import { ref } from 'vue';
import DataTable from '@/components/common/DataTable.vue';
import Button from '@/components/common/Button.vue';

const columns = [
  {
    key: 'name',
    title: '名称',
    dataIndex: 'name',
    sortable: true,
    filterable: true
  },
  {
    key: 'status',
    title: '状态',
    dataIndex: 'status'
  },
  {
    key: 'action',
    title: '操作',
    slots: { customRender: 'action' }
  }
];

const dataSource = ref([
  { id: '1', name: '示例1', status: 'active' },
  { id: '2', name: '示例2', status: 'inactive' }
]);

const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 2
});

const selectedRowKeys = ref([]);

const handleTableChange = (event) => {
  console.log('表格变化:', event);
};

const handleEdit = (record) => {
  console.log('编辑:', record);
};

const handleDelete = (record) => {
  console.log('删除:', record);
};
</script>
```

### StatusBadge - 状态标签组件

StatusBadge组件用于显示各种状态指示器，提供一致的视觉效果。

```vue
<template>
  <div class="space-y-2">
    <StatusBadge status="success" text="成功" />
    <StatusBadge status="warning" text="警告" />
    <StatusBadge status="error" text="错误" />
    <StatusBadge status="info" text="信息" />
    <StatusBadge status="default" text="默认" />
    
    <!-- 自定义内容 -->
    <StatusBadge status="success">
      <Icon name="check" class="mr-1" /> 自定义成功
    </StatusBadge>
  </div>
</template>

<script setup>
import StatusBadge from '@/components/common/StatusBadge.vue';
import Icon from '@/components/common/Icon.vue';
</script>
```

### Modal - 模态对话框组件

Modal组件用于创建模态对话框，支持自定义内容和交互。

```vue
<template>
  <Button @click="showModal = true">打开模态框</Button>
  
  <Modal
    v-model:visible="showModal"
    title="模态框标题"
    :footer="true"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <p>这里是模态框内容...</p>
  </Modal>
</template>

<script setup>
import { ref } from 'vue';
import Modal from '@/components/common/Modal.vue';
import Button from '@/components/common/Button.vue';

const showModal = ref(false);

const handleOk = () => {
  console.log('确认');
  showModal.value = false;
};

const handleCancel = () => {
  console.log('取消');
  showModal.value = false;
};
</script>
```

## 组件视觉效果

### 按钮组件视觉效果

按钮组件提供了多种样式变体，适应不同场景：

```
主要按钮: ⬛️ 蓝色实心按钮，用于主要操作
次要按钮: ⬜️ 灰色边框按钮，用于次要操作
成功按钮: 🟩 绿色实心按钮，用于表示成功或确认操作
危险按钮: 🟥 红色实心按钮，用于危险操作如删除
文本按钮: 文本样式按钮，无背景，用于较轻量级操作
```

### 状态标签视觉效果

状态标签使用一致的色彩系统：

```
成功状态: 🟢 浅绿底深绿字，表示成功或激活状态
警告状态: 🟡 浅黄底深黄字，表示警告或需要注意
错误状态: 🔴 浅红底深红字，表示错误或失败
信息状态: 🔵 浅蓝底深蓝字，表示一般信息
默认状态: ⚪️ 浅灰底深灰字，表示默认或未知状态
```

### 表格组件视觉效果

表格组件采用了简洁的设计风格：

```
表头: 灰色背景，深灰文字
表体: 白色背景，黑色文字，悬停行浅灰背景
边框: 浅灰色边框分隔行
分页: 底部带有分页控件，包含页码和每页条数选择
```

### 页面标题组件视觉效果

页面标题组件提供统一的页面顶部布局：

```
标题: 左侧大号黑色文字
操作区: 右侧按钮组，主要操作使用主色调实心按钮
间距: 标准内边距和底部外边距，与内容区分离
```

## 推荐使用模式

1. **页面布局**：每个页面应使用以下基本结构：
   ```vue
   <template>
     <div class="container mx-auto px-4 py-6">
       <PageHeader title="页面标题" :actionItems="actionItems" />
       <div class="bg-white shadow rounded-lg p-6">
         <!-- 页面内容 -->
       </div>
     </div>
   </template>
   ```

2. **数据列表页面**：列表页面应使用以下模式：
   ```vue
   <template>
     <div class="container mx-auto px-4 py-6">
       <PageHeader title="列表页面" :actionItems="actionItems" />
       
       <!-- 过滤区域 -->
       <div class="bg-white shadow rounded-lg p-6 mb-6">
         <!-- 过滤表单 -->
       </div>
       
       <!-- 数据表格 -->
       <div class="bg-white shadow rounded-lg">
         <DataTable :columns="columns" :data-source="dataSource" :pagination="pagination" />
       </div>
     </div>
   </template>
   ```

3. **表单页面**：表单页面应使用以下模式：
   ```vue
   <template>
     <div class="container mx-auto px-4 py-6">
       <PageHeader title="表单页面" :actionItems="actionItems" />
       
       <div class="bg-white shadow rounded-lg p-6">
         <BaseForm :form-items="formItems" :model="formModel" @submit="handleSubmit" />
       </div>
     </div>
   </template>
   ```

## 最佳实践

1. **始终使用组件库**：使用已有组件而非直接使用HTML+Tailwind
2. **保持样式一致性**：遵循既定的色彩和间距规范
3. **响应式设计**：所有页面应支持从移动设备到桌面的响应式布局
4. **组件属性**：充分利用组件提供的属性定制行为，而非修改组件本身
5. **插槽使用**：合理使用组件提供的插槽自定义内容
6. **交互反馈**：为用户操作提供适当的加载状态和结果反馈

## 待改进的页面

以下页面需要重点改进以符合组件库标准：

1. **数据源管理页面**：使用PageHeader代替自定义标题，使用Button组件代替原生按钮
2. **查询服务页面**：重构页面头部，使用一致的过滤器区域，使用DataTable组件
3. **系统集成页面**：使用一致的列表展示样式，统一操作按钮

## 结论

这份组件库提供了构建一致性用户界面所需的全部工具。通过正确使用这些组件，可以显著提高UI一致性和开发效率。我们建议所有开发人员熟悉这些组件，并在新功能开发和页面改进中优先使用它们。