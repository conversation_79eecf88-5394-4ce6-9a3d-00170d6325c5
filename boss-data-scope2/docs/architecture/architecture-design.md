---
description:
globs:
alwaysApply: true
---
# DataScope系统架构设计

## 概述

DataScope是一个专为公司内部员工设计的智能化数据发现与查询系统。系统允许用户将不同的数据库系统（如MySQL、DB2）无缝集成为数据源，自动提取和管理元数据，并通过SQL或自然语言进行数据查询。系统特别强调了自然语言转SQL（NL2SQL）的能力，利用OpenRouter的LLM服务降低数据访问门槛。此外，系统支持表关系智能推断与手动维护，以及与低代码平台的深度集成，通过生成标准化的JSON配置和API接口，实现快速的应用开发和数据展示。

## 架构原则

DataScope的架构设计遵循以下原则：

1. **领域驱动设计(DDD)** - 使用DDD方法组织代码，将业务逻辑与技术实现分离
2. **关注点分离** - 清晰地分离不同的系统职责
3. **模块化** - 通过定义明确的模块边界实现高内聚低耦合
4. **可扩展性** - 设计灵活的架构以支持新的数据源类型和功能
5. **安全优先** - 在设计的各个层面考虑安全性
6. **可测试性** - 架构支持各个层次的自动化测试

## 1. 核心业务模型和数据实体分析

DataScope系统基于清晰的领域模型构建。完整的领域模型文档请参见[领域模型文档](domain-model.md)。

以下是主要业务流程和数据实体的简要概述：

1. **数据源注册与管理**：管理外部数据库连接
2. **元数据提取与维护**：自动提取和更新数据结构信息
3. **数据查询**：支持SQL和自然语言查询
4. **表关系管理**：自动推断和手动定义表关系
5. **页面配置生成**：生成低代码平台所需的配置

## 2. 系统整体架构图

```mermaid
graph TB
    subgraph "前端层"
        UI[用户界面]
        subgraph "前端组件"
            DSM[数据源管理界面]
            QM[查询管理界面]
            PC[页面配置工具]
            RM[关系管理界面]
        end
    end

    subgraph "API网关层"
        AG[API网关]
    end

    subgraph "应用服务层"
        DSS[数据源服务]
        QS[查询服务]
        MDS[元数据服务]
        RS[关系服务]
        PCS[页面配置服务]
        NLPS[自然语言处理服务]
    end

    subgraph "领域层"
        DSDM[数据源领域模型]
        QDM[查询领域模型]
        MDM[元数据领域模型]
        RDM[关系领域模型]
        PCDM[页面配置领域模型]
    end

    subgraph "基础设施层"
        DS[(数据源适配器)]
        MR[(元数据仓库)]
        QR[(查询仓库)]
        RR[(关系仓库)]
        PCR[(页面配置仓库)]
        RC[(Redis缓存)]
        LLM[OpenRouter LLM]
    end

    subgraph "外部系统"
        EDB1[(MySQL数据源)]
        EDB2[(DB2数据源)]
        LCDP[低代码平台]
    end

    UI --> DSM & QM & PC & RM
    DSM & QM & PC & RM --> AG

    AG --> DSS & QS & MDS & RS & PCS & NLPS

    DSS --> DSDM
    QS --> QDM
    MDS --> MDM
    RS --> RDM
    PCS --> PCDM
    NLPS --> QDM

    DSDM --> DS & MR
    QDM --> QR & RC
    MDM --> MR & RC
    RDM --> RR & RC
    PCDM --> PCR

    NLPS --> LLM
    DS --> EDB1 & EDB2
    PCS --> LCDP
    QS --> EDB1 & EDB2
```

## 3. 主要模块划分及职责

### 3.0 模块概述

DataScope系统的核心功能通过前后端多个模块协同实现：

#### 前端主要组件
1. **数据源管理组件**：数据源的CRUD和状态监控
2. **查询管理组件**：
   - SQL编辑器 (语法高亮、自动完成)
   - 自然语言查询界面
   - 查询结果展示
   - 查询历史和收藏管理
3. **表关系管理组件**：表关系定义和可视化
4. **页面配置组件**：低代码集成配置界面
5. **可视化组件**：数据图表展示和配置

#### 后端主要模块
1. **数据源管理模块**：处理数据源连接和状态管理
2. **元数据提取和同步模块**：从数据源提取元数据并支持增量同步
3. **查询管理模块**：执行SQL查询、管理查询历史和收藏
4. **自然语言处理模块**：集成OpenRouter LLM将自然语言转换为SQL
5. **表关系推断模块**：自动推断和管理表间关系
6. **页面配置模块**：生成低代码平台所需的JSON配置
7. **系统集成模块**：提供API接口供外部系统调用
8. **查询性能分析模块**：监控慢查询并提供优化建议
9. **系统监控模块**：收集和展示系统性能指标

以下各节将详细介绍每个模块的职责和核心组件。

### 3.1 数据源管理模块

**职责**：

- 管理数据源的注册、编辑、删除和状态监控
- 安全存储数据源连接信息（使用AES-GCM加密密码）
- 提供数据源连接测试功能
- 管理数据源同步策略和状态
- 处理最多100个数据源的同步

**核心组件**：

- `DataSourceController`: 提供数据源管理REST API
- `DataSourceService`: 处理数据源业务逻辑
- `DataSourceRepository`: 数据源持久化
- `EncryptionService`: 密码加密解密服务
- `ConnectionTestService`: 连接测试服务

### 3.2 元数据提取和同步模块

**职责**：

- 从数据源提取元数据信息，包括Schema/Table/Column/Index
- 支持全量和增量同步策略（24小时同步周期）
- 存储和维护元数据
- 提供元数据查询接口
- 管理同步任务和日志

**核心组件**：

- `MetadataController`: 提供元数据查询REST API
- `MetadataService`: 处理元数据业务逻辑
- `MetadataExtractor`: 负责从不同数据源提取元数据
- `MySQLMetadataExtractor`: MySQL元数据提取实现
- `DB2MetadataExtractor`: DB2元数据提取实现
- `SyncTaskScheduler`: 同步任务调度器
- `IncrementalSyncService`: 增量同步服务
- `MetadataDiffAnalyzer`: 元数据差异分析器
  - 比较同步前后的元数据差异
  - 识别新增、修改和删除的表和列
  - 检测数据类型、长度等属性变更
  - 生成差异报告
  - 评估变更影响范围
  - 支持差异可视化展示

### 3.3 查询管理模块

**职责**：

- 管理用户SQL和自然语言查询
- 执行SQL查询并返回结果
- 验证和优化SQL
- 管理查询历史和收藏
- 控制查询频率和超时（默认30秒）
- 支持查询结果CSV导出（最多50000条）

**核心组件**：

- `QueryController`: 提供查询相关REST API
- `QueryService`: 处理查询业务逻辑
- `QueryExecutor`: 执行SQL查询
- `QueryValidator`: 验证SQL语法和安全性
- `QueryOptimizer`: 优化SQL语句
- `RateLimiter`: 查询频率限制
- `TimeoutManager`: 查询超时控制
- `CSVExportService`: 查询结果导出服务

### 3.4 自然语言处理模块

**职责**：

- 将自然语言查询转换为SQL语句
- 利用元数据和表关系信息提高转换准确性
- 与OpenRouter LLM服务集成
- 支持多轮对话和上下文管理
- 提供SQL微调建议

**核心组件**：

- `NLQueryController`: 提供自然语言查询REST API
- `NLQueryService`: 处理自然语言查询业务逻辑
- `NLToSQLConverter`: 自然语言到SQL转换器
- `PromptGenerator`: 生成LLM提示
- `LLMIntegrationService`: 与OpenRouter集成
- `ContextManager`: 管理对话上下文
- `SQLFeedbackService`: 处理SQL微调反馈
- `LLMConfigManager`: LLM服务配置管理组件
  - 管理多个LLM服务提供商配置
  - 支持服务优先级设置
  - 处理API密钥和模型参数的安全存储
  - 提供配置有效性验证
  - 支持动态切换LLM服务
  - 收集和分析LLM服务性能指标

### 3.5 表关系推断模块

**职责**：

- 通过查询历史和数据分析推断表间关系
- 管理手动定义的表关系
- 提供关系可视化
- 支持关系查询和使用
- 自动识别外键或通过数据分析推断关联

**核心组件**：

- `RelationController`: 提供关系管理REST API
- `RelationService`: 处理关系业务逻辑
- `RelationInferenceEngine`: 关系推断引擎
- `QueryHistoryAnalyzer`: 分析查询历史推断关系
- `DataContentAnalyzer`: 分析数据内容推断关系
- `RelationRepository`: 关系持久化
- `RelationVisualizer`: 关系可视化组件
  - 构建表关系的图形化表示
  - 提供不同关系类型的视觉区分
  - 支持图形布局算法（力导向、层次、环形等）
  - 处理大型关系图的性能优化
  - 支持交互式操作（缩放、平移、展开/折叠）
  - 提供表节点详情查看
  - 支持关系图的导出（PNG, PDF, SVG）

### 3.6 页面配置模块

**职责**：

- 生成和管理查询相关的页面配置
- 支持查询条件和结果展示配置
- 转换配置为特定格式的JSON
- 管理配置版本
- 提供AI辅助配置功能
- 支持敏感数据掩码配置
- 自动隐藏不常用查询条件

**核心组件**：

- `PageConfigController`: 提供页面配置REST API
- `PageConfigService`: 处理页面配置业务逻辑
- `ConfigGenerator`: 生成页面配置
- `AIConfigAssistant`: AI辅助配置
- `DataTypeUIMapper`: 数据类型到UI组件映射
- `ConfigRepository`: 配置持久化
- `VersionManager`: 配置版本管理
  - 实现Git类似的版本控制机制
  - 存储配置变更历史和差异
  - 生成版本间差异报告
  - 提供版本回滚功能
  - 支持回滚后创建新版本
  - 审计日志记录所有版本操作
  - 版本冲突解决策略
- `MaskingRuleManager`: 敏感数据掩码规则管理
- `DynamicConditionManager`: 动态查询条件管理

### 3.7 系统集成模块

**职责**：

- 提供低代码平台集成API
- 管理API版本
- 处理数据查询请求
- 转换和格式化查询结果
- 支持多种数据呈现形式（查询表单、查看页面、图表展示）

**核心组件**：

- `IntegrationController`: 提供集成REST API
- `IntegrationService`: 处理集成业务逻辑
- `APIVersionManager`: API版本管理
- `ResultFormatter`: 结果格式化
- `CSVExporter`: CSV导出功能
- `LowCodeConfigManager`: 低代码配置管理
- `DisplayTemplateManager`: 展示模板管理

### 3.8 查询性能分析模块

**职责**：

- 监控并记录超过阈值的慢查询
- 分析慢查询的执行计划和性能瓶颈
- 提供查询优化建议
- 生成性能趋势报告
- 配置性能警报规则
- 支持历史慢查询的搜索和分析

**核心组件**：

- `SlowQueryDetector`: 慢查询识别组件
- `QueryAnalyzer`: SQL查询分析器
- `ExecutionPlanVisualizer`: 执行计划可视化
- `OptimizationAdvisor`: 查询优化建议生成器
- `PerformanceAlertManager`: 性能警报管理
- `QueryStatisticsCollector`: 查询统计收集器
- `PerformanceDashboard`: 性能仪表盘组件

### 3.9 系统监控仪表盘模块

**职责**：

- 收集和展示系统性能指标
- 提供可视化仪表盘和图表
- 支持自定义监控视图
- 设置和管理性能阈值和告警
- 历史性能数据分析
- 资源利用率预测

**核心组件**：

- `MetricsCollector`: 指标收集组件
- `DashboardManager`: 仪表盘管理
- `VisualizationEngine`: 数据可视化引擎
- `AlertingService`: 告警服务
- `HistoricalAnalyzer`: 历史数据分析
- `ResourcePredictor`: 资源预测组件
- `CustomWidgetManager`: 自定义监控组件管理

## 4. 数据流向图

### 4.1 数据流向概述

DataScope系统中的主要数据流向包括：

1. **用户界面交互流**
   - 用户UI交互 → 前端应用 → API网关 → 后端服务
   - 处理用户的各种操作请求，如数据源管理、查询执行等

2. **数据源连接与元数据流**
   - 后端服务 → 外部数据库系统
   - 提取和同步表、列等元数据信息

3. **查询执行流**
   - 前端SQL编辑器 → API网关 → 查询服务 → 数据源适配器 → 外部数据源
   - 执行用户编写的SQL语句并返回结果

4. **自然语言查询流**
   - 前端 → API网关 → NLP服务 → LLM → SQL生成 → 查询执行
   - 将用户的自然语言描述转换为SQL并执行

5. **表关系管理流**
   - 自动推断：元数据+查询历史 → 关系推断引擎 → 关系存储
   - 手动定义：用户输入 → 关系服务 → 关系存储

6. **配置生成流**
   - 用户配置 → 配置服务 → 生成JSON → 低代码平台集成

### 4.2 关键流程序列图

以下图表展示了关键流程的详细序列图。

```mermaid
sequenceDiagram
    participant User as 用户
    participant Frontend as 前端应用
    participant Gateway as API网关
    participant NLP as 自然语言处理服务
    participant LLM as OpenRouter LLM
    participant MetaSvc as 元数据服务
    participant RelSvc as 关系服务
    participant QuerySvc as 查询服务
    participant DataSourceAdapter as 数据源适配器
    participant ExternalDB as 外部数据源
    participant RelViz as 关系可视化组件

    User->>Frontend: 输入自然语言查询
    Frontend->>Gateway: 发送查询请求
    Gateway->>NLP: 转发到NLP服务

    NLP->>MetaSvc: 获取相关元数据
    MetaSvc-->>NLP: 返回元数据

    NLP->>RelSvc: 获取表关系
    RelSvc-->>NLP: 返回表关系

    NLP->>LLM: 调用LLM生成SQL
    LLM-->>NLP: 返回生成的SQL

    NLP->>NLP: 验证和优化SQL
    NLP-->>Gateway: 返回SQL和微调建议
    Gateway-->>Frontend: 显示SQL和微调建议

    User->>Frontend: 确认或微调SQL
    Frontend->>Gateway: 发送执行请求
    Gateway->>QuerySvc: 转发到查询服务

    QuerySvc->>DataSourceAdapter: 执行SQL查询
    DataSourceAdapter->>ExternalDB: 在外部数据源执行
    ExternalDB-->>DataSourceAdapter: 返回查询结果
    DataSourceAdapter-->>QuerySvc: 返回处理后的结果

    QuerySvc->>QuerySvc: 格式化和分页处理
    QuerySvc-->>Gateway: 返回最终结果
    Gateway-->>Frontend: 显示查询结果

    User->>Frontend: 请求下载CSV
    Frontend->>Gateway: 发送导出请求
    Gateway->>QuerySvc: 请求CSV导出
    QuerySvc-->>Gateway: 返回CSV数据
    Gateway-->>Frontend: 提供CSV下载

    User->>Frontend: 请求表关系可视化
    Frontend->>Gateway: 发送可视化请求
    Gateway->>RelSvc: 转发请求
    
    RelSvc->>MetaSvc: 获取表元数据
    MetaSvc-->>RelSvc: 返回表元数据
    
    RelSvc->>RelSvc: 获取表关系数据
    RelSvc->>RelViz: 转换为可视化格式
    RelViz->>RelViz: 应用布局算法
    RelViz-->>RelSvc: 返回可视化数据
    
    RelSvc-->>Gateway: 返回可视化数据
    Gateway-->>Frontend: 转发可视化数据
    Frontend->>Frontend: 渲染关系图
    
    User->>Frontend: 交互操作（缩放/平移）
    Frontend->>Frontend: 本地处理交互
    
    User->>Frontend: 请求导出关系图
    Frontend->>Gateway: 发送导出请求
    Gateway->>RelSvc: 转发导出请求
    RelSvc->>RelViz: 生成导出文件
    RelViz-->>RelSvc: 返回导出文件
    RelSvc-->>Gateway: 返回导出文件
    Gateway-->>Frontend: 提供文件下载
```

API设计详情请参见[API设计文档](api-design.md)
技术规格请参见[技术规格文档](technical-specs.md)