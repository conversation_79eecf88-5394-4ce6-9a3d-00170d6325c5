# DataScope 数据模型设计文档

## 文档概述

本文档详细描述了DataScope系统的数据模型设计，包括数据库表结构、字段定义、索引设计和表间关系。此文档作为领域模型文档的补充，更专注于系统的物理数据存储结构和实现细节。

## 1. 核心实体与关系图

以下关系图展示了DataScope系统中核心数据实体之间的关系：

```mermaid
erDiagram
    DataSource ||--o{ Metadata : contains
    Metadata ||--o{ Column : contains
    Metadata ||--o{ TableRelation : participates_as_source
    Metadata ||--o{ TableRelation : participates_as_target
    Column ||--o{ TableRelation : used_in_source
    Column ||--o{ TableRelation : used_in_target
    DataSource ||--o{ Query : targets
    Query ||--o{ QueryParam : defines
    Query ||--o{ QueryExecution : has
    QueryExecution ||--o{ QueryResult : produces
    User ||--o{ Query : creates
    User ||--o{ PageConfig : designs
    Query ||--o{ PageConfig : configures
    PageConfig ||--o{ ColumnMapping : defines
    Column ||--o{ ColumnMapping : maps
```

## 2. 详细实体定义

### 2.1 DataSource（数据源）

存储系统连接的外部数据库信息。

| 字段名 | 数据类型 | 说明 | 约束 |
|-------|---------|------|------|
| id | BIGINT | 主键 | PK, 自增 |
| name | VARCHAR(100) | 数据源名称 | 非空, 唯一 |
| type | VARCHAR(50) | 数据库类型(MYSQL,DB2等) | 非空 |
| url | VARCHAR(500) | 连接URL | 非空 |
| username | VARCHAR(100) | 连接用户名 | 非空 |
| password | VARCHAR(1000) | 加密的连接密码 | 非空 |
| dbName | VARCHAR(100) | 数据库名称 | 非空 |
| status | VARCHAR(20) | 连接状态(ACTIVE,ERROR等) | 非空 |
| lastSyncTime | TIMESTAMP | 上次同步时间 | 允许空 |
| syncInterval | INT | 同步间隔(小时) | 默认24 |
| createdBy | BIGINT | 创建者用户ID | FK->User.id |
| createdAt | TIMESTAMP | 创建时间 | 非空, 默认当前时间 |
| updatedAt | TIMESTAMP | 更新时间 | 非空, 默认当前时间 |

### 2.2 Metadata（元数据表）

存储从数据源提取的表元数据。

| 字段名 | 数据类型 | 说明 | 约束 |
|-------|---------|------|------|
| id | BIGINT | 主键 | PK, 自增 |
| dataSourceId | BIGINT | 所属数据源ID | FK->DataSource.id |
| schemaName | VARCHAR(100) | 模式名称 | 非空 |
| tableName | VARCHAR(100) | 表名 | 非空 |
| tableType | VARCHAR(50) | 表类型(TABLE,VIEW等) | 非空 |
| description | VARCHAR(1000) | 表描述 | 允许空 |
| lastSyncTime | TIMESTAMP | 上次同步时间 | 非空 |
| rows | BIGINT | 表行数(估计值) | 允许空 |

_唯一约束_: (dataSourceId, schemaName, tableName)

### 2.3 Column（列信息）

存储表的列元数据信息。

| 字段名 | 数据类型 | 说明 | 约束 |
|-------|---------|------|------|
| id | BIGINT | 主键 | PK, 自增 |
| tableId | BIGINT | 所属表ID | FK->Metadata.id |
| name | VARCHAR(100) | 列名 | 非空 |
| dataType | VARCHAR(50) | 数据类型 | 非空 |
| isNullable | BOOLEAN | 是否可为空 | 非空 |
| isPrimaryKey | BOOLEAN | 是否为主键 | 非空, 默认false |
| isForeignKey | BOOLEAN | 是否为外键 | 非空, 默认false |
| defaultValue | VARCHAR(500) | 默认值 | 允许空 |
| description | VARCHAR(1000) | 列描述 | 允许空 |
| ordinalPosition | INT | 列在表中的位置 | 非空 |
| isHidden | BOOLEAN | 是否隐藏(不显示给用户) | 非空, 默认false |
| isSensitive | BOOLEAN | 是否敏感数据 | 非空, 默认false |

_唯一约束_: (tableId, name)

### 2.4 TableRelation（表关系）

存储表间的关系，包括自动推断和手动定义的关系。

| 字段名 | 数据类型 | 说明 | 约束 |
|-------|---------|------|------|
| id | BIGINT | 主键 | PK, 自增 |
| sourceTableId | BIGINT | 源表ID | FK->Metadata.id |
| targetTableId | BIGINT | 目标表ID | FK->Metadata.id |
| sourceColId | BIGINT | 源列ID | FK->Column.id |
| targetColId | BIGINT | 目标列ID | FK->Column.id |
| relationType | VARCHAR(20) | 关系类型(ONE_TO_ONE,ONE_TO_MANY等) | 非空 |
| confidence | FLOAT | 关系置信度(0-1,推断关系使用) | 非空, 默认1.0 |
| isManual | BOOLEAN | 是否手动定义 | 非空, 默认false |
| createdAt | TIMESTAMP | 创建时间 | 非空, 默认当前时间 |
| updatedAt | TIMESTAMP | 更新时间 | 非空, 默认当前时间 |

_唯一约束_: (sourceTableId, targetTableId, sourceColId, targetColId)

### 2.5 Query（查询）

存储用户保存的查询。

| 字段名 | 数据类型 | 说明 | 约束 |
|-------|---------|------|------|
| id | BIGINT | 主键 | PK, 自增 |
| name | VARCHAR(100) | 查询名称 | 非空 |
| description | VARCHAR(1000) | 查询描述 | 允许空 |
| sqlText | TEXT | SQL文本 | 非空 |
| dataSourceId | BIGINT | 目标数据源ID | FK->DataSource.id |
| createdBy | BIGINT | 创建者ID | FK->User.id |
| createdAt | TIMESTAMP | 创建时间 | 非空, 默认当前时间 |
| updatedAt | TIMESTAMP | 更新时间 | 非空, 默认当前时间 |
| isPublic | BOOLEAN | 是否公开 | 非空, 默认false |
| viewCount | INT | 查看次数 | 非空, 默认0 |
| lastRunTime | TIMESTAMP | 上次运行时间 | 允许空 |

### 2.6 QueryParam（查询参数）

存储查询的参数定义。

| 字段名 | 数据类型 | 说明 | 约束 |
|-------|---------|------|------|
| id | BIGINT | 主键 | PK, 自增 |
| queryId | BIGINT | 所属查询ID | FK->Query.id |
| name | VARCHAR(100) | 参数名称 | 非空 |
| dataType | VARCHAR(50) | 数据类型 | 非空 |
| defaultValue | VARCHAR(500) | 默认值 | 允许空 |
| description | VARCHAR(500) | 参数描述 | 允许空 |
| isRequired | BOOLEAN | 是否必填 | 非空, 默认true |
| uiComponent | VARCHAR(50) | UI组件类型 | 非空, 默认"INPUT" |
| displayOrder | INT | 显示顺序 | 非空, 默认0 |

_唯一约束_: (queryId, name)

### 2.7 QueryExecution（查询执行记录）

记录查询执行的历史记录。

| 字段名 | 数据类型 | 说明 | 约束 |
|-------|---------|------|------|
| id | BIGINT | 主键 | PK, 自增 |
| queryId | BIGINT | 执行的查询ID | FK->Query.id |
| parameters | TEXT | 执行参数(JSON格式) | 允许空 |
| startTime | TIMESTAMP | 开始时间 | 非空 |
| endTime | TIMESTAMP | 结束时间 | 允许空 |
| status | VARCHAR(20) | 执行状态(RUNNING,COMPLETED,ERROR) | 非空 |
| error | TEXT | 错误信息 | 允许空 |
| executedBy | BIGINT | 执行用户ID | FK->User.id |
| executionTime | INT | 执行耗时(毫秒) | 允许空 |

### 2.8 QueryResult（查询结果）

存储查询执行结果数据。

| 字段名 | 数据类型 | 说明 | 约束 |
|-------|---------|------|------|
| id | BIGINT | 主键 | PK, 自增 |
| executionId | BIGINT | 执行记录ID | FK->QueryExecution.id |
| resultData | LONGTEXT | 结果数据(JSON格式) | 非空 |
| resultFormat | VARCHAR(20) | 结果格式(JSON,CSV等) | 非空, 默认"JSON" |
| rowCount | INT | 结果行数 | 非空 |
| createdAt | TIMESTAMP | 创建时间 | 非空, 默认当前时间 |

### 2.9 User（用户）

系统用户信息。

| 字段名 | 数据类型 | 说明 | 约束 |
|-------|---------|------|------|
| id | BIGINT | 主键 | PK, 自增 |
| username | VARCHAR(50) | 用户名 | 非空, 唯一 |
| password | VARCHAR(256) | 加密密码 | 非空 |
| email | VARCHAR(100) | 邮箱 | 非空, 唯一 |
| fullName | VARCHAR(100) | 全名 | 非空 |
| role | VARCHAR(20) | 角色(ADMIN,ANALYST,DEVELOPER) | 非空, 默认"ANALYST" |
| lastLoginTime | TIMESTAMP | 上次登录时间 | 允许空 |
| createdAt | TIMESTAMP | 创建时间 | 非空, 默认当前时间 |
| updatedAt | TIMESTAMP | 更新时间 | 非空, 默认当前时间 |

### 2.10 PageConfig（页面配置）

存储低代码集成的页面配置。

| 字段名 | 数据类型 | 说明 | 约束 |
|-------|---------|------|------|
| id | BIGINT | 主键 | PK, 自增 |
| name | VARCHAR(100) | 配置名称 | 非空 |
| description | VARCHAR(1000) | 配置描述 | 允许空 |
| queryId | BIGINT | 关联的查询ID | FK->Query.id |
| configData | TEXT | 配置数据(JSON格式) | 非空 |
| version | VARCHAR(20) | 版本号 | 非空, 默认"1.0.0" |
| createdBy | BIGINT | 创建者ID | FK->User.id |
| createdAt | TIMESTAMP | 创建时间 | 非空, 默认当前时间 |
| updatedAt | TIMESTAMP | 更新时间 | 非空, 默认当前时间 |
| isPublished | BOOLEAN | 是否发布 | 非空, 默认false |

### 2.11 ColumnMapping（列映射）

定义页面配置中的列映射和显示规则。

| 字段名 | 数据类型 | 说明 | 约束 |
|-------|---------|------|------|
| id | BIGINT | 主键 | PK, 自增 |
| pageConfigId | BIGINT | 所属页面配置ID | FK->PageConfig.id |
| columnId | BIGINT | 映射的列ID | FK->Column.id |
| displayName | VARCHAR(100) | 显示名称 | 非空 |
| isVisible | BOOLEAN | 是否可见 | 非空, 默认true |
| displayOrder | INT | 显示顺序 | 非空, 默认0 |
| formatter | VARCHAR(500) | 格式化表达式 | 允许空 |
| maskRule | VARCHAR(500) | 掩码规则 | 允许空 |
| isFilterable | BOOLEAN | 是否可过滤 | 非空, 默认false |
| isSortable | BOOLEAN | 是否可排序 | 非空, 默认false |

_唯一约束_: (pageConfigId, columnId)

## 3. 索引设计

除了主键和外键索引外，还需要创建以下索引以提高系统性能：

### 3.1 DataSource表索引
- idx_datasource_name (name)
- idx_datasource_status (status)
- idx_datasource_lastsync (lastSyncTime)

### 3.2 Metadata表索引
- idx_metadata_datasource (dataSourceId)
- idx_metadata_schema_table (dataSourceId, schemaName, tableName)
- idx_metadata_lastsync (lastSyncTime)

### 3.3 Column表索引
- idx_column_table (tableId)
- idx_column_primary (tableId, isPrimaryKey)
- idx_column_sensitive (tableId, isSensitive)

### 3.4 Query表索引
- idx_query_datasource (dataSourceId)
- idx_query_creator (createdBy)
- idx_query_popularity (viewCount)

### 3.5 QueryExecution表索引
- idx_queryexec_query (queryId)
- idx_queryexec_time (startTime, endTime)
- idx_queryexec_user (executedBy)

### 3.6 PageConfig表索引
- idx_pageconfig_query (queryId)
- idx_pageconfig_creator (createdBy)
- idx_pageconfig_published (isPublished)

## 4. 数据模型特性与考量

### 4.1 安全性设计
- 数据源密码使用AES-GCM加密存储
- 用户密码加密存储
- 敏感列标记和掩码规则机制
- 通过isHidden和isSensitive标记管理数据访问控制

### 4.2 性能优化
- 采用合理的索引设计
- 查询执行记录与查询结果分离存储
- 大文本内容(如resultData)使用LONGTEXT类型
- 适当使用复合索引优化查询性能

### 4.3 扩展性设计
- configData和parameters使用JSON格式存储，支持灵活扩展
- 表关系支持多种关系类型
- 页面配置支持版本管理
- 数据源类型可扩展，支持添加更多数据库类型

### 4.4 可追溯性
- 所有实体都有createdAt和updatedAt时间戳
- 查询执行保留完整记录
- 页面配置修改使用版本控制
- 用户操作可通过createdBy和executedBy追踪

## 5. 数据库实现建议

### 5.1 MySQL实现
- 使用InnoDB存储引擎确保事务和外键约束
- 对大文本字段如sqlText和resultData考虑使用压缩
- 使用行级锁提高并发性能
- 对敏感数据列配置TDE加密

### 5.2 数据迁移与演进
- 使用Flyway或Liquibase管理数据库版本
- 设计清晰的迁移策略和回滚计划
- 增量脚本遵循幂等原则

### 5.3 高可用性考虑
- 主从复制配置
- 定期备份策略
- 数据库连接池配置(如HikariCP)

## 6. 与领域模型的对应关系

本数据模型与领域模型文档中定义的实体有直接对应关系：

| 数据模型实体 | 领域模型实体 | 说明 |
|------------|------------|------|
| DataSource | DataSource | 直接对应 |
| Metadata | Table | 对应领域模型中的Table |
| Column | Column | 直接对应 |
| TableRelation | TableRelation | 直接对应 |
| Query | Query | 直接对应 |
| User | User | 直接对应 |
| PageConfig | PageConfig | 直接对应 |

## 7. 数据初始化

系统初始化时需要创建的基础数据：

1. 默认管理员用户
2. 系统配置参数
3. 示例数据源(可选)

## 参考文档

- [领域模型文档](domain-model.md)
- [架构设计文档](architecture-design.md)
- [API设计文档](api-design.md)
- [需求分析文档](../需求分析.md) 