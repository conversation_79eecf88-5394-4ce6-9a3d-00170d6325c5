# 查询版本管理功能说明

## 功能概述

查询版本管理是 DataScope 的核心功能，旨在提供完整的查询生命周期管理、版本控制和状态管理。此功能可确保查询在不同环境（如开发、测试和生产）中有一致且可追踪的工作流。

## 主要功能

### 版本控制

- **版本历史**：记录查询的所有历史版本，包括变更时间、作者和变更内容
- **版本比较**：直观比较不同版本之间的SQL差异、参数变化和元数据变更
- **版本标记**：为重要版本添加标签和注释，便于后续查找和管理

### 状态管理

支持查询版本的完整生命周期管理，包括以下状态：

- **草稿（Draft）**：初始创建或修改的版本
- **审核中（Review）**：已提交等待审核的版本
- **已批准（Approved）**：通过审核的版本
- **已发布（Published）**：正式发布到生产环境的版本
- **已弃用（Deprecated）**：不再推荐使用的版本
- **已归档（Archived）**：已归档不可修改的版本

### 状态流转

版本状态管理支持以下流转路径：

- **草稿** → **审核中**：提交查询到审核流程
- **审核中** → **已批准**：批准通过此查询（需要审核权限）
- **审核中** → **草稿**：退回查询到草稿状态进行修改
- **已批准** → **已发布**：将批准的查询发布到生产环境
- **已批准** → **草稿**：退回已批准的查询到草稿状态
- **已发布** → **已弃用**：将此查询标记为已弃用
- **已弃用** → **已发布**：重新激活已弃用的查询
- **已弃用** → **已归档**：将此查询归档（不可恢复）

## 核心组件

### 用户界面组件

- **QueryDetailView**：查询详情页，集成版本控制和状态管理功能
- **VersionManagementView**：版本管理专用页面
- **QueryListView**：查询版本列表页面，提供版本筛选、搜索和管理功能
- **VersionStatusManager**：版本状态管理组件，支持状态转换和历史查看
- **StatusHelpTips**：状态管理帮助提示组件，提供状态工作流的详细说明和使用指南

### API 服务

- **queryVersionService**：提供版本相关的API调用，如创建版本、获取版本列表和详情、变更版本状态等
- **queryService**：提供查询相关的API调用，并集成版本相关功能

## 使用指南

### 创建新版本

基于现有查询创建新版本：

1. 在查询详情页点击"创建新版本"按钮
2. 填写版本名称、说明和变更记录
3. 修改SQL语句和参数配置
4. 提交创建新版本

### 比较版本

比较两个版本之间的差异：

1. 在版本历史列表中选择两个要比较的版本
2. 点击"比较版本"按钮
3. 在比较界面查看SQL差异、元数据变更和参数变化

### 管理版本状态

变更版本状态：

1. 在查询详情页打开"状态管理"面板
2. 选择目标状态（如从"草稿"提交到"审核中"）
3. 添加状态变更说明（可选）
4. 确认状态变更

### 查看版本历史

查看查询的版本历史：

1. 在查询详情页选择"版本历史"选项卡
2. 浏览所有历史版本及其详细信息
3. 选择任意版本进行查看或比较

## 权限控制

版本管理功能中的不同操作需要不同级别的权限：

- **查看版本**：所有用户
- **创建草稿**：具有编辑权限的用户
- **提交审核**：具有编辑权限的用户
- **批准版本**：具有审核权限的用户
- **发布版本**：具有发布权限的用户
- **废弃版本**：具有发布权限的用户
- **归档版本**：具有发布权限的用户

## 最佳实践

1. **定期创建版本**：定期为重要查询创建版本，确保能够追踪查询的演变历史
2. **添加详细描述**：为每个版本添加详细的描述和变更记录，便于其他团队成员理解
3. **合理使用状态**：按照推荐的工作流使用状态管理，不要跳过重要的状态转换
4. **使用标签**：为关键版本添加标签，如"季度报表"、"审计专用"等
5. **定期清理**：定期将不再使用的版本归档，保持活跃版本列表的简洁