# Element Plus 迁移到 Ant Design Vue 指南

## 迁移背景

当前项目同时使用了Element Plus和Ant Design Vue两种UI框架，这导致了以下问题：
- 项目体积增大，影响加载速度
- 开发人员需要学习两套组件API
- 样式冲突，特别是z-index管理混乱
- 代码维护困难，无法统一开发规范

因此，我们决定将所有Element Plus组件逐步迁移到Ant Design Vue，统一项目的UI框架。

## 组件映射表

下面是常用Element Plus组件到Ant Design Vue的映射关系：

| Element Plus | Ant Design Vue | 注意事项 |
|--------------|---------------|---------|
| el-form | a-form | Form布局和验证规则需要调整 |
| el-input | a-input | 事件和插槽名称有差异 |
| el-select | a-select | 多选模式和filterable属性需要转换 |
| el-option | a-select-option | 需要一起迁移 |
| el-date-picker | a-date-picker | 格式化和范围选择器的处理方式不同 |
| el-checkbox | a-checkbox | 组和单个复选框的处理有差异 |
| el-radio | a-radio | 组和单个单选框的处理有差异 |
| el-switch | a-switch | 值定义不同 |
| el-table | a-table | 数据结构和插槽定义完全不同，需要重写 |
| el-pagination | a-pagination | 属性名称和事件不同 |
| el-dialog | a-modal | 使用方式和事件有很大差异 |
| el-tabs | a-tabs | 内容定义方式不同 |
| el-dropdown | a-dropdown | 菜单结构定义不同 |
| el-menu | a-menu | 整体结构和事件处理不同 |
| el-notification | message.success等 | 调用方式完全不同 |

## 属性映射

| Element Plus属性 | Ant Design Vue属性 | 
|-----------------|-------------------|
| v-model | v-model:value |
| label | label |
| placeholder | placeholder |
| disabled | disabled |
| clearable | allowClear |
| filterable | show-search |
| multiple | mode="multiple" |
| @change | @change |
| @input | @update:value |
| size="small" | size="small" |

## 迁移步骤

### 1. 准备工作
- 确保项目中已安装最新版Ant Design Vue (`npm install ant-design-vue@^4.0.0`)
- 在main.ts中正确引入Ant Design Vue和样式
- 移除Element Plus相关导入和配置

### 2. 组件迁移顺序
1. 先迁移基础表单组件（input, select等）
2. 迁移布局和容器组件（form, card等）
3. 迁移复杂交互组件（table, dialog等）
4. 最后处理导航和全局组件（menu, notification等）

### 3. 迁移技巧
- 使用搜索工具找出所有Element Plus组件使用
- 先在样式表中添加Ant Design Vue对应的样式覆盖
- 逐个替换组件，保证功能正常
- 使用注释标记待迁移和已迁移的组件
- 使用git分支管理，确保迁移过程可回退

### 4. 常见问题

#### Form组件迁移
Element Plus:
```vue
<el-form :model="form" :rules="rules" ref="formRef">
  <el-form-item label="名称" prop="name">
    <el-input v-model="form.name"></el-input>
  </el-form-item>
</el-form>
```

Ant Design Vue:
```vue
<a-form :model="form" ref="formRef">
  <a-form-item label="名称" name="name" :rules="nameRules">
    <a-input v-model:value="form.name"></a-input>
  </a-form-item>
</a-form>
```

#### Select组件迁移
Element Plus:
```vue
<el-select v-model="value" placeholder="请选择" clearable filterable>
  <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
</el-select>
```

Ant Design Vue:
```vue
<a-select v-model:value="value" placeholder="请选择" allow-clear show-search :filter-option="filterOption">
  <a-select-option v-for="item in options" :key="item.value" :value="item.value">{{ item.label }}</a-select-option>
</a-select>
```

#### Dialog组件迁移
Element Plus:
```vue
<el-dialog v-model="dialogVisible" title="标题" width="30%">
  <span>内容</span>
  <template #footer>
    <el-button @click="dialogVisible = false">取消</el-button>
    <el-button type="primary" @click="confirm">确定</el-button>
  </template>
</el-dialog>
```

Ant Design Vue:
```vue
<a-modal v-model:visible="dialogVisible" title="标题" :width="'30%'" @ok="confirm" @cancel="dialogVisible = false">
  <span>内容</span>
  <template #footer>
    <a-button @click="dialogVisible = false">取消</a-button>
    <a-button type="primary" @click="confirm">确定</a-button>
  </template>
</a-modal>
```

## 测试策略

为确保迁移过程不影响功能正常运行，建议采用以下测试策略：

1. 单元测试：为关键组件编写单元测试
2. 功能测试：每个迁移完成的页面需要进行功能验证
3. 视觉回归测试：确保UI外观一致
4. 性能测试：监控迁移前后的性能变化

## 时间规划

预计迁移工作分为以下阶段：

1. **准备阶段**（1周）：评估、组件映射、制定计划
2. **基础组件迁移**（2周）：表单、按钮等基础组件
3. **复杂组件迁移**（3周）：表格、弹窗等复杂组件
4. **全局组件迁移**（2周）：导航、消息通知等
5. **测试和修复**（2周）：全面测试和问题修复
6. **文档更新**（1周）：更新开发文档和示例

## 注意事项

- 迁移过程中保持功能不变
- 优先处理用户经常使用的页面
- 保持组件API的一致性，必要时编写包装组件
- 注意事件名称和参数的差异
- 处理好CSS样式冲突
- 更新相关文档和示例

## 降级方案

如果迁移过程中发现重大问题，可以采用以下降级方案：

1. 回退到迁移前的版本
2. 部分关键组件保留Element Plus实现
3. 编写适配层兼容两种组件库 