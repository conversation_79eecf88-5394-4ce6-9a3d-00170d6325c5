# 需求文档

## 目标用户

公司内部员工，系统定位为公司内部使用的信息管理系统

## 核心功能

- 数据源管理：
    - 允许用户将不同的数据库系统（如MySQL、DB2）无缝集成为数据源。系统自动、全面的从数据源提取元数据并持久存储，包括模式、表、列、索引、数据类型、长度和描述等元数据。
    - 数据源主要是MySQL和DB2，数据源最多不超过100个，每个数据源有不超过100个表，每个表的数据量级在几千到几亿不等；
    - 该系统并不同步数据源中的数据，而是保存描述这些数据源的元数据信息，实际的数据查询还是需要请求到数据源执行；
    - 数据源涉及密码，密码需要AES(GCM)加密后保存；
    - 智能推断的表关系需要保存下来以便后续直接使用；
    - 实现健壮的、可配置的同步机制，以维护所有托管数据源之间的数据一致性；元数据提取过程需要支持增量更新；定时同步周期为24小时；
    - 不需要支持特定版本的MySQL特性；

- 查询管理模块
    - 用户可以选择数据源，并查看有哪些 schema、table、column 等信息。用户可以通过使用 SQL、自然语言描述需求来查询想要的数据。当用户使用自然语言描述时，允许用户根据显示结果做微调。
    - 系统根据历史用户的查询习惯学习所有表之间的关联关系，用户也可以手工维护这些表之间的关联关系。
    - 自然语言查询功能需要对接OpenRouter的LLM的接口来实现自然语言转成sql语句，支持在配置文件中维护LLM接口的请求地址、key、模型等参数；
    - 为用户提供一个直观的界面来探索所选数据源中的可用模式、表和列。通过SQL查询和自然语言描述支持数据检索。结合一个优化循环，允许用户根据显示的结果迭代地调整自然语言查询。利用机器学习从历史查询模式中推断和学习表关系，同时还提供手动定义和维护关系的机制。
    - 需要实现更高级的功能，比如自动识别外键或通过数据分析推断关联；
    - 系统需要支持查询历史和收藏功能；

- 系统集成模块
    - 给低代码平台使用的查询服务的api接口、页面配置应该有版本化，以免出现修改时影响其他用户的正常使用；
    - 页面配置模块：需要描述查询条件和查询结果如何展示，系统也可以借助 AI 能力配置信息。用户可以下载特定格式的json，低代码平台可以解析该json生成前端页面，从而实现功能的快速交付。
    - 除了给低代码平台提供json来方便前端生成查询页面，还要提供统一的api接口来实现数据查询的能力；
    - 通过提供工具来定义如何在低代码环境中呈现查询条件和结果，从而简化应用程序开发过程。通过人工智能辅助增强配置过程，智能地建议和生成显示配置。
    - 在api接口层面需要细化LowCodeConfig的设计，以便后续和低代码平台沟通交互细节；
    - 期望支持不同的数据呈现形式，比如查询表单、查看页面、图表展示等形式。
    - 需要集成到特定的低代码平台，只需要按照约定好的交互协议生成json即可。
    - 界面配置结果列的显示属性应该加入敏感数据的掩码能力，在查询页面可以通过点击“查看”按钮看到原文；
    - 支持配置操作列；
    - 界面配置支持查询条件的配置，部分列会作为必填条件；
    - 需要梳理数据库列数据类型与界面显示元素的对应关系，比如数据库中datetime列应该对应前端界面的时间选择器组件；
    - 当查询界面的查询条件过多时应该能自动隐藏用户不经常使用的条件，如果用户需要指定隐藏的条件时可以点击“更多条件”；

- 每个用户的查询频率应该被合理的限制，避免对系统正常运行造成影响；
- 查询界面用到的api接口参数可以多于界面配置显示的条件和结果列；
- 查询数据源的每个api接口可以配置查询超时时间，默认为30秒；
- 查询界面应该提供一个下载数据的功能，生成csv格式，单次下载数据量不能超过50000条；

## 技术要求

- 采用前后端分离的模块化架构
- 不考虑访问控制
- 不考虑多租户
- 要求生成完整的前后端代码，并确保其可用于实际开发
- 使用真实的 UI 图片，而非占位符图片（可从 Unsplash、Pexels、Apple 官方 UI 资源中选择）
- 前端技术栈：Vue、Tailwind CSS、FontAwesome
- 后端技术栈：Java、Spring Boot、MyBatis、Maven
- 数据库：MySQL
- 缓存：Redis
- 部署环境：Docker

