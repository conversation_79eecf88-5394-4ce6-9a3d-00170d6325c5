# DataScope UI 组件展示

本文档展示了DataScope UI库中的核心组件及其视觉效果，供设计和开发团队参考。

## 按钮组件 (Button)

按钮组件提供了统一的交互元素，支持多种样式和状态。

### 主要变体

```
[ 主要按钮 ]  - 深蓝背景，白色文字，用于主要操作
[ 次要按钮 ]  - 浅灰背景，深色文字，用于次要操作
[ 成功按钮 ]  - 绿色背景，白色文字，用于表示成功或确认操作
[ 危险按钮 ]  - 红色背景，白色文字，用于危险操作如删除
[ 文本按钮 ]  - 无背景，蓝色文字，用于轻量级操作
```

### 尺寸变体

```
[ 小 ]  - 小号按钮，用于紧凑界面
[ 中 ]  - 标准尺寸按钮，默认大小
[ 大 ]  - 大号按钮，用于强调
```

### 状态变体

```
[ 正常 ]     - 正常交互状态
[ 加载中... ] - 显示加载指示器，按钮禁用
[ 禁用按钮 ]  - 灰色透明效果，表示不可用
```

## 状态标签组件 (StatusBadge)

状态标签用于表示项目的不同状态，使用一致的色彩体系。

```
[ 正常 ]  - 绿底绿字，表示正常或成功状态
[ 警告 ]  - 黄底黄字，表示警告或需要注意
[ 错误 ]  - 红底红字，表示错误或失败状态
[ 信息 ]  - 蓝底蓝字，表示一般信息状态
[ 默认 ]  - 灰底灰字，表示默认或中性状态
```

## 页面标题组件 (PageHeader)

页面标题组件提供统一的页面顶部结构，包含标题和操作区。

```
-----------------------------------------------------------------
|  数据源管理                               [ 刷新 ] [ 添加数据源 ] |
-----------------------------------------------------------------
```

页面标题组件特点：
- 左侧显示大号粗体页面标题
- 右侧显示操作按钮，主要操作使用主色调按钮
- 一致的间距和位置
- 明确的视觉层次

## 数据表格组件 (DataTable)

数据表格提供结构化数据展示，支持多种交互功能。

```
-----------------------------------------------------------------
|  名称 ▲      |  类型        |  状态        |  操作              |
-----------------------------------------------------------------
|  示例1      |  MySQL      |  [ 正常 ]    |  [ 编辑 ] [ 删除 ]  |
-----------------------------------------------------------------
|  示例2      |  PostgreSQL |  [ 错误 ]    |  [ 编辑 ] [ 删除 ]  |
-----------------------------------------------------------------
                                        < 1 2 3 ... 10 >
```

数据表格特点：
- 灰色表头，白色或条纹表身
- 一致的单元格内边距
- 内置排序、筛选功能
- 底部分页控件
- 支持行选择
- 支持自定义单元格渲染

## 模态对话框组件 (Modal)

模态对话框提供弹出式交互界面，用于重要操作确认或表单输入。

```
┌─────────────────────────────────────┐
│  对话框标题                      ╳  │
├─────────────────────────────────────┤
│                                     │
│  这里是对话框内容                    │
│                                     │
│                                     │
├─────────────────────────────────────┤
│                      [ 取消 ] [ 确定 ] │
└─────────────────────────────────────┘
```

模态对话框特点：
- 居中显示，背景遮罩
- 标题栏带关闭按钮
- 内容区可自定义
- 底部操作按钮区域，默认包含取消和确定按钮
- 响应式设计，适应不同屏幕尺寸

## 确认对话框组件 (ConfirmModal)

确认对话框是模态对话框的特化版本，用于操作确认。

```
┌─────────────────────────────────────┐
│  确认删除                       ╳  │
├─────────────────────────────────────┤
│                                     │
│  您确定要删除"示例数据"吗？           │
│  此操作无法撤销。                    │
│                                     │
├─────────────────────────────────────┤
│                      [ 取消 ] [ 删除 ] │
└─────────────────────────────────────┘
```

确认对话框特点：
- 简洁的确认信息
- 操作后果说明
- 右侧操作按钮，危险操作使用红色按钮

## 分页组件 (Pagination)

分页组件提供统一的列表分页控制器。

```
显示 1 至 10 条，共 42 条    < 1 2 3 ... 5 >    10 条/页 ▼
```

分页组件特点：
- 显示当前页码和总页数
- 提供上一页、下一页导航
- 支持页码直接跳转
- 可配置每页显示条数
- 显示总记录数和当前显示范围

## 加载状态组件 (LoadingSpinner)

加载状态组件提供统一的加载指示器。

```
    ╱╲
   ╱  ╲
  │    │
   ╲  ╱
    ╲╱
 加载中...
```

加载状态特点：
- 居中显示的旋转动画
- 可选的加载文字提示
- 可配置尺寸和颜色
- 支持全屏或局部使用

## 标准页面布局

DataScope使用统一的页面布局结构，确保一致的用户体验。

```
------------------------------------------------------------
|                      导航栏                               |
------------------------------------------------------------
|                                                          |
|  页面标题                               操作按钮区          |
|                                                          |
|  ┌────────────────────────────────────────────────────┐  |
|  │                                                    │  |
|  │                    过滤区域                         │  |
|  │                                                    │  |
|  └────────────────────────────────────────────────────┘  |
|                                                          |
|  ┌────────────────────────────────────────────────────┐  |
|  │                                                    │  |
|  │                    内容区域                         │  |
|  │                                                    │  |
|  └────────────────────────────────────────────────────┘  |
|                                                          |
------------------------------------------------------------
|                      页脚区域                             |
------------------------------------------------------------
```

标准页面布局特点：
- 顶部固定导航栏
- 页面标题和操作按钮区域
- 可选的过滤/搜索区域，使用卡片样式
- 主要内容区域，使用卡片样式
- 底部页脚区域
- 响应式布局，适应不同屏幕尺寸

## 结论

DataScope UI组件库提供了完整的组件集合，用于构建一致的用户界面。遵循本文档中的视觉规范和使用模式，可以确保整个应用程序具有一致的用户体验。