<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图标按钮 - DataScope UI</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        .action-button {
            transition: all 0.2s;
        }
        .action-button:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body class="bg-gray-50">
    <header class="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="../index.html" class="flex items-center">
                        <h1 class="text-xl font-bold text-gray-900">DataScope UI</h1>
                    </a>
                </div>
                <nav class="flex items-center space-x-4">
                    <a href="../index.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">首页</a>
                    <a href="../components.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">组件</a>
                    <a href="../guides/index.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">使用指引</a>
                    <a href="../sitemap.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">网站地图</a>
                    <a href="https://github.com/yourusername/datascope-ui" target="_blank" class="text-gray-500 hover:text-indigo-600">
                        <i class="ri-github-fill text-xl"></i>
                    </a>
                </nav>
            </div>
        </div>
    </header>

    <div class="container mx-auto px-4 py-8">
        <div class="flex items-center mb-8">
            <a href="../index.html" class="text-gray-500 hover:text-indigo-600 mr-2">
                <i class="ri-home-line"></i>
            </a>
            <span class="text-gray-400 mx-2">/</span>
            <a href="../components.html" class="text-gray-500 hover:text-indigo-600 mr-2">组件</a>
            <span class="text-gray-400 mx-2">/</span>
            <a href="./index.html" class="text-gray-500 hover:text-indigo-600 mr-2">基础组件</a>
            <span class="text-gray-400 mx-2">/</span>
            <span class="text-gray-900 font-medium">图标按钮</span>
        </div>

        <div class="mb-12">
            <h2 class="text-3xl font-bold mb-4 text-gray-900">图标按钮</h2>
            <p class="text-lg text-gray-600 max-w-3xl">
                图标按钮是按钮的一种特殊形式，以图标为主要视觉元素，可用于各种交互场景，特别适合在空间有限的区域使用。
            </p>
        </div>

        <!-- 基础图标按钮 -->
        <section class="mb-16">
            <h3 class="text-xl font-bold mb-6 pb-2 border-b border-gray-200">基础图标按钮</h3>
            
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <h4 class="text-lg font-semibold mb-4">仅图标按钮</h4>
                <p class="text-gray-600 mb-6">这种按钮只包含图标，没有文本，适合用于工具栏、操作区等空间受限的位置：</p>
                
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div class="border border-gray-200 rounded-lg p-4 text-center">
                        <div class="flex justify-center mb-3">
                            <button class="w-10 h-10 flex items-center justify-center rounded-md text-indigo-600 bg-indigo-100 hover:bg-indigo-200 action-button">
                                <i class="ri-add-line text-lg"></i>
                            </button>
                        </div>
                        <p class="font-medium">添加</p>
                        <p class="text-sm text-gray-500 mt-1">用于新增内容</p>
                    </div>
                    
                    <div class="border border-gray-200 rounded-lg p-4 text-center">
                        <div class="flex justify-center mb-3">
                            <button class="w-10 h-10 flex items-center justify-center rounded-md text-blue-600 bg-blue-100 hover:bg-blue-200 action-button">
                                <i class="ri-search-line text-lg"></i>
                            </button>
                        </div>
                        <p class="font-medium">搜索</p>
                        <p class="text-sm text-gray-500 mt-1">用于查找内容</p>
                    </div>
                    
                    <div class="border border-gray-200 rounded-lg p-4 text-center">
                        <div class="flex justify-center mb-3">
                            <button class="w-10 h-10 flex items-center justify-center rounded-md text-green-600 bg-green-100 hover:bg-green-200 action-button">
                                <i class="ri-refresh-line text-lg"></i>
                            </button>
                        </div>
                        <p class="font-medium">刷新</p>
                        <p class="text-sm text-gray-500 mt-1">用于更新内容</p>
                    </div>
                    
                    <div class="border border-gray-200 rounded-lg p-4 text-center">
                        <div class="flex justify-center mb-3">
                            <button class="w-10 h-10 flex items-center justify-center rounded-md text-red-600 bg-red-100 hover:bg-red-200 action-button">
                                <i class="ri-delete-bin-line text-lg"></i>
                            </button>
                        </div>
                        <p class="font-medium">删除</p>
                        <p class="text-sm text-gray-500 mt-1">用于移除内容</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <h4 class="text-lg font-semibold mb-4">带文本的图标按钮</h4>
                <p class="text-gray-600 mb-6">这种按钮同时包含图标和文本，提供更好的可读性和交互提示：</p>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h5 class="font-medium text-gray-900 mb-4">前置图标</h5>
                        <div class="flex flex-wrap gap-3">
                            <button class="inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors">
                                <i class="ri-add-line mr-2"></i>
                                添加
                            </button>
                            <button class="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors">
                                <i class="ri-download-line mr-2"></i>
                                下载
                            </button>
                            <button class="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors">
                                <i class="ri-delete-bin-line mr-2"></i>
                                删除
                            </button>
                        </div>
                    </div>
                    
                    <div>
                        <h5 class="font-medium text-gray-900 mb-4">后置图标</h5>
                        <div class="flex flex-wrap gap-3">
                            <button class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                                下一步
                                <i class="ri-arrow-right-line ml-2"></i>
                            </button>
                            <button class="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors">
                                更多
                                <i class="ri-more-line ml-2"></i>
                            </button>
                            <button class="inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-gray-700 rounded-md hover:bg-gray-50 transition-colors">
                                返回
                                <i class="ri-arrow-left-line ml-2"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- 图标按钮变体 -->
        <section class="mb-16">
            <h3 class="text-xl font-bold mb-6 pb-2 border-b border-gray-200">图标按钮变体</h3>
            
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <h4 class="text-lg font-semibold mb-4">不同形状</h4>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="border border-gray-200 rounded-lg p-4 text-center">
                        <h5 class="font-medium text-gray-900 mb-4">圆角矩形</h5>
                        <div class="flex justify-center gap-3">
                            <button class="w-10 h-10 flex items-center justify-center rounded-md bg-indigo-600 text-white hover:bg-indigo-700 action-button">
                                <i class="ri-add-line"></i>
                            </button>
                            <button class="w-10 h-10 flex items-center justify-center rounded-md bg-blue-600 text-white hover:bg-blue-700 action-button">
                                <i class="ri-search-line"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="border border-gray-200 rounded-lg p-4 text-center">
                        <h5 class="font-medium text-gray-900 mb-4">圆形</h5>
                        <div class="flex justify-center gap-3">
                            <button class="w-10 h-10 flex items-center justify-center rounded-full bg-indigo-600 text-white hover:bg-indigo-700 action-button">
                                <i class="ri-add-line"></i>
                            </button>
                            <button class="w-10 h-10 flex items-center justify-center rounded-full bg-blue-600 text-white hover:bg-blue-700 action-button">
                                <i class="ri-search-line"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="border border-gray-200 rounded-lg p-4 text-center">
                        <h5 class="font-medium text-gray-900 mb-4">轮廓</h5>
                        <div class="flex justify-center gap-3">
                            <button class="w-10 h-10 flex items-center justify-center rounded-md border border-indigo-600 text-indigo-600 hover:bg-indigo-50 action-button">
                                <i class="ri-add-line"></i>
                            </button>
                            <button class="w-10 h-10 flex items-center justify-center rounded-full border border-blue-600 text-blue-600 hover:bg-blue-50 action-button">
                                <i class="ri-search-line"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <h4 class="text-lg font-semibold mb-4">不同尺寸</h4>
                
                <div class="flex flex-wrap items-center gap-6">
                    <div class="text-center">
                        <button class="w-6 h-6 flex items-center justify-center rounded-md bg-indigo-600 text-white hover:bg-indigo-700 action-button">
                            <i class="ri-add-line text-xs"></i>
                        </button>
                        <p class="text-xs text-gray-500 mt-2">小型</p>
                    </div>
                    
                    <div class="text-center">
                        <button class="w-8 h-8 flex items-center justify-center rounded-md bg-indigo-600 text-white hover:bg-indigo-700 action-button">
                            <i class="ri-add-line"></i>
                        </button>
                        <p class="text-xs text-gray-500 mt-2">中型</p>
                    </div>
                    
                    <div class="text-center">
                        <button class="w-10 h-10 flex items-center justify-center rounded-md bg-indigo-600 text-white hover:bg-indigo-700 action-button">
                            <i class="ri-add-line text-lg"></i>
                        </button>
                        <p class="text-xs text-gray-500 mt-2">大型</p>
                    </div>
                    
                    <div class="text-center">
                        <button class="w-12 h-12 flex items-center justify-center rounded-md bg-indigo-600 text-white hover:bg-indigo-700 action-button">
                            <i class="ri-add-line text-xl"></i>
                        </button>
                        <p class="text-xs text-gray-500 mt-2">超大</p>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- 使用场景 -->
        <section class="mb-16">
            <h3 class="text-xl font-bold mb-6 pb-2 border-b border-gray-200">使用场景</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h4 class="text-lg font-semibold mb-4">工具栏</h4>
                    
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-4">
                            <h5 class="font-medium">文档编辑器</h5>
                            <div class="flex gap-2">
                                <button class="w-8 h-8 flex items-center justify-center rounded-md text-gray-600 hover:bg-gray-100 action-button">
                                    <i class="ri-bold"></i>
                                </button>
                                <button class="w-8 h-8 flex items-center justify-center rounded-md text-gray-600 hover:bg-gray-100 action-button">
                                    <i class="ri-italic"></i>
                                </button>
                                <button class="w-8 h-8 flex items-center justify-center rounded-md text-gray-600 hover:bg-gray-100 action-button">
                                    <i class="ri-underline"></i>
                                </button>
                                <button class="w-8 h-8 flex items-center justify-center rounded-md text-gray-600 hover:bg-gray-100 action-button">
                                    <i class="ri-link"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="border border-gray-200 rounded bg-gray-50 p-3 h-32 flex items-center justify-center text-gray-400">
                            编辑器内容区域
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h4 class="text-lg font-semibold mb-4">数据表格操作列</h4>
                    
                    <div class="border border-gray-200 rounded-lg overflow-hidden">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">项目</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">状态</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">数据分析报告</td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">已完成</span>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <div class="flex space-x-2">
                                            <button class="w-8 h-8 flex items-center justify-center rounded-full text-blue-600 hover:bg-blue-100 action-button">
                                                <i class="ri-eye-line"></i>
                                            </button>
                                            <button class="w-8 h-8 flex items-center justify-center rounded-full text-green-600 hover:bg-green-100 action-button">
                                                <i class="ri-edit-line"></i>
                                            </button>
                                            <button class="w-8 h-8 flex items-center justify-center rounded-full text-red-600 hover:bg-red-100 action-button">
                                                <i class="ri-delete-bin-line"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- 设计规范 -->
        <section class="mb-16">
            <h3 class="text-xl font-bold mb-6 pb-2 border-b border-gray-200">设计规范</h3>
            
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div>
                        <h4 class="text-lg font-semibold mb-4">图标按钮设计原则</h4>
                        <ul class="list-disc pl-5 space-y-2 text-gray-600">
                            <li>使用直观易懂的图标，确保用户能够理解其含义</li>
                            <li>对于不太明显的图标，可以添加文本说明或工具提示</li>
                            <li>保持一致的间距和对齐方式</li>
                            <li>图标大小应该适中，既不会过大占用太多空间，也不会太小难以点击</li>
                            <li>触摸区域应足够大（建议至少40x40px），便于移动设备操作</li>
                        </ul>
                    </div>
                    
                    <div>
                        <h4 class="text-lg font-semibold mb-4">常用图标颜色规范</h4>
                        <div class="space-y-2">
                            <div class="flex items-center">
                                <div class="w-6 h-6 rounded bg-blue-600 mr-3"></div>
                                <span>蓝色 - 信息类操作（查看、信息）</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-6 h-6 rounded bg-green-600 mr-3"></div>
                                <span>绿色 - 正向操作（确认、编辑、下载）</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-6 h-6 rounded bg-yellow-500 mr-3"></div>
                                <span>黄色 - 警告类操作（提醒、标记）</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-6 h-6 rounded bg-red-600 mr-3"></div>
                                <span>红色 - 危险操作（删除、停用）</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-6 h-6 rounded bg-gray-600 mr-3"></div>
                                <span>灰色 - 次要操作（取消、关闭）</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- 代码示例 -->
        <section class="mb-12">
            <h3 class="text-xl font-bold mb-6 pb-2 border-b border-gray-200">代码示例</h3>
            
            <div class="bg-white rounded-lg shadow-md p-6">
                <h4 class="text-lg font-semibold mb-4">Vue 3 实现</h4>
                
                <div class="bg-gray-50 rounded-lg p-4 mb-6 overflow-auto">
                    <pre class="text-sm text-gray-800">
&lt;template&gt;
  &lt;!-- 仅图标按钮 --&gt;
  &lt;button 
    class="w-10 h-10 flex items-center justify-center rounded-md bg-indigo-600 text-white hover:bg-indigo-700 transition-all focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
    @click="handleClick"
    :title="tooltip"
    :disabled="disabled"
  &gt;
    &lt;i :class="`ri-${icon}`&gt;&lt;/i&gt;
  &lt;/button&gt;
  
  &lt;!-- 带文本的图标按钮 --&gt;
  &lt;button 
    class="inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
    @click="handleClick"
    :disabled="disabled"
  &gt;
    &lt;i :class="`ri-${icon} ${iconPosition === 'left' ? 'mr-2' : 'ml-2'}`"&gt;&lt;/i&gt;
    &lt;slot&gt;&lt;/slot&gt;
  &lt;/button&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { defineProps, defineEmits } from 'vue';

const props = defineProps({
  icon: {
    type: String,
    required: true
  },
  iconPosition: {
    type: String,
    default: 'left',
    validator: (value) => ['left', 'right'].includes(value)
  },
  tooltip: {
    type: String,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['click']);

const handleClick = (event) => {
  emit('click', event);
};
&lt;/script&gt;
                    </pre>
                </div>
            </div>
        </section>

        <div class="text-center mb-12">
            <a href="./buttons.html" class="inline-flex items-center px-5 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                查看按钮组件
            </a>
            <a href="../display/action-buttons.html" class="inline-flex items-center px-5 py-2 ml-4 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                查看操作按钮组件
            </a>
        </div>
    </div>
    
    <footer class="bg-white border-t border-gray-200 py-6 mt-12">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="mb-4 md:mb-0">
                    <p class="text-gray-500">© 2023 DataScope UI 组件库</p>
                </div>
                <div class="flex space-x-4">
                    <a href="../guides/changelog.html" class="text-gray-500 hover:text-indigo-600">
                        更新日志
                    </a>
                    <a href="../sitemap.html" class="text-gray-500 hover:text-indigo-600">
                        网站地图
                    </a>
                    <a href="https://github.com/yourusername/datascope-ui" target="_blank" class="text-gray-500 hover:text-indigo-600">
                        <i class="ri-github-fill text-xl"></i>
                    </a>
                </div>
            </div>
        </div>
    </footer>
</body>
</html>