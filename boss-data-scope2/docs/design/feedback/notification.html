<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通知提醒 - DataScope UI</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        .notification-wrapper {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        
        .notification {
            max-width: 350px;
            min-width: 280px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 16px;
            animation: slideInRight 0.3s ease;
        }
        
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        
        .demo-trigger {
            transition: all 0.3s;
        }
        
        .demo-trigger:hover {
            transform: translateY(-2px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
        }
    </style>
</head>
<body class="bg-gray-50">
    <header class="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="../index.html" class="flex items-center">
                        <h1 class="text-xl font-bold text-gray-900">DataScope UI</h1>
                    </a>
                </div>
                <nav class="flex items-center space-x-4">
                    <a href="../index.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">首页</a>
                    <a href="../components.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">组件</a>
                    <a href="../guides/index.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">使用指引</a>
                    <a href="../sitemap.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">网站地图</a>
                </nav>
            </div>
        </div>
    </header>

    <div class="flex min-h-screen">
        <!-- 左侧导航菜单 -->
        <div class="w-64 bg-white border-r border-gray-200 overflow-y-auto">
            <div class="p-4">
                <h3 class="text-lg font-semibold text-gray-900 mb-3">组件分类</h3>
                
                <div class="space-y-4">
                    <!-- 基础组件 -->
                    <div>
                        <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                            <i class="ri-layout-2-line mr-2 text-blue-600"></i>
                            <span>基础组件</span>
                        </h4>
                        <ul class="ml-6 space-y-1">
                            <li>
                                <a href="../basic/buttons.html" class="block text-gray-700 hover:text-indigo-600 py-1">按钮</a>
                            </li>
                            <li>
                                <a href="../basic/icon-buttons.html" class="block text-gray-700 hover:text-indigo-600 py-1">图标按钮</a>
                            </li>
                            <li>
                                <a href="../basic/inputs.html" class="block text-gray-700 hover:text-indigo-600 py-1">输入框</a>
                            </li>
                            <li>
                                <a href="../basic/badges.html" class="block text-gray-700 hover:text-indigo-600 py-1">状态标签</a>
                            </li>
                            <li>
                                <a href="../basic/index.html" class="block text-gray-700 hover:text-indigo-600 py-1">全部基础组件</a>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- 表单组件 -->
                    <div>
                        <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                            <i class="ri-file-list-3-line mr-2 text-green-600"></i>
                            <span>表单组件</span>
                        </h4>
                        <ul class="ml-6 space-y-1">
                            <li>
                                <a href="../forms/basic-inputs.html" class="block text-gray-700 hover:text-indigo-600 py-1">基础输入</a>
                            </li>
                            <li>
                                <a href="../forms/selectors.html" class="block text-gray-700 hover:text-indigo-600 py-1">选择器</a>
                            </li>
                            <li>
                                <a href="../forms/toggles.html" class="block text-gray-700 hover:text-indigo-600 py-1">开关控件</a>
                            </li>
                            <li>
                                <a href="../forms/date-picker.html" class="block text-gray-700 hover:text-indigo-600 py-1">日期选择器</a>
                            </li>
                            <li>
                                <a href="../forms/index.html" class="block text-gray-700 hover:text-indigo-600 py-1">全部表单组件</a>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- 容器组件 -->
                    <div>
                        <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                            <i class="ri-layout-masonry-line mr-2 text-purple-600"></i>
                            <span>容器组件</span>
                        </h4>
                        <ul class="ml-6 space-y-1">
                            <li>
                                <a href="../container/cards.html" class="block text-gray-700 hover:text-indigo-600 py-1">卡片容器</a>
                            </li>
                            <li>
                                <a href="../container/panels.html" class="block text-gray-700 hover:text-indigo-600 py-1">面板容器</a>
                            </li>
                            <li>
                                <a href="../container/boxes.html" class="block text-gray-700 hover:text-indigo-600 py-1">盒子容器</a>
                            </li>
                            <li>
                                <a href="../container/index.html" class="block text-gray-700 hover:text-indigo-600 py-1">全部容器组件</a>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- 数据展示 -->
                    <div>
                        <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                            <i class="ri-table-line mr-2 text-yellow-600"></i>
                            <span>数据展示</span>
                        </h4>
                        <ul class="ml-6 space-y-1">
                            <li>
                                <a href="../display/data-table.html" class="block text-gray-700 hover:text-indigo-600 py-1">数据表格</a>
                            </li>
                            <li>
                                <a href="../display/action-buttons.html" class="block text-gray-700 hover:text-indigo-600 py-1">操作按钮</a>
                            </li>
                            <li>
                                <a href="../display/page-header.html" class="block text-gray-700 hover:text-indigo-600 py-1">页面标题</a>
                            </li>
                            <li>
                                <a href="../display/index.html" class="block text-gray-700 hover:text-indigo-600 py-1">全部数据展示</a>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- 反馈组件 -->
                    <div>
                        <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                            <i class="ri-notification-3-line mr-2 text-indigo-600"></i>
                            <span>反馈组件</span>
                        </h4>
                        <ul class="ml-6 space-y-1">
                            <li>
                                <a href="./message.html" class="block text-gray-700 hover:text-indigo-600 py-1">消息提示</a>
                            </li>
                            <li>
                                <a href="./notification.html" class="block text-gray-700 hover:text-indigo-600 py-1 font-medium text-indigo-600">通知提醒</a>
                            </li>
                            <li>
                                <a href="./dialog.html" class="block text-gray-700 hover:text-indigo-600 py-1">对话框</a>
                            </li>
                            <li>
                                <a href="./alert.html" class="block text-gray-700 hover:text-indigo-600 py-1">警告提示</a>
                            </li>
                            <li>
                                <a href="./index.html" class="block text-gray-700 hover:text-indigo-600 py-1">全部反馈组件</a>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- 动画效果 -->
                    <div>
                        <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                            <i class="ri-slideshow-3-line mr-2 text-pink-600"></i>
                            <span>动画效果</span>
                        </h4>
                        <ul class="ml-6 space-y-1">
                            <li>
                                <a href="../animations/transitions.html" class="block text-gray-700 hover:text-indigo-600 py-1">过渡动画</a>
                            </li>
                            <li>
                                <a href="../animations/loading.html" class="block text-gray-700 hover:text-indigo-600 py-1">加载动画</a>
                            </li>
                            <li>
                                <a href="../animations/index.html" class="block text-gray-700 hover:text-indigo-600 py-1">全部动画效果</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 右侧内容区 -->
        <div class="flex-1 p-6 overflow-y-auto">
            <div class="container mx-auto px-4 py-8">
        <!-- 导航面包屑 -->
        <div class="mb-8">
            <div class="flex items-center mb-3">
                <a href="../index.html" class="text-gray-500 hover:text-indigo-600 mr-2">
                    <i class="ri-home-line"></i>
                </a>
                <span class="text-gray-400 mx-2">/</span>
                <a href="../components.html" class="text-gray-500 hover:text-indigo-600">组件</a>
                <span class="text-gray-400 mx-2">/</span>
                <a href="./index.html" class="text-gray-500 hover:text-indigo-600">反馈组件</a>
                <span class="text-gray-400 mx-2">/</span>
                <span class="text-gray-800">通知提醒</span>
            </div>
            
            <!-- 当前位置指示器 -->
            <nav class="flex bg-gray-50 text-gray-700 border border-gray-200 py-3 px-5 rounded-lg mb-6" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <i class="ri-notification-3-line text-indigo-600 mr-2"></i>
                        <span class="text-gray-500">反馈组件</span>
                        <i class="ri-arrow-right-s-line mx-2 text-gray-400"></i>
                    </li>
                    <li aria-current="page">
                        <div class="flex items-center">
                            <span class="text-gray-800 font-medium">通知提醒 Notification</span>
                        </div>
                    </li>
                </ol>
            </nav>

        <div class="mb-8">
            <h2 class="text-3xl font-bold mb-4 text-gray-900">通知提醒</h2>
            <p class="text-lg text-gray-600 max-w-4xl">
                全局展示通知提醒信息，在页面右上角展示，可以带有标题和内容，适合系统级通知或重要信息提示。
            </p>
        </div>

        <!-- 基础用法 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h3 class="text-xl font-bold mb-4">基础用法</h3>
            <p class="text-gray-600 mb-6">
                最简单的通知提醒用法，包含标题和内容，支持自动关闭和手动关闭。
            </p>
            
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                <div class="demo-trigger bg-white border border-gray-200 rounded-md p-4 flex flex-col items-center">
                    <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mb-3">
                        <i class="ri-information-line text-blue-600"></i>
                    </div>
                    <button class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition">
                        信息通知
                    </button>
                </div>
                
                <div class="demo-trigger bg-white border border-gray-200 rounded-md p-4 flex flex-col items-center">
                    <div class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mb-3">
                        <i class="ri-check-line text-green-600"></i>
                    </div>
                    <button class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition">
                        成功通知
                    </button>
                </div>
                
                <div class="demo-trigger bg-white border border-gray-200 rounded-md p-4 flex flex-col items-center">
                    <div class="w-10 h-10 rounded-full bg-yellow-100 flex items-center justify-center mb-3">
                        <i class="ri-alert-line text-yellow-600"></i>
                    </div>
                    <button class="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600 transition">
                        警告通知
                    </button>
                </div>
                
                <div class="demo-trigger bg-white border border-gray-200 rounded-md p-4 flex flex-col items-center">
                    <div class="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center mb-3">
                        <i class="ri-close-circle-line text-red-600"></i>
                    </div>
                    <button class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition">
                        错误通知
                    </button>
                </div>
            </div>
            
            <div class="bg-gray-50 p-6 rounded-lg border border-gray-200 mb-4">
                <div class="notification-wrapper">
                    <div class="notification bg-white border-l-4 border-blue-500">
                        <div class="px-4 py-3 border-b border-gray-100">
                            <div class="flex justify-between items-center">
                                <h4 class="text-sm font-medium text-gray-900 flex items-center">
                                    <i class="ri-information-line text-blue-500 mr-2"></i>
                                    通知提醒
                                </h4>
                                <button class="text-gray-400 hover:text-gray-500">
                                    <i class="ri-close-line"></i>
                                </button>
                            </div>
                        </div>
                        <div class="px-4 py-3">
                            <p class="text-sm text-gray-600">这是一条重要的系统通知，请注意查看。</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="bg-gray-100 p-4 rounded">
                <pre class="text-sm text-gray-800 overflow-x-auto">
// Vue 3 示例代码
import { Notification } from 'datascope-ui'

// 基础用法
Notification.info({
  title: '通知提醒',
  content: '这是一条重要的系统通知，请注意查看。',
  duration: 4.5,  // 自动关闭的延时，单位秒，设为 0 则不自动关闭
})

// 其他类型
Notification.success({
  title: '成功提示',
  content: '数据保存成功！',
})

Notification.warning({
  title: '警告提示',
  content: '服务器负载过高，请稍后再试。',
})

Notification.error({
  title: '错误提示',
  content: '操作失败，请检查网络连接。',
})
                </pre>
            </div>
        </div>
        
        <!-- 高级用法 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h3 class="text-xl font-bold mb-4">高级用法</h3>
            <p class="text-gray-600 mb-6">
                通知提醒支持自定义图标、关闭按钮、持续时间和点击回调等功能。
            </p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-white border border-gray-200 rounded-md p-4">
                    <h4 class="font-medium mb-3">带操作的通知</h4>
                    <div class="mb-4">
                        <div class="notification bg-white border-l-4 border-indigo-500">
                            <div class="px-4 py-3 border-b border-gray-100">
                                <div class="flex justify-between items-center">
                                    <h4 class="text-sm font-medium text-gray-900 flex items-center">
                                        <i class="ri-mail-line text-indigo-500 mr-2"></i>
                                        新消息
                                    </h4>
                                    <button class="text-gray-400 hover:text-gray-500">
                                        <i class="ri-close-line"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="px-4 py-3">
                                <p class="text-sm text-gray-600 mb-2">您收到一条新的消息，请及时查看。</p>
                                <div class="flex space-x-2">
                                    <button class="px-3 py-1 text-xs bg-indigo-600 text-white rounded hover:bg-indigo-700">
                                        查看详情
                                    </button>
                                    <button class="px-3 py-1 text-xs border border-gray-300 text-gray-600 rounded hover:bg-gray-50">
                                        稍后再看
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <button class="px-4 py-2 bg-indigo-500 text-white rounded hover:bg-indigo-600 transition">
                        显示带操作的通知
                    </button>
                </div>
                
                <div class="bg-white border border-gray-200 rounded-md p-4">
                    <h4 class="font-medium mb-3">自定义图标和位置</h4>
                    <div class="mb-4">
                        <div class="notification bg-white border-l-4 border-purple-500">
                            <div class="px-4 py-3 border-b border-gray-100">
                                <div class="flex justify-between items-center">
                                    <h4 class="text-sm font-medium text-gray-900 flex items-center">
                                        <i class="ri-rocket-line text-purple-500 mr-2"></i>
                                        新功能上线
                                    </h4>
                                    <button class="text-gray-400 hover:text-gray-500">
                                        <i class="ri-close-line"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="px-4 py-3">
                                <p class="text-sm text-gray-600">平台新增数据分析功能，立即体验全新的数据可视化！</p>
                            </div>
                        </div>
                    </div>
                    <button class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 transition">
                        自定义通知样式
                    </button>
                </div>
            </div>
            
            <div class="bg-gray-100 p-4 rounded">
                <pre class="text-sm text-gray-800 overflow-x-auto">
// 带操作按钮的通知
Notification.open({
  title: '新消息',
  content: '您收到一条新的消息，请及时查看。',
  icon: 'mail',
  btn: h => [
    h('button', {
      class: 'px-3 py-1 text-xs bg-indigo-600 text-white rounded hover:bg-indigo-700 mr-2',
      onClick: () => {
        console.log('点击查看详情')
      }
    }, '查看详情'),
    h('button', {
      class: 'px-3 py-1 text-xs border border-gray-300 text-gray-600 rounded hover:bg-gray-50',
      onClick: () => {
        console.log('点击稍后再看')
      }
    }, '稍后再看')
  ],
  duration: 0, // 不自动关闭
})

// 自定义图标和位置
Notification.open({
  title: '新功能上线',
  content: '平台新增数据分析功能，立即体验全新的数据可视化！',
  icon: 'rocket',
  iconColor: '#805ad5',
  placement: 'topLeft',  // 位置选项: topRight, topLeft, bottomRight, bottomLeft
  className: 'custom-notification',
  style: { borderLeft: '4px solid #805ad5' },
})
                </pre>
            </div>
        </div>
    
        <!-- API参考 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h3 class="text-xl font-bold mb-4">API参考</h3>
            
            <div class="mb-6">
                <h4 class="font-medium mb-3">Notification方法</h4>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead>
                            <tr>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">方法名</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">说明</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">参数</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900">Notification.open(config)</td>
                                <td class="px-4 py-3 text-sm text-gray-500">打开通知提醒</td>
                                <td class="px-4 py-3 text-sm text-gray-500">config: 配置对象</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900">Notification.info(config)</td>
                                <td class="px-4 py-3 text-sm text-gray-500">打开信息通知</td>
                                <td class="px-4 py-3 text-sm text-gray-500">config: 配置对象</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900">Notification.success(config)</td>
                                <td class="px-4 py-3 text-sm text-gray-500">打开成功通知</td>
                                <td class="px-4 py-3 text-sm text-gray-500">config: 配置对象</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900">Notification.warning(config)</td>
                                <td class="px-4 py-3 text-sm text-gray-500">打开警告通知</td>
                                <td class="px-4 py-3 text-sm text-gray-500">config: 配置对象</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900">Notification.error(config)</td>
                                <td class="px-4 py-3 text-sm text-gray-500">打开错误通知</td>
                                <td class="px-4 py-3 text-sm text-gray-500">config: 配置对象</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900">Notification.close(key)</td>
                                <td class="px-4 py-3 text-sm text-gray-500">关闭指定的通知</td>
                                <td class="px-4 py-3 text-sm text-gray-500">key: 通知的唯一标识</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900">Notification.closeAll()</td>
                                <td class="px-4 py-3 text-sm text-gray-500">关闭所有通知</td>
                                <td class="px-4 py-3 text-sm text-gray-500">-</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <div>
                <h4 class="font-medium mb-3">配置参数</h4>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead>
                            <tr>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">参数</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">说明</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">类型</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">默认值</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900">title</td>
                                <td class="px-4 py-3 text-sm text-gray-500">通知标题</td>
                                <td class="px-4 py-3 text-sm text-gray-500">string</td>
                                <td class="px-4 py-3 text-sm text-gray-500">-</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900">content</td>
                                <td class="px-4 py-3 text-sm text-gray-500">通知内容</td>
                                <td class="px-4 py-3 text-sm text-gray-500">string</td>
                                <td class="px-4 py-3 text-sm text-gray-500">-</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900">key</td>
                                <td class="px-4 py-3 text-sm text-gray-500">通知的唯一标识</td>
                                <td class="px-4 py-3 text-sm text-gray-500">string</td>
                                <td class="px-4 py-3 text-sm text-gray-500">自动生成</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900">duration</td>
                                <td class="px-4 py-3 text-sm text-gray-500">自动关闭的延时，单位秒，设为 0 不自动关闭</td>
                                <td class="px-4 py-3 text-sm text-gray-500">number</td>
                                <td class="px-4 py-3 text-sm text-gray-500">4.5</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900">icon</td>
                                <td class="px-4 py-3 text-sm text-gray-500">自定义图标</td>
                                <td class="px-4 py-3 text-sm text-gray-500">string | VNode</td>
                                <td class="px-4 py-3 text-sm text-gray-500">-</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900">iconColor</td>
                                <td class="px-4 py-3 text-sm text-gray-500">图标颜色</td>
                                <td class="px-4 py-3 text-sm text-gray-500">string</td>
                                <td class="px-4 py-3 text-sm text-gray-500">-</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900">placement</td>
                                <td class="px-4 py-3 text-sm text-gray-500">通知出现的位置</td>
                                <td class="px-4 py-3 text-sm text-gray-500">string</td>
                                <td class="px-4 py-3 text-sm text-gray-500">topRight</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900">btn</td>
                                <td class="px-4 py-3 text-sm text-gray-500">自定义操作按钮</td>
                                <td class="px-4 py-3 text-sm text-gray-500">VNode | ((h) => VNode[])</td>
                                <td class="px-4 py-3 text-sm text-gray-500">-</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900">onClose</td>
                                <td class="px-4 py-3 text-sm text-gray-500">通知关闭时回调</td>
                                <td class="px-4 py-3 text-sm text-gray-500">() => void</td>
                                <td class="px-4 py-3 text-sm text-gray-500">-</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900">onClick</td>
                                <td class="px-4 py-3 text-sm text-gray-500">点击通知时回调</td>
                                <td class="px-4 py-3 text-sm text-gray-500">() => void</td>
                                <td class="px-4 py-3 text-sm text-gray-500">-</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900">className</td>
                                <td class="px-4 py-3 text-sm text-gray-500">自定义 CSS 类名</td>
                                <td class="px-4 py-3 text-sm text-gray-500">string</td>
                                <td class="px-4 py-3 text-sm text-gray-500">-</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900">style</td>
                                <td class="px-4 py-3 text-sm text-gray-500">自定义内联样式</td>
                                <td class="px-4 py-3 text-sm text-gray-500">CSSProperties</td>
                                <td class="px-4 py-3 text-sm text-gray-500">-</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="text-center mb-12">
            <a href="./index.html" class="inline-flex items-center px-5 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <i class="ri-arrow-left-line mr-1"></i> 返回反馈组件列表
            </a>
        </div>
    </div>
        </div>
    </div>
    
    <footer class="bg-white border-t border-gray-200 py-6 mt-12">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="mb-4 md:mb-0">
                    <p class="text-gray-500">© 2023 DataScope UI 组件库</p>
                </div>
                <div class="flex space-x-4">
                    <a href="../guides/changelog.html" class="text-gray-500 hover:text-indigo-600">
                        更新日志
                    </a>
                    <a href="../sitemap.html" class="text-gray-500 hover:text-indigo-600">
                        网站地图
                    </a>
                    <a href="https://github.com/yourusername/datascope-ui" target="_blank" class="text-gray-500 hover:text-indigo-600">
                        <i class="ri-github-fill text-xl"></i>
                    </a>
                </div>
            </div>
        </div>
    </footer>
</body>
</html>