<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>警告提示 - DataScope UI</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        .demo-trigger {
            transition: all 0.3s;
        }
        
        .demo-trigger:hover {
            transform: translateY(-2px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
        }
    </style>
</head>
<body class="bg-gray-50">
    <header class="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="../index.html" class="flex items-center">
                        <h1 class="text-xl font-bold text-gray-900">DataScope UI</h1>
                    </a>
                </div>
                <nav class="flex items-center space-x-4">
                    <a href="../index.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">首页</a>
                    <a href="../components.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">组件</a>
                    <a href="../guides/index.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">使用指引</a>
                    <a href="../sitemap.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">网站地图</a>
                </nav>
            </div>
        </div>
    </header>

    <div class="flex min-h-screen">
        <!-- 左侧导航菜单 -->
        <div class="w-64 bg-white border-r border-gray-200 overflow-y-auto">
            <div class="p-4">
                <h3 class="text-lg font-semibold text-gray-900 mb-3">组件分类</h3>
                
                <div class="space-y-4">
                    <!-- 基础组件 -->
                    <div>
                        <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                            <i class="ri-layout-2-line mr-2 text-blue-600"></i>
                            <span>基础组件</span>
                        </h4>
                        <ul class="ml-6 space-y-1">
                            <li>
                                <a href="../basic/buttons.html" class="block text-gray-700 hover:text-indigo-600 py-1">按钮</a>
                            </li>
                            <li>
                                <a href="../basic/icon-buttons.html" class="block text-gray-700 hover:text-indigo-600 py-1">图标按钮</a>
                            </li>
                            <li>
                                <a href="../basic/inputs.html" class="block text-gray-700 hover:text-indigo-600 py-1">输入框</a>
                            </li>
                            <li>
                                <a href="../basic/badges.html" class="block text-gray-700 hover:text-indigo-600 py-1">状态标签</a>
                            </li>
                            <li>
                                <a href="../basic/index.html" class="block text-gray-700 hover:text-indigo-600 py-1">全部基础组件</a>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- 表单组件 -->
                    <div>
                        <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                            <i class="ri-file-list-3-line mr-2 text-green-600"></i>
                            <span>表单组件</span>
                        </h4>
                        <ul class="ml-6 space-y-1">
                            <li>
                                <a href="../forms/basic-inputs.html" class="block text-gray-700 hover:text-indigo-600 py-1">基础输入</a>
                            </li>
                            <li>
                                <a href="../forms/selectors.html" class="block text-gray-700 hover:text-indigo-600 py-1">选择器</a>
                            </li>
                            <li>
                                <a href="../forms/toggles.html" class="block text-gray-700 hover:text-indigo-600 py-1">开关控件</a>
                            </li>
                            <li>
                                <a href="../forms/date-picker.html" class="block text-gray-700 hover:text-indigo-600 py-1">日期选择器</a>
                            </li>
                            <li>
                                <a href="../forms/index.html" class="block text-gray-700 hover:text-indigo-600 py-1">全部表单组件</a>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- 容器组件 -->
                    <div>
                        <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                            <i class="ri-layout-masonry-line mr-2 text-purple-600"></i>
                            <span>容器组件</span>
                        </h4>
                        <ul class="ml-6 space-y-1">
                            <li>
                                <a href="../container/cards.html" class="block text-gray-700 hover:text-indigo-600 py-1">卡片容器</a>
                            </li>
                            <li>
                                <a href="../container/panels.html" class="block text-gray-700 hover:text-indigo-600 py-1">面板容器</a>
                            </li>
                            <li>
                                <a href="../container/boxes.html" class="block text-gray-700 hover:text-indigo-600 py-1">盒子容器</a>
                            </li>
                            <li>
                                <a href="../container/index.html" class="block text-gray-700 hover:text-indigo-600 py-1">全部容器组件</a>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- 数据展示 -->
                    <div>
                        <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                            <i class="ri-table-line mr-2 text-yellow-600"></i>
                            <span>数据展示</span>
                        </h4>
                        <ul class="ml-6 space-y-1">
                            <li>
                                <a href="../display/data-table.html" class="block text-gray-700 hover:text-indigo-600 py-1">数据表格</a>
                            </li>
                            <li>
                                <a href="../display/action-buttons.html" class="block text-gray-700 hover:text-indigo-600 py-1">操作按钮</a>
                            </li>
                            <li>
                                <a href="../display/page-header.html" class="block text-gray-700 hover:text-indigo-600 py-1">页面标题</a>
                            </li>
                            <li>
                                <a href="../display/index.html" class="block text-gray-700 hover:text-indigo-600 py-1">全部数据展示</a>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- 反馈组件 -->
                    <div>
                        <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                            <i class="ri-notification-3-line mr-2 text-indigo-600"></i>
                            <span>反馈组件</span>
                        </h4>
                        <ul class="ml-6 space-y-1">
                            <li>
                                <a href="./message.html" class="block text-gray-700 hover:text-indigo-600 py-1">消息提示</a>
                            </li>
                            <li>
                                <a href="./notification.html" class="block text-gray-700 hover:text-indigo-600 py-1">通知提醒</a>
                            </li>
                            <li>
                                <a href="./dialog.html" class="block text-gray-700 hover:text-indigo-600 py-1">对话框</a>
                            </li>
                            <li>
                                <a href="./alert.html" class="block text-gray-700 hover:text-indigo-600 py-1 font-medium text-indigo-600">警告提示</a>
                            </li>
                            <li>
                                <a href="./index.html" class="block text-gray-700 hover:text-indigo-600 py-1">全部反馈组件</a>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- 动画效果 -->
                    <div>
                        <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                            <i class="ri-slideshow-3-line mr-2 text-pink-600"></i>
                            <span>动画效果</span>
                        </h4>
                        <ul class="ml-6 space-y-1">
                            <li>
                                <a href="../animations/transitions.html" class="block text-gray-700 hover:text-indigo-600 py-1">过渡动画</a>
                            </li>
                            <li>
                                <a href="../animations/loading.html" class="block text-gray-700 hover:text-indigo-600 py-1">加载动画</a>
                            </li>
                            <li>
                                <a href="../animations/index.html" class="block text-gray-700 hover:text-indigo-600 py-1">全部动画效果</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 右侧内容区 -->
        <div class="flex-1 p-6 overflow-y-auto">
            <div class="container mx-auto px-4 py-8">
        <div class="flex items-center mb-8">
            <a href="../index.html" class="text-gray-500 hover:text-indigo-600 mr-2">
                <i class="ri-home-line"></i>
            </a>
            <span class="text-gray-400 mx-2">/</span>
            <a href="../components.html" class="text-gray-500 hover:text-indigo-600 mr-2">
                组件
            </a>
            <span class="text-gray-400 mx-2">/</span>
            <a href="./index.html" class="text-gray-500 hover:text-indigo-600 mr-2">
                反馈组件
            </a>
            <span class="text-gray-400 mx-2">/</span>
            <span class="text-gray-900 font-medium">警告提示</span>
        </div>

        <div class="mb-8">
            <h2 class="text-3xl font-bold mb-4 text-gray-900">警告提示</h2>
            <p class="text-lg text-gray-600 max-w-4xl">
                警告提示，展示页面内的警告信息，可以有效引导用户关注重要信息并采取行动。
            </p>
        </div>

        <!-- 基础用法 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h3 class="text-xl font-bold mb-4">基础用法</h3>
            <p class="text-gray-600 mb-6">
                基础的警告提示，有四种不同类型，分别表示不同的警示程度：信息、成功、警告和错误。
            </p>
            
            <div class="space-y-4 mb-6">
                <div class="bg-blue-50 border-l-4 border-blue-400 p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="ri-information-line text-blue-400"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-blue-700">
                                这是一条信息提示，展示一些参考信息。
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-green-50 border-l-4 border-green-400 p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="ri-check-line text-green-400"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-green-700">
                                这是一条成功提示，表示操作已成功完成。
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="ri-alert-line text-yellow-400"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-yellow-700">
                                这是一条警告提示，提醒用户需要注意的事项。
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-red-50 border-l-4 border-red-400 p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="ri-close-circle-line text-red-400"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-red-700">
                                这是一条错误提示，表示操作出现问题或者存在错误。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="bg-gray-100 p-4 rounded">
                <pre class="text-sm text-gray-800 overflow-x-auto">
// Vue 3 示例代码
import { Alert } from 'datascope-ui'

// 在模板中使用
&lt;template&gt;
  &lt;Alert type="info"&gt;这是一条信息提示，展示一些参考信息。&lt;/Alert&gt;
  &lt;Alert type="success"&gt;这是一条成功提示，表示操作已成功完成。&lt;/Alert&gt;
  &lt;Alert type="warning"&gt;这是一条警告提示，提醒用户需要注意的事项。&lt;/Alert&gt;
  &lt;Alert type="error"&gt;这是一条错误提示，表示操作出现问题或者存在错误。&lt;/Alert&gt;
&lt;/template&gt;
                </pre>
            </div>
        </div>
        
        <!-- 可关闭的警告提示 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h3 class="text-xl font-bold mb-4">可关闭的警告提示</h3>
            <p class="text-gray-600 mb-6">
                可以通过添加关闭按钮让用户手动关闭提示，也可以设置自动关闭。
            </p>
            
            <div class="space-y-4 mb-6">
                <div class="bg-blue-50 border-l-4 border-blue-400 p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="ri-information-line text-blue-400"></i>
                        </div>
                        <div class="ml-3 flex-grow">
                            <p class="text-sm text-blue-700">
                                这是一条可关闭的信息提示。
                            </p>
                        </div>
                        <div class="ml-auto pl-3">
                            <div class="-mx-1.5 -my-1.5">
                                <button class="inline-flex bg-blue-50 rounded-md p-1.5 text-blue-500 hover:bg-blue-100 focus:outline-none">
                                    <i class="ri-close-line"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="ri-alert-line text-yellow-400"></i>
                        </div>
                        <div class="ml-3 flex-grow">
                            <p class="text-sm text-yellow-700">
                                这是一条会在 5 秒后自动关闭的警告提示。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="bg-gray-100 p-4 rounded">
                <pre class="text-sm text-gray-800 overflow-x-auto">
// 可手动关闭的警告提示
&lt;Alert type="info" closable @close="handleClose"&gt;
  这是一条可关闭的信息提示。
&lt;/Alert&gt;

// 自动关闭的警告提示
&lt;Alert type="warning" :autoClose="5"&gt;
  这是一条会在 5 秒后自动关闭的警告提示。
&lt;/Alert&gt;

// 处理关闭事件
const handleClose = () => {
  console.log('警告提示已关闭')
}
                </pre>
            </div>
        </div>
        
        <!-- 带有标题和描述的警告提示 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h3 class="text-xl font-bold mb-4">带有标题和描述的警告提示</h3>
            <p class="text-gray-600 mb-6">
                当内容较长或需要更详细说明时，可以使用带有标题和描述的警告提示。
            </p>
            
            <div class="space-y-4 mb-6">
                <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="ri-alert-line text-yellow-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-yellow-800">系统维护通知</h3>
                            <div class="mt-2 text-sm text-yellow-700">
                                <p>
                                    系统将于2023年5月1日凌晨2:00-4:00进行例行维护，届时所有服务将暂时不可用。请您提前做好相关准备，我们对此带来的不便深表歉意。
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="bg-red-50 border-l-4 border-red-400 p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="ri-close-circle-line text-red-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800">数据导出失败</h3>
                            <div class="mt-2 text-sm text-red-700">
                                <p>
                                    由于以下原因，数据导出失败：
                                </p>
                                <ul class="list-disc pl-5 mt-1 space-y-1">
                                    <li>服务器连接超时</li>
                                    <li>数据量超过单次导出限制 (10MB)</li>
                                    <li>请求参数格式不正确</li>
                                </ul>
                                <p class="mt-1">
                                    请尝试减少数据量或联系系统管理员获取帮助。
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="bg-gray-100 p-4 rounded">
                <pre class="text-sm text-gray-800 overflow-x-auto">
// 带有标题和描述的警告提示
&lt;Alert
  type="warning"
  title="系统维护通知"
  description="系统将于2023年5月1日凌晨2:00-4:00进行例行维护，届时所有服务将暂时不可用。请您提前做好相关准备，我们对此带来的不便深表歉意。"
/&gt;

// 复杂内容可以使用插槽
&lt;Alert
  type="error"
  title="数据导出失败"
&gt;
  &lt;template #description&gt;
    &lt;p&gt;由于以下原因，数据导出失败：&lt;/p&gt;
    &lt;ul class="list-disc pl-5 mt-1 space-y-1"&gt;
      &lt;li&gt;服务器连接超时&lt;/li&gt;
      &lt;li&gt;数据量超过单次导出限制 (10MB)&lt;/li&gt;
      &lt;li&gt;请求参数格式不正确&lt;/li&gt;
    &lt;/ul&gt;
    &lt;p class="mt-1"&gt;请尝试减少数据量或联系系统管理员获取帮助。&lt;/p&gt;
  &lt;/template&gt;
&lt;/Alert&gt;
                </pre>
            </div>
        </div>
        
        <!-- 自定义样式 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h3 class="text-xl font-bold mb-4">自定义样式</h3>
            <p class="text-gray-600 mb-6">
                可以通过自定义样式来满足特定的设计需求。
            </p>
            
            <div class="space-y-4 mb-6">
                <div class="rounded-lg bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200 p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="ri-star-line text-purple-500"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-purple-700">
                                这是一个使用渐变背景的自定义提示。
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="rounded-lg bg-gray-50 border border-gray-200 shadow-sm p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 rounded-full bg-indigo-100 flex items-center justify-center">
                                <i class="ri-question-line text-indigo-500"></i>
                            </div>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-gray-900">需要帮助？</h3>
                            <div class="mt-2 text-sm text-gray-600">
                                <p>如果您对使用有任何疑问，可以查看我们的<a href="#" class="text-indigo-600 hover:text-indigo-500">帮助文档</a>或<a href="#" class="text-indigo-600 hover:text-indigo-500">联系客服</a>。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="bg-gray-100 p-4 rounded">
                <pre class="text-sm text-gray-800 overflow-x-auto">
// 自定义样式的警告提示
&lt;Alert
  class="rounded-lg bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200"
  type="custom"
  icon="star"
  iconClass="text-purple-500"
  messageClass="text-sm text-purple-700"
&gt;
  这是一个使用渐变背景的自定义提示。
&lt;/Alert&gt;

// 完全自定义的警告提示
&lt;Alert
  class="rounded-lg bg-gray-50 border border-gray-200 shadow-sm"
  icon={
    &lt;div class="w-8 h-8 rounded-full bg-indigo-100 flex items-center justify-center"&gt;
      &lt;i class="ri-question-line text-indigo-500"&gt;&lt;/i&gt;
    &lt;/div&gt;
  }
&gt;
  &lt;template #title&gt;
    &lt;h3 class="text-sm font-medium text-gray-900"&gt;需要帮助？&lt;/h3&gt;
  &lt;/template&gt;
  &lt;template #description&gt;
    &lt;p class="text-sm text-gray-600"&gt;
      如果您对使用有任何疑问，可以查看我们的
      &lt;a href="#" class="text-indigo-600 hover:text-indigo-500"&gt;帮助文档&lt;/a&gt;
      或
      &lt;a href="#" class="text-indigo-600 hover:text-indigo-500"&gt;联系客服&lt;/a&gt;。
    &lt;/p&gt;
  &lt;/template&gt;
&lt;/Alert&gt;
                </pre>
            </div>
        </div>
        
        <!-- API参考 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h3 class="text-xl font-bold mb-4">API参考</h3>
            
            <div class="mb-6">
                <h4 class="font-medium mb-3">Alert Props</h4>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead>
                            <tr>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">属性</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">说明</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">类型</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">默认值</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900">type</td>
                                <td class="px-4 py-3 text-sm text-gray-500">警告提示的类型</td>
                                <td class="px-4 py-3 text-sm text-gray-500">info | success | warning | error | custom</td>
                                <td class="px-4 py-3 text-sm text-gray-500">info</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900">closable</td>
                                <td class="px-4 py-3 text-sm text-gray-500">是否显示关闭按钮</td>
                                <td class="px-4 py-3 text-sm text-gray-500">boolean</td>
                                <td class="px-4 py-3 text-sm text-gray-500">false</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900">autoClose</td>
                                <td class="px-4 py-3 text-sm text-gray-500">自动关闭的延时，单位秒，设为 0 不自动关闭</td>
                                <td class="px-4 py-3 text-sm text-gray-500">number</td>
                                <td class="px-4 py-3 text-sm text-gray-500">0</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900">title</td>
                                <td class="px-4 py-3 text-sm text-gray-500">警告提示的标题</td>
                                <td class="px-4 py-3 text-sm text-gray-500">string</td>
                                <td class="px-4 py-3 text-sm text-gray-500">-</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900">description</td>
                                <td class="px-4 py-3 text-sm text-gray-500">警告提示的详细描述</td>
                                <td class="px-4 py-3 text-sm text-gray-500">string</td>
                                <td class="px-4 py-3 text-sm text-gray-500">-</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900">icon</td>
                                <td class="px-4 py-3 text-sm text-gray-500">自定义图标</td>
                                <td class="px-4 py-3 text-sm text-gray-500">string | VNode</td>
                                <td class="px-4 py-3 text-sm text-gray-500">-</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900">iconClass</td>
                                <td class="px-4 py-3 text-sm text-gray-500">图标的自定义类名</td>
                                <td class="px-4 py-3 text-sm text-gray-500">string</td>
                                <td class="px-4 py-3 text-sm text-gray-500">-</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900">messageClass</td>
                                <td class="px-4 py-3 text-sm text-gray-500">消息文本的自定义类名</td>
                                <td class="px-4 py-3 text-sm text-gray-500">string</td>
                                <td class="px-4 py-3 text-sm text-gray-500">-</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900">banner</td>
                                <td class="px-4 py-3 text-sm text-gray-500">是否用作顶部公告</td>
                                <td class="px-4 py-3 text-sm text-gray-500">boolean</td>
                                <td class="px-4 py-3 text-sm text-gray-500">false</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <div>
                <h4 class="font-medium mb-3">Alert 事件</h4>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead>
                            <tr>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">事件名</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">说明</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">回调参数</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900">close</td>
                                <td class="px-4 py-3 text-sm text-gray-500">关闭警告提示时触发</td>
                                <td class="px-4 py-3 text-sm text-gray-500">event: Event</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900">closed</td>
                                <td class="px-4 py-3 text-sm text-gray-500">警告提示关闭动画结束后触发</td>
                                <td class="px-4 py-3 text-sm text-gray-500">-</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="text-center mb-12">
            <a href="./index.html" class="inline-flex items-center px-5 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <i class="ri-arrow-left-line mr-1"></i> 返回反馈组件列表
            </a>
        </div>
    </div>
    
    <footer class="bg-white border-t border-gray-200 py-6 mt-12">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="mb-4 md:mb-0">
                    <p class="text-gray-500">© 2023 DataScope UI 组件库</p>
                </div>
                <div class="flex space-x-4">
                    <a href="../guides/changelog.html" class="text-gray-500 hover:text-indigo-600">
                        更新日志
                    </a>
                    <a href="../sitemap.html" class="text-gray-500 hover:text-indigo-600">
                        网站地图
                    </a>
                    <a href="https://github.com/yourusername/datascope-ui" target="_blank" class="text-gray-500 hover:text-indigo-600">
                        <i class="ri-github-fill text-xl"></i>
                    </a>
                </div>
            </div>
        </div>
    </footer>
</body>
</html>