<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>动画效果 - DataScope UI组件库</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
  <style>
    .component-card {
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      overflow: hidden;
      transition: all 0.2s ease-in-out;
    }
    .component-card:hover {
      box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      transform: translateY(-2px);
    }
    .component-preview {
      padding: 1.5rem;
      background-color: #f8fafc;
      border-bottom: 1px solid #e2e8f0;
      min-height: 150px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .component-info {
      padding: 1rem;
      background-color: white;
    }
    .code-block {
      background-color: #f8fafc;
      border-radius: 6px;
      padding: 16px;
      margin-top: 16px;
      font-family: monospace;
      font-size: 14px;
      white-space: pre-wrap;
      color: #334155;
    }
    
    /* 动画效果 */
    .fade-in {
      animation: fadeIn 0.5s ease-in-out;
    }
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    
    .fade-out {
      animation: fadeOut 0.5s ease-in-out;
    }
    @keyframes fadeOut {
      from { opacity: 1; }
      to { opacity: 0; }
    }
    
    .slide-in {
      animation: slideIn 0.5s ease-in-out;
    }
    @keyframes slideIn {
      from { transform: translateX(-20px); opacity: 0; }
      to { transform: translateX(0); opacity: 1; }
    }
    
    .slide-out {
      animation: slideOut 0.5s ease-in-out;
    }
    @keyframes slideOut {
      from { transform: translateX(0); opacity: 1; }
      to { transform: translateX(20px); opacity: 0; }
    }
    
    .zoom-in {
      animation: zoomIn 0.5s ease-in-out;
    }
    @keyframes zoomIn {
      from { transform: scale(0.8); opacity: 0; }
      to { transform: scale(1); opacity: 1; }
    }
    
    .zoom-out {
      animation: zoomOut 0.5s ease-in-out;
    }
    @keyframes zoomOut {
      from { transform: scale(1); opacity: 1; }
      to { transform: scale(0.8); opacity: 0; }
    }
    
    .bounce-in {
      animation: bounceIn 0.5s cubic-bezier(.17,.67,.83,.67);
    }
    @keyframes bounceIn {
      0% { transform: scale(0.3); opacity: 0; }
      50% { transform: scale(1.05); opacity: 0.8; }
      70% { transform: scale(0.9); }
      100% { transform: scale(1); opacity: 1; }
    }
    
    .spin {
      animation: spin 1s linear infinite;
    }
    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }
    
    .pulse {
      animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    }
    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.6; }
    }
    
    .ripple {
      position: relative;
      overflow: hidden;
    }
    .ripple::after {
      content: "";
      position: absolute;
      width: 100%;
      height: 100%;
      background: rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      transform: scale(0);
      animation: ripple 0.6s linear;
      top: 0;
      left: 0;
    }
    @keyframes ripple {
      to { transform: scale(2.5); opacity: 0; }
    }
    
    .skeleton {
      background: linear-gradient(110deg, #ebebeb 8%, #f5f5f5 18%, #ebebeb 33%);
      background-size: 200% 100%;
      animation: skeleton 1.5s linear infinite;
    }
    @keyframes skeleton {
      to { background-position-x: -200%; }
    }
    
    .shake {
      animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
    }
    @keyframes shake {
      10%, 90% { transform: translate3d(-1px, 0, 0); }
      20%, 80% { transform: translate3d(2px, 0, 0); }
      30%, 50%, 70% { transform: translate3d(-4px, 0, 0); }
      40%, 60% { transform: translate3d(4px, 0, 0); }
    }
    
    .button-demo {
      display: inline-block;
      padding: 0.5rem 1rem;
      background-color: #4f46e5;
      color: white;
      border-radius: 0.25rem;
      font-weight: 500;
      cursor: pointer;
    }
    
    .animation-demo-box {
      width: 100px;
      height: 100px;
      background-color: #4f46e5;
      border-radius: 0.5rem;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: 500;
    }
    
    .skeleton-item {
      height: 20px;
      border-radius: 4px;
    }
    
    /* 动画控制 */
    .animation-control {
      margin-top: 1rem;
      display: flex;
      justify-content: center;
      gap: 0.5rem;
    }
    .animation-control button {
      padding: 0.25rem 0.5rem;
      background-color: #f1f5f9;
      border: 1px solid #e2e8f0;
      border-radius: 0.25rem;
      font-size: 0.875rem;
      cursor: pointer;
    }
    .animation-control button:hover {
      background-color: #e2e8f0;
    }
  </style>
</head>
<body class="bg-gray-50 p-8">
  <div class="container mx-auto max-w-6xl">
    <h1 class="text-3xl font-bold mb-2 text-gray-800">动画效果</h1>
    <p class="text-lg text-gray-600 mb-8">
      动画效果可以提升用户体验，为界面添加生动的视觉反馈，使交互更加流畅自然。
    </p>

    <div class="bg-white p-6 rounded-lg shadow-sm mb-8">
      <h2 class="text-xl font-semibold mb-4">基础动画系统</h2>
      <p class="text-gray-600 mb-6">
        DataScope UI提供了一套基础的动画系统，包括过渡动画、加载动画和交互反馈动画，用于增强用户体验。
      </p>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div>
          <h3 class="text-lg font-medium mb-2 text-gray-800">过渡动画</h3>
          <p class="text-gray-600">用于元素的出现和消失，以及状态变化，使界面变化更加自然流畅。</p>
        </div>
        <div>
          <h3 class="text-lg font-medium mb-2 text-gray-800">加载动画</h3>
          <p class="text-gray-600">在数据加载过程中提供视觉反馈，减少用户等待的焦虑感。</p>
        </div>
        <div>
          <h3 class="text-lg font-medium mb-2 text-gray-800">交互反馈</h3>
          <p class="text-gray-600">为用户操作提供即时的视觉反馈，增强操作的确认感。</p>
        </div>
      </div>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
      <!-- 淡入淡出 -->
      <div class="component-card">
        <div class="component-preview">
          <div>
            <div class="animation-demo-box fade-in" id="fade-demo">淡入效果</div>
            <div class="animation-control">
              <button onclick="document.getElementById('fade-demo').className = 'animation-demo-box fade-in'">淡入</button>
              <button onclick="document.getElementById('fade-demo').className = 'animation-demo-box fade-out'">淡出</button>
            </div>
          </div>
        </div>
        <div class="component-info">
          <h2 class="text-lg font-semibold mb-1">淡入淡出</h2>
          <p class="text-gray-600 text-sm">元素通过透明度变化实现平滑的显示和隐藏，适用于模态框、提示框等。</p>
        </div>
      </div>

      <!-- 滑动 -->
      <div class="component-card">
        <div class="component-preview">
          <div>
            <div class="animation-demo-box slide-in" id="slide-demo">滑动效果</div>
            <div class="animation-control">
              <button onclick="document.getElementById('slide-demo').className = 'animation-demo-box slide-in'">滑入</button>
              <button onclick="document.getElementById('slide-demo').className = 'animation-demo-box slide-out'">滑出</button>
            </div>
          </div>
        </div>
        <div class="component-info">
          <h2 class="text-lg font-semibold mb-1">滑动</h2>
          <p class="text-gray-600 text-sm">元素通过位移实现平滑的进入和退出，适用于侧边栏、下拉菜单等。</p>
        </div>
      </div>

      <!-- 缩放 -->
      <div class="component-card">
        <div class="component-preview">
          <div>
            <div class="animation-demo-box zoom-in" id="zoom-demo">缩放效果</div>
            <div class="animation-control">
              <button onclick="document.getElementById('zoom-demo').className = 'animation-demo-box zoom-in'">放大</button>
              <button onclick="document.getElementById('zoom-demo').className = 'animation-demo-box zoom-out'">缩小</button>
            </div>
          </div>
        </div>
        <div class="component-info">
          <h2 class="text-lg font-semibold mb-1">缩放</h2>
          <p class="text-gray-600 text-sm">元素通过尺寸变化实现平滑的显示和隐藏，适用于弹出框、图片预览等。</p>
        </div>
      </div>

      <!-- 弹跳 -->
      <div class="component-card">
        <div class="component-preview">
          <div>
            <div class="animation-demo-box" id="bounce-demo">弹跳效果</div>
            <div class="animation-control">
              <button onclick="
                document.getElementById('bounce-demo').className = 'animation-demo-box';
                setTimeout(() => {
                  document.getElementById('bounce-demo').className = 'animation-demo-box bounce-in';
                }, 10);
              ">弹跳</button>
            </div>
          </div>
        </div>
        <div class="component-info">
          <h2 class="text-lg font-semibold mb-1">弹跳</h2>
          <p class="text-gray-600 text-sm">元素通过弹性变化引起注意，适用于强调新出现的元素或提醒通知。</p>
        </div>
      </div>

      <!-- 旋转 -->
      <div class="component-card">
        <div class="component-preview">
          <div>
            <div class="animation-demo-box" id="spin-demo">
              <i class="ri-loader-4-line text-3xl spin"></i>
            </div>
          </div>
        </div>
        <div class="component-info">
          <h2 class="text-lg font-semibold mb-1">旋转</h2>
          <p class="text-gray-600 text-sm">元素持续旋转以指示加载状态，适用于数据加载、处理中等状态。</p>
        </div>
      </div>

      <!-- 脉冲 -->
      <div class="component-card">
        <div class="component-preview">
          <div>
            <div class="animation-demo-box pulse" id="pulse-demo">脉冲效果</div>
          </div>
        </div>
        <div class="component-info">
          <h2 class="text-lg font-semibold mb-1">脉冲</h2>
          <p class="text-gray-600 text-sm">元素通过透明度周期性变化引起注意，适用于提示用户关注或等待状态。</p>
        </div>
      </div>

      <!-- 涟漪 -->
      <div class="component-card">
        <div class="component-preview">
          <div>
            <button class="button-demo ripple" id="ripple-demo" onclick="
              this.classList.remove('ripple');
              setTimeout(() => {
                this.classList.add('ripple');
              }, 10);
            ">点击产生涟漪</button>
          </div>
        </div>
        <div class="component-info">
          <h2 class="text-lg font-semibold mb-1">涟漪</h2>
          <p class="text-gray-600 text-sm">点击元素产生向外扩散的波纹效果，提供即时的点击反馈。</p>
        </div>
      </div>

      <!-- 骨架屏 -->
      <div class="component-card">
        <div class="component-preview">
          <div class="w-full max-w-xs">
            <div class="skeleton skeleton-item w-3/4 mb-4"></div>
            <div class="skeleton skeleton-item w-full mb-2"></div>
            <div class="skeleton skeleton-item w-full mb-2"></div>
            <div class="skeleton skeleton-item w-2/3"></div>
          </div>
        </div>
        <div class="component-info">
          <h2 class="text-lg font-semibold mb-1">骨架屏</h2>
          <p class="text-gray-600 text-sm">在内容加载过程中显示的占位符，减少用户等待时的空白感。</p>
        </div>
      </div>

      <!-- 抖动 -->
      <div class="component-card">
        <div class="component-preview">
          <div>
            <div class="animation-demo-box" id="shake-demo">抖动效果</div>
            <div class="animation-control">
              <button onclick="
                document.getElementById('shake-demo').className = 'animation-demo-box';
                setTimeout(() => {
                  document.getElementById('shake-demo').className = 'animation-demo-box shake';
                }, 10);
              ">抖动</button>
            </div>
          </div>
        </div>
        <div class="component-info">
          <h2 class="text-lg font-semibold mb-1">抖动</h2>
          <p class="text-gray-600 text-sm">元素左右快速移动以提示错误或需要注意，常用于表单验证错误提示。</p>
        </div>
      </div>
    </div>

    <div class="bg-white p-6 rounded-lg shadow-sm mb-8">
      <h2 class="text-xl font-semibold mb-4">动画使用指南</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h3 class="text-lg font-medium mb-2 text-gray-800">适用场景</h3>
          <ul class="list-disc pl-6 text-gray-600 space-y-1">
            <li>元素的进入和退出：如模态框、抽屉、弹出菜单等</li>
            <li>状态变化：如按钮点击、开关切换、表单验证等</li>
            <li>数据加载：如页面初始化、分页加载、提交操作等</li>
            <li>用户交互反馈：如点击效果、悬停效果、拖拽操作等</li>
          </ul>
        </div>
        <div>
          <h3 class="text-lg font-medium mb-2 text-gray-800">使用原则</h3>
          <ul class="list-disc pl-6 text-gray-600 space-y-1">
            <li>保持简洁：动画应当简洁明了，不过度复杂</li>
            <li>保持一致：相同操作应使用相同的动画效果</li>
            <li>适当时长：动画时长通常在200-500ms，过长会影响效率</li>
            <li>目的明确：动画应当有明确的目的，而非仅为装饰</li>
            <li>可配置：考虑用户可能需要减少动画或关闭动画</li>
          </ul>
        </div>
      </div>
      
      <div class="mt-8">
        <h3 class="text-lg font-medium mb-2 text-gray-800">代码示例</h3>
        <div class="code-block">
<!-- 动画组件用法示例 -->
<template>
  <!-- 淡入淡出 -->
  <Transition name="fade">
    <div v-if="visible" class="modal">内容</div>
  </Transition>

  <!-- 滑动 -->
  <Transition name="slide">
    <div v-if="showMenu" class="sidebar">菜单内容</div>
  </Transition>

  <!-- 列表动画 -->
  <TransitionGroup name="list" tag="ul">
    <li v-for="item in items" :key="item.id">{{ item.name }}</li>
  </TransitionGroup>

  <!-- 加载动画 -->
  <div v-if="loading" class="loading-wrapper">
    <Spinner size="md" />
  </div>

  <!-- 骨架屏 -->
  <template v-if="loading">
    <Skeleton :rows="3" />
  </template>
  <template v-else>
    <div class="content">实际内容</div>
  </template>
</template>

<style>
/* CSS动画定义 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}
.slide-enter-from,
.slide-leave-to {
  transform: translateX(-100%);
}

.list-enter-active,
.list-leave-active {
  transition: all 0.3s ease;
}
.list-enter-from,
.list-leave-to {
  opacity: 0;
  transform: translateY(30px);
}
</style>
        </div>
      </div>
    </div>
    
    <div class="text-center mt-12 mb-8">
      <a href="../index.html" class="text-blue-600 hover:text-blue-800 font-medium">
        <i class="ri-arrow-left-line mr-1"></i> 返回组件总览
      </a>
    </div>
  </div>
</body>
</html>