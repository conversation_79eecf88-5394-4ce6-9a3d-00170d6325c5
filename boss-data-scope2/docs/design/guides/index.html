<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>使用指南 - DataScope UI</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        .guideline-card {
            transition: all 0.3s ease;
        }
        .guideline-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        .code-block {
            background-color: #f8fafc;
            border-radius: 6px;
            padding: 16px;
            margin: 16px 0;
            font-family: 'Courier New', Courier, monospace;
            font-size: 14px;
            overflow-x: auto;
        }
    </style>
</head>
<body class="bg-gray-50">
    <header class="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="../index.html" class="flex items-center">
                        <h1 class="text-xl font-bold text-gray-900">DataScope UI</h1>
                    </a>
                </div>
                <nav class="flex items-center space-x-4">
                    <a href="../index.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">首页</a>
                    <a href="../components.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">组件</a>
                    <a href="#" class="text-gray-900 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">使用指引</a>
                    <a href="../sitemap.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">网站地图</a>
                    <a href="https://github.com/yourusername/datascope-ui" target="_blank" class="text-gray-500 hover:text-indigo-600">
                        <i class="ri-github-fill text-xl"></i>
                    </a>
                </nav>
            </div>
        </div>
    </header>

    <div class="container mx-auto px-4 py-8">
        <div class="flex items-center mb-8">
            <a href="../index.html" class="text-gray-500 hover:text-indigo-600 mr-2">
                <i class="ri-home-line"></i>
            </a>
            <span class="text-gray-400 mx-2">/</span>
            <span class="text-gray-900 font-medium">使用指南</span>
        </div>

        <div class="mb-12">
            <h2 class="text-3xl font-bold mb-4 text-gray-900">组件库使用指南</h2>
            <p class="text-lg text-gray-600 max-w-4xl">
                DataScope UI 组件库旨在为数据分析平台提供统一、一致的用户界面组件。本指南介绍了组件库的设计原则、使用规范和最佳实践，帮助您快速开发高质量的用户界面。
            </p>
        </div>

        <!-- 目录 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h3 class="text-xl font-bold mb-4">指南目录</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h4 class="font-medium text-gray-900 mb-3">设计与开发</h4>
                    <ul class="space-y-2">
                        <li class="flex items-center">
                            <i class="ri-arrow-right-s-line text-indigo-600 mr-2"></i>
                            <a href="./design-principles.html" class="text-indigo-600 hover:text-indigo-800">设计原则</a>
                        </li>
                        <li class="flex items-center">
                            <i class="ri-arrow-right-s-line text-indigo-600 mr-2"></i>
                            <a href="./development.html" class="text-indigo-600 hover:text-indigo-800">开发指南</a>
                        </li>
                        <li class="flex items-center">
                            <i class="ri-arrow-right-s-line text-indigo-600 mr-2"></i>
                            <a href="./vue3.html" class="text-indigo-600 hover:text-indigo-800">Vue 3 集成</a>
                        </li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-medium text-gray-900 mb-3">其他资源</h4>
                    <ul class="space-y-2">
                        <li class="flex items-center">
                            <i class="ri-arrow-right-s-line text-indigo-600 mr-2"></i>
                            <a href="./changelog.html" class="text-indigo-600 hover:text-indigo-800">更新日志</a>
                        </li>
                        <li class="flex items-center">
                            <i class="ri-arrow-right-s-line text-indigo-600 mr-2"></i>
                            <a href="#best-practices" class="text-indigo-600 hover:text-indigo-800">最佳实践</a>
                        </li>
                        <li class="flex items-center">
                            <i class="ri-arrow-right-s-line text-indigo-600 mr-2"></i>
                            <a href="#customization" class="text-indigo-600 hover:text-indigo-800">定制与扩展</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 设计原则 -->
        <section id="design-principles" class="mb-12">
            <h3 class="text-2xl font-bold mb-6 pb-2 border-b border-gray-200">设计原则</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow-md p-6 guideline-card">
                    <div class="flex items-center mb-4">
                        <div class="flex-shrink-0 flex items-center justify-center h-10 w-10 rounded-full bg-blue-100 text-blue-600 mr-3">
                            <i class="ri-focus-3-line"></i>
                        </div>
                        <h4 class="text-lg font-semibold">简洁明了</h4>
                    </div>
                    <p class="text-gray-600">
                        组件设计注重简洁明了，避免不必要的视觉元素和复杂交互，让用户能够专注于数据和任务本身。
                    </p>
                </div>
                
                <div class="bg-white rounded-lg shadow-md p-6 guideline-card">
                    <div class="flex items-center mb-4">
                        <div class="flex-shrink-0 flex items-center justify-center h-10 w-10 rounded-full bg-green-100 text-green-600 mr-3">
                            <i class="ri-repeat-one-line"></i>
                        </div>
                        <h4 class="text-lg font-semibold">一致性</h4>
                    </div>
                    <p class="text-gray-600">
                        保持视觉风格、交互模式和行为的一致性，使用户能够轻松理解和预测组件的行为。
                    </p>
                </div>
                
                <div class="bg-white rounded-lg shadow-md p-6 guideline-card">
                    <div class="flex items-center mb-4">
                        <div class="flex-shrink-0 flex items-center justify-center h-10 w-10 rounded-full bg-purple-100 text-purple-600 mr-3">
                            <i class="ri-user-smile-line"></i>
                        </div>
                        <h4 class="text-lg font-semibold">易用性优先</h4>
                    </div>
                    <p class="text-gray-600">
                        组件的设计以易用性为优先考虑因素，降低用户操作复杂度，提供清晰的反馈和提示。
                    </p>
                </div>
                
                <div class="bg-white rounded-lg shadow-md p-6 guideline-card">
                    <div class="flex items-center mb-4">
                        <div class="flex-shrink-0 flex items-center justify-center h-10 w-10 rounded-full bg-yellow-100 text-yellow-600 mr-3">
                            <i class="ri-device-line"></i>
                        </div>
                        <h4 class="text-lg font-semibold">响应式设计</h4>
                    </div>
                    <p class="text-gray-600">
                        组件支持响应式设计，在不同屏幕尺寸和设备上能够提供良好的用户体验。
                    </p>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6">
                <h4 class="text-lg font-semibold mb-4">设计语言</h4>
                <p class="text-gray-600 mb-4">
                    DataScope UI 组件库基于以下设计语言构建：
                </p>
                <ul class="list-disc pl-6 space-y-2 text-gray-600">
                    <li>使用圆角和适当的阴影来提供层次感</li>
                    <li>采用简洁的线条和图标，减少视觉干扰</li>
                    <li>使用一致的颜色系统，主要基于蓝色和灰色调</li>
                    <li>提供清晰的视觉层次和内容分组</li>
                    <li>组件间保持一致的间距和对齐方式</li>
                </ul>
            </div>
        </section>

        <!-- 使用规范 -->
        <section id="usage-guidelines" class="mb-12">
            <h3 class="text-2xl font-bold mb-6 pb-2 border-b border-gray-200">使用规范</h3>
            
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h4 class="text-lg font-semibold mb-4">按钮的使用</h4>
                <p class="text-gray-600 mb-4">
                    按钮是用户交互的主要方式，请遵循以下规范：
                </p>
                
                <div class="mb-4">
                    <h5 class="font-medium text-gray-900 mb-2">按钮类型选择</h5>
                    <ul class="list-disc pl-6 space-y-2 text-gray-600">
                        <li>主要按钮（蓝色背景）：用于页面上最主要的操作</li>
                        <li>次要按钮（灰色边框）：用于次要操作或辅助性操作</li>
                        <li>文本按钮：用于不太重要的操作或紧凑布局中</li>
                        <li>图标按钮：用于常见的、易于理解的操作，或空间受限的场景</li>
                    </ul>
                </div>
                
                <div class="mb-4">
                    <h5 class="font-medium text-gray-900 mb-2">按钮排列顺序</h5>
                    <p class="text-gray-600 mb-2">
                        在对话框或表单中，按钮排列应遵循以下顺序：
                    </p>
                    <div class="bg-gray-50 p-4 rounded-lg flex justify-end space-x-2">
                        <button class="px-3 py-1 border border-gray-300 text-gray-700 rounded-md text-sm">取消</button>
                        <button class="px-3 py-1 bg-indigo-600 text-white rounded-md text-sm">确定</button>
                    </div>
                </div>
                
                <div class="code-block">
<pre><code class="language-html">&lt;!-- 主要按钮 --&gt;
&lt;button class="px-4 py-2 bg-indigo-600 text-white rounded-md"&gt;确认&lt;/button&gt;

&lt;!-- 次要按钮 --&gt;
&lt;button class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md"&gt;取消&lt;/button&gt;

&lt;!-- 图标按钮 --&gt;
&lt;button class="p-2 text-gray-500 hover:text-gray-700 rounded-md"&gt;
  &lt;i class="ri-search-line"&gt;&lt;/i&gt;
&lt;/button&gt;</code></pre>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h4 class="text-lg font-semibold mb-4">表单控件使用</h4>
                <p class="text-gray-600 mb-4">
                    表单是收集用户输入的重要方式，请遵循以下规范：
                </p>
                
                <div class="mb-4">
                    <h5 class="font-medium text-gray-900 mb-2">表单布局</h5>
                    <ul class="list-disc pl-6 space-y-2 text-gray-600">
                        <li>标签应放置在输入框上方或左侧，保持一致性</li>
                        <li>相关的表单项应进行分组，使用标题或分隔线分隔不同组</li>
                        <li>必填字段应使用星号（*）标记</li>
                        <li>错误信息应直接显示在相应表单项下方</li>
                    </ul>
                </div>
                
                <div class="mb-4">
                    <h5 class="font-medium text-gray-900 mb-2">输入验证</h5>
                    <p class="text-gray-600 mb-2">
                        在表单提交前进行输入验证，并提供清晰的错误反馈：
                    </p>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-1">用户名 <span class="text-red-500">*</span></label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        </div>
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-1">邮箱 <span class="text-red-500">*</span></label>
                            <input type="email" class="w-full px-3 py-2 border border-red-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500">
                            <p class="mt-1 text-sm text-red-600">请输入有效的邮箱地址</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 最佳实践 -->
        <section id="best-practices" class="mb-12">
            <h3 class="text-2xl font-bold mb-6 pb-2 border-b border-gray-200">最佳实践</h3>
            
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h4 class="text-lg font-semibold mb-4">页面布局</h4>
                <p class="text-gray-600 mb-4">
                    页面布局应遵循以下最佳实践：
                </p>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h5 class="font-medium text-gray-900 mb-2">内容分组</h5>
                        <ul class="list-disc pl-6 space-y-2 text-gray-600">
                            <li>相关内容应进行分组，使用卡片或分隔线明确区分</li>
                            <li>主要内容应位于页面顶部或中央显眼位置</li>
                            <li>次要内容或辅助操作可放置在侧边栏或底部</li>
                        </ul>
                    </div>
                    
                    <div>
                        <h5 class="font-medium text-gray-900 mb-2">响应式考虑</h5>
                        <ul class="list-disc pl-6 space-y-2 text-gray-600">
                            <li>使用流式布局和弹性盒模型适应不同屏幕尺寸</li>
                            <li>在移动设备上适当调整元素大小和间距</li>
                            <li>考虑在小屏幕上隐藏或折叠次要内容</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6">
                <h4 class="text-lg font-semibold mb-4">交互设计</h4>
                <p class="text-gray-600 mb-4">
                    良好的交互设计能够提升用户体验：
                </p>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h5 class="font-medium text-gray-900 mb-2">即时反馈</h5>
                        <ul class="list-disc pl-6 space-y-2 text-gray-600">
                            <li>按钮点击时提供视觉反馈（颜色变化、轻微动画）</li>
                            <li>操作完成后及时显示成功或失败消息</li>
                            <li>长时间操作时显示加载状态或进度指示</li>
                        </ul>
                    </div>
                    
                    <div>
                        <h5 class="font-medium text-gray-900 mb-2">错误处理</h5>
                        <ul class="list-disc pl-6 space-y-2 text-gray-600">
                            <li>错误消息应清晰明确，提供解决建议</li>
                            <li>在用户输入时进行实时验证，减少提交后的错误</li>
                            <li>系统错误应优雅处理，避免中断用户操作流程</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- 组件集成 -->
        <section id="component-integration" class="mb-12">
            <h3 class="text-2xl font-bold mb-6 pb-2 border-b border-gray-200">组件集成</h3>
            
            <div class="bg-white rounded-lg shadow-md p-6">
                <h4 class="text-lg font-semibold mb-4">在 Vue.js 中使用</h4>
                <p class="text-gray-600 mb-4">
                    DataScope UI 组件库可以轻松集成到 Vue.js 项目中：
                </p>
                
                <div class="code-block">
<pre><code class="language-html">&lt;template&gt;
  &lt;div&gt;
    &lt;!-- 使用按钮组件 --&gt;
    &lt;ds-button type="primary" @click="handleClick"&gt;确认&lt;/ds-button&gt;
    
    &lt;!-- 使用输入框组件 --&gt;
    &lt;ds-input 
      v-model="username"
      placeholder="请输入用户名"
      prefix-icon="user"
      :required="true"
    /&gt;
    
    &lt;!-- 使用数据表格组件 --&gt;
    &lt;ds-table :data="tableData" :columns="tableColumns" @row-click="handleRowClick" /&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref } from 'vue'

// 响应式状态
const username = ref('')
const tableData = ref([
  { id: 1, name: '数据1', status: 'active' },
  { id: 2, name: '数据2', status: 'inactive' }
])

const tableColumns = [
  { key: 'id', title: 'ID' },
  { key: 'name', title: '名称' },
  { key: 'status', title: '状态', type: 'status' }
]

// 事件处理函数
const handleClick = () => {
  // 处理按钮点击事件
}

const handleRowClick = (row) => {
  // 处理行点击事件
}
&lt;/script&gt;</code></pre>
                </div>
            </div>
        </section>

        <!-- 定制与扩展 -->
        <section id="customization" class="mb-12">
            <h3 class="text-2xl font-bold mb-6 pb-2 border-b border-gray-200">定制与扩展</h3>
            
            <div class="bg-white rounded-lg shadow-md p-6">
                <h4 class="text-lg font-semibold mb-4">主题定制</h4>
                <p class="text-gray-600 mb-4">
                    DataScope UI 组件库支持主题定制，您可以通过以下方式进行定制：
                </p>
                
                <div class="mb-4">
                    <h5 class="font-medium text-gray-900 mb-2">颜色系统</h5>
                    <p class="text-gray-600">
                        可以通过修改颜色变量来定制组件的颜色方案：
                    </p>
                    <div class="code-block">
<pre><code class="language-css">:root {
  --primary-color: #4f46e5;    /* 主要颜色 */
  --primary-color-light: #eef2ff;    /* 主要颜色的浅色版本 */
  --secondary-color: #6b7280;  /* 次要颜色 */
  --success-color: #10b981;    /* 成功状态颜色 */
  --warning-color: #f59e0b;    /* 警告状态颜色 */
  --danger-color: #ef4444;     /* 危险状态颜色 */
  --border-radius: 0.375rem;   /* 边框圆角 */
  --box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);  /* 阴影效果 */
}</code></pre>
                    </div>
                </div>
                
                <div>
                    <h5 class="font-medium text-gray-900 mb-2">组件扩展</h5>
                    <p class="text-gray-600">
                        您可以基于现有组件进行扩展，创建符合特定需求的自定义组件：
                    </p>
                    <div class="code-block">
<pre><code class="language-javascript">// 扩展基础按钮组件，创建特定应用场景的按钮
import { defineComponent, computed } from 'vue';
import DsButton from 'datascope-ui';

// 使用 Vue 3 defineComponent 创建组件
export default defineComponent({
  name: 'ExportButton',
  // 使用组件复合
  extends: DsButton,
  props: {
    // 自定义属性
    fileType: {
      type: String,
      default: 'csv',
      validator: (value) => ['csv', 'excel', 'pdf'].includes(value)
    }
  },
  setup(props, { emit }) {
    // 使用 computed 计算属性
    const buttonIcon = computed(() => {
      const iconMap = {
        csv: 'file-text',
        excel: 'file-excel',
        pdf: 'file-pdf'
      };
      return iconMap[props.fileType] || 'download';
    });
    
    // 导出数据的方法
    const exportData = (type) => {
      // 实现导出逻辑
      console.log(`导出 ${type} 格式数据`);
    };
    
    // 自定义点击事件处理
    const handleClick = (e) => {
      emit('before-export', props.fileType);
      // 调用导出逻辑
      exportData(props.fileType);
      emit('after-export', props.fileType);
    };
    
    return {
      buttonIcon,
      handleClick,
      exportData
    };
  }
});

// 也可以使用更简洁的使用式
/*
import { defineComponent } from 'vue';

// 使用 .vue 文件
&lt;script setup&gt;
import { computed } from 'vue';
import { DsButton } from 'datascope-ui';

// 属性定义
const props = defineProps({
  fileType: {
    type: String,
    default: 'csv',
    validator: (value) => ['csv', 'excel', 'pdf'].includes(value)
  }
});

// 事件定义
const emit = defineEmits(['before-export', 'after-export']);

// 计算属性
const buttonIcon = computed(() => {
  const iconMap = {
    csv: 'file-text',
    excel: 'file-excel',
    pdf: 'file-pdf'
  };
  return iconMap[props.fileType] || 'download';
});

// 导出数据方法
const exportData = (type) => {
  // 实现导出逻辑
};

// 点击事件处理
const handleClick = (e) => {
  emit('before-export', props.fileType);
  exportData(props.fileType);
  emit('after-export', props.fileType);
};
&lt;/script&gt;
*/</code></pre>
                    </div>
                </div>
            </div>
        </section>

        <div class="text-center mb-12 space-x-4">
            <a href="../components.html" class="inline-flex items-center px-5 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                浏览组件库 <i class="ri-arrow-right-line ml-1"></i>
            </a>
            <a href="./vue3.html" class="inline-flex items-center px-5 py-2 border border-gray-300 text-sm font-medium rounded-md bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                Vue 3 使用指南 <i class="ri-code-line ml-1"></i>
            </a>
        </div>
    </div>
    
    <footer class="bg-white border-t border-gray-200 py-6 mt-12">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="mb-4 md:mb-0">
                    <p class="text-gray-500">© 2023 DataScope UI 组件库</p>
                </div>
                <div class="flex space-x-4">
                    <a href="./changelog.html" class="text-gray-500 hover:text-indigo-600">
                        更新日志
                    </a>
                    <a href="../sitemap.html" class="text-gray-500 hover:text-indigo-600">
                        网站地图
                    </a>
                    <a href="https://github.com/yourusername/datascope-ui" target="_blank" class="text-gray-500 hover:text-indigo-600">
                        <i class="ri-github-fill text-xl"></i>
                    </a>
                </div>
            </div>
        </div>
    </footer>
</body>
</html>