<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>更新日志 - DataScope UI组件库</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        .version-tag {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
            line-height: 1;
        }
        .tag-new {
            background-color: #DEF7EC;
            color: #03543E;
        }
        .tag-optimization {
            background-color: #E1EFFE;
            color: #1E429F;
        }
        .tag-bugfix {
            background-color: #FEF3C7;
            color: #92400E;
        }
        .version-entry {
            position: relative;
        }
        .version-entry::before {
            content: '';
            position: absolute;
            top: 0;
            left: -1.5rem;
            width: 1rem;
            height: 1rem;
            border-radius: 50%;
            background-color: #4F46E5;
            transform: translateY(1.25rem);
            z-index: 10;
        }
        .version-entry.history::before {
            background-color: #E5E7EB;
        }
        .timeline {
            position: relative;
        }
        .timeline::before {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            left: -1rem;
            width: 2px;
            background-color: #E5E7EB;
        }
    </style>
</head>
<body class="bg-gray-50">
    <header class="bg-white shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex">
                    <div class="flex-shrink-0 flex items-center">
                        <img class="h-8 w-auto" src="../../assets/logo.png" alt="DataScope Logo">
                    </div>
                    <nav class="ml-6 flex space-x-8">
                        <a href="../../index.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            首页
                        </a>
                        <a href="../index.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            组件
                        </a>
                        <a href="./getting-started.html" class="border-indigo-500 text-gray-900 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            使用指南
                        </a>
                        <a href="../sitemap.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            网站地图
                        </a>
                    </nav>
                </div>
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <a href="https://github.com/yourusername/datascope" target="_blank" class="ml-3 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <i class="ri-github-fill mr-2"></i> GitHub
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-10">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">更新日志</h1>
        
        <!-- 最新版本 -->
        <div class="mb-12">
            <div class="flex items-center mb-4">
                <h2 class="text-2xl font-semibold text-gray-900">V1.7.0 - 2023.04.22</h2>
                <span class="ml-3 version-tag tag-new">最新</span>
            </div>
            
            <div class="mb-6">
                <h3 class="text-lg font-medium text-gray-800 mb-3">新功能</h3>
                <ul class="space-y-2 ml-5 list-disc text-gray-600">
                    <li>在所有组件详情页添加统一的左侧导航菜单，包括基础组件、表单组件、容器组件、数据展示和动画效果组件</li>
                    <li>为所有组件页面添加统一的面包屑导航，增强用户在组件系统中的导航体验</li>
                    <li>在每个组件页面添加当前位置指示器，帮助用户理解当前所在位置</li>
                    <li>统一所有组件页面的页脚设计，改善整体一致性</li>
                </ul>
            </div>
            
            <div class="mb-6">
                <h3 class="text-lg font-medium text-gray-800 mb-3">优化</h3>
                <ul class="space-y-2 ml-5 list-disc text-gray-600">
                    <li>重新设计组件文档的整体布局，采用左侧导航 + 右侧内容的分栏结构</li>
                    <li>优化组件分类的显示，使所有组件类别在左侧导航中清晰展示</li>
                    <li>改进页面结构，使所有组件页面保持一致的交互模式和视觉设计</li>
                    <li>更新了按钮(buttons.html)、基础输入(basic-inputs.html)、卡片(cards.html)、数据表格(data-table.html)和过渡动画(transitions.html)等组件页面的布局</li>
                </ul>
            </div>
        </div>

        <!-- V1.6.0 版本 -->
        <div class="mb-12">
            <div class="flex items-center mb-4">
                <h2 class="text-2xl font-semibold text-gray-900">V1.6.0 - 2023.04.16</h2>
            </div>
            
            <div class="mb-6">
                <h3 class="text-lg font-medium text-gray-800 mb-3">新功能</h3>
                <ul class="space-y-2 ml-5 list-disc text-gray-600">
                    <li>在组件详情页添加左侧导航菜单，优化组件分类浏览体验</li>
                    <li>引入当前位置指示器，帮助用户了解当前浏览位置</li>
                    <li>优化页面结构，改善整体用户体验</li>
                </ul>
            </div>
            
            <div class="mb-6">
                <h3 class="text-lg font-medium text-gray-800 mb-3">优化</h3>
                <ul class="space-y-2 ml-5 list-disc text-gray-600">
                    <li>统一组件详情页的布局，提高一致性</li>
                    <li>增强组件分类的分组显示</li>
                    <li>改进反馈组件页面，提供更好的示例和代码展示</li>
                </ul>
            </div>
        </div>
        
        <!-- V1.5.0 版本 -->
        <div class="mb-12">
            <div class="flex items-center mb-4">
                <h2 class="text-2xl font-semibold text-gray-900">V1.5.0 - 2023.03.28</h2>
            </div>
            
            <div class="mb-6">
                <h3 class="text-lg font-medium text-gray-800 mb-3">新功能</h3>
                <ul class="space-y-2 ml-5 list-disc text-gray-600">
                    <li>新增通知提醒组件（Notification），支持不同类型和位置的通知</li>
                    <li>新增全局消息组件（Message），提供轻量级的反馈方式</li>
                    <li>新增加载动画（Loader）组件，支持多种尺寸和样式</li>
                </ul>
            </div>
            
            <div class="mb-6">
                <h3 class="text-lg font-medium text-gray-800 mb-3">优化</h3>
                <ul class="space-y-2 ml-5 list-disc text-gray-600">
                    <li>改进对话框组件的响应式布局</li>
                    <li>优化按钮组件的悬停和点击效果</li>
                    <li>调整表单控件的边距和对齐方式，提升易用性</li>
                </ul>
            </div>
            
            <div>
                <h3 class="text-lg font-medium text-gray-800 mb-3">Bug修复</h3>
                <ul class="space-y-2 ml-5 list-disc text-gray-600">
                    <li>修复了表格组件在小屏幕设备上的显示问题</li>
                    <li>解决了下拉菜单在某些情况下定位不准确的问题</li>
                    <li>修复了日期选择器在Safari浏览器上的兼容性问题</li>
                </ul>
            </div>
        </div>
        
        <!-- V1.4.0 版本 -->
        <div class="mb-12">
            <div class="flex items-center mb-4">
                <h2 class="text-2xl font-semibold text-gray-900">V1.4.0 - 2023.03.10</h2>
            </div>
            
            <div class="mb-6">
                <h3 class="text-lg font-medium text-gray-800 mb-3">新功能</h3>
                <ul class="space-y-2 ml-5 list-disc text-gray-600">
                    <li>新增数据表格组件，支持排序、筛选和分页功能</li>
                    <li>新增分页组件，提供多种尺寸和样式</li>
                    <li>引入图表组件，支持柱状图、折线图和饼图</li>
                </ul>
            </div>
            
            <div class="mb-6">
                <h3 class="text-lg font-medium text-gray-800 mb-3">优化</h3>
                <ul class="space-y-2 ml-5 list-disc text-gray-600">
                    <li>改进卡片组件的阴影和边框样式</li>
                    <li>增强表单验证的视觉反馈</li>
                    <li>优化组件库文档结构，提升阅读体验</li>
                </ul>
            </div>
            
            <div>
                <h3 class="text-lg font-medium text-gray-800 mb-3">Bug修复</h3>
                <ul class="space-y-2 ml-5 list-disc text-gray-600">
                    <li>修复了标签页组件切换时的动画问题</li>
                    <li>解决了模态框在某些情况下无法关闭的问题</li>
                    <li>修复了复选框组件在暗色模式下的样式问题</li>
                </ul>
            </div>
        </div>
        
        <!-- V1.3.0 版本 -->
        <div class="mb-12">
            <div class="flex items-center mb-4">
                <h2 class="text-2xl font-semibold text-gray-900">V1.3.0 - 2023.02.22</h2>
            </div>
            
            <div class="mb-6">
                <h3 class="text-lg font-medium text-gray-800 mb-3">新功能</h3>
                <ul class="space-y-2 ml-5 list-disc text-gray-600">
                    <li>新增卡片组件，支持多种样式和布局</li>
                    <li>新增标签页组件，提供水平和垂直两种方向</li>
                    <li>引入折叠面板组件，适用于分组展示信息</li>
                </ul>
            </div>
            
            <div class="mb-6">
                <h3 class="text-lg font-medium text-gray-800 mb-3">优化</h3>
                <ul class="space-y-2 ml-5 list-disc text-gray-600">
                    <li>改进按钮组件的响应式表现</li>
                    <li>调整排版组件的字体大小和行高</li>
                    <li>优化图标组件的加载性能</li>
                </ul>
            </div>
            
            <div>
                <h3 class="text-lg font-medium text-gray-800 mb-3">Bug修复</h3>
                <ul class="space-y-2 ml-5 list-disc text-gray-600">
                    <li>修复了输入框在聚焦状态下边框样式的问题</li>
                    <li>解决了单选按钮组在某些浏览器上的对齐问题</li>
                    <li>修复了下拉选择器选项过多时的滚动问题</li>
                </ul>
            </div>
        </div>
        
        <!-- V1.2.0 版本 -->
        <div class="mb-12">
            <div class="flex items-center mb-4">
                <h2 class="text-2xl font-semibold text-gray-900">V1.2.0 - 2023.02.05</h2>
            </div>
            
            <div class="mb-6">
                <h3 class="text-lg font-medium text-gray-800 mb-3">新功能</h3>
                <ul class="space-y-2 ml-5 list-disc text-gray-600">
                    <li>新增表单组件，包括输入框、复选框、单选框和选择器</li>
                    <li>引入表单布局组件，简化表单的构建</li>
                    <li>新增表单验证功能，支持多种验证规则</li>
                </ul>
            </div>
            
            <div class="mb-6">
                <h3 class="text-lg font-medium text-gray-800 mb-3">优化</h3>
                <ul class="space-y-2 ml-5 list-disc text-gray-600">
                    <li>改进警告提示组件的关闭动画</li>
                    <li>优化对话框组件的遮罩层效果</li>
                    <li>调整按钮组件的间距和对齐方式</li>
                </ul>
            </div>
            
            <div>
                <h3 class="text-lg font-medium text-gray-800 mb-3">Bug修复</h3>
                <ul class="space-y-2 ml-5 list-disc text-gray-600">
                    <li>修复了图标组件在高分辨率屏幕上的模糊问题</li>
                    <li>解决了按钮组件在禁用状态下仍可点击的问题</li>
                    <li>修复了警告提示组件自动关闭功能的计时问题</li>
                </ul>
            </div>
        </div>
        
        <!-- V1.1.0 版本 -->
        <div class="mb-12">
            <div class="flex items-center mb-4">
                <h2 class="text-2xl font-semibold text-gray-900">V1.1.0 - 2023.01.18</h2>
            </div>
            
            <div class="mb-6">
                <h3 class="text-lg font-medium text-gray-800 mb-3">新功能</h3>
                <ul class="space-y-2 ml-5 list-disc text-gray-600">
                    <li>新增警告提示组件，支持多种类型和可关闭选项</li>
                    <li>引入对话框组件，提供确认和取消操作</li>
                    <li>新增图标库，包含常用的界面图标</li>
                </ul>
            </div>
            
            <div class="mb-6">
                <h3 class="text-lg font-medium text-gray-800 mb-3">优化</h3>
                <ul class="space-y-2 ml-5 list-disc text-gray-600">
                    <li>改进按钮组件的可访问性</li>
                    <li>优化排版组件的响应式表现</li>
                    <li>调整颜色系统，提高对比度</li>
                </ul>
            </div>
            
            <div>
                <h3 class="text-lg font-medium text-gray-800 mb-3">Bug修复</h3>
                <ul class="space-y-2 ml-5 list-disc text-gray-600">
                    <li>修复了按钮组件在不同浏览器下的样式差异</li>
                    <li>解决了排版组件在小屏幕设备上的溢出问题</li>
                    <li>修复了文档中的代码示例错误</li>
                </ul>
            </div>
        </div>
        
        <!-- V1.0.0 版本 -->
        <div>
            <div class="flex items-center mb-4">
                <h2 class="text-2xl font-semibold text-gray-900">V1.0.0 - 2023.01.01</h2>
            </div>
            
            <div class="mb-6">
                <h3 class="text-lg font-medium text-gray-800 mb-3">初始版本</h3>
                <ul class="space-y-2 ml-5 list-disc text-gray-600">
                    <li>发布基础组件，包括按钮和排版</li>
                    <li>建立基础的设计系统，包括颜色、字体和间距</li>
                    <li>提供组件文档和使用示例</li>
                </ul>
            </div>
        </div>
    </div>
    
    <footer class="bg-white">
        <div class="max-w-7xl mx-auto py-12 px-4 overflow-hidden sm:px-6 lg:px-8">
            <nav class="-mx-5 -my-2 flex flex-wrap justify-center" aria-label="Footer">
                <div class="px-5 py-2">
                    <a href="../../index.html" class="text-base text-gray-500 hover:text-gray-900">首页</a>
                </div>
                <div class="px-5 py-2">
                    <a href="../index.html" class="text-base text-gray-500 hover:text-gray-900">组件</a>
                </div>
                <div class="px-5 py-2">
                    <a href="./getting-started.html" class="text-base text-gray-500 hover:text-gray-900">使用指南</a>
                </div>
                <div class="px-5 py-2">
                    <a href="./changelog.html" class="text-base text-gray-500 hover:text-gray-900">更新日志</a>
                </div>
            </nav>
            <p class="mt-8 text-center text-base text-gray-400">
                &copy; 2023 DataScope UI组件库. All rights reserved.
            </p>
        </div>
    </footer>
</body>
</html>