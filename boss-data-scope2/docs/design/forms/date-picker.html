<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>日期选择器 - DataScope UI组件库</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
  <style>
    .component-container {
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      padding: 24px;
      margin-bottom: 24px;
    }
    .code-block {
      background-color: #f8fafc;
      border-radius: 6px;
      padding: 16px;
      margin-top: 16px;
      font-family: monospace;
      font-size: 14px;
      white-space: pre-wrap;
      color: #334155;
    }
    .form-control {
      display: block;
      width: 100%;
      padding: 0.5rem 0.75rem;
      font-size: 1rem;
      line-height: 1.5;
      color: #333;
      background-color: #fff;
      background-clip: padding-box;
      border: 2px solid #cbd5e0;
      border-radius: 0.25rem;
      transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }
    .form-control:focus {
      border-color: #4a6cf7;
      outline: 0;
      box-shadow: 0 0 0 0.2rem rgba(74, 108, 247, 0.25);
    }
    .form-control:disabled {
      background-color: #f1f5f9;
      opacity: 1;
    }
    .label {
      display: inline-block;
      margin-bottom: 0.5rem;
      font-weight: 500;
      color: #4a5568;
    }
    .form-group {
      margin-bottom: 1rem;
    }
    .help-text {
      display: block;
      margin-top: 0.25rem;
      font-size: 0.875rem;
      color: #718096;
    }
    .calendar {
      width: 100%;
      max-width: 336px;
      background-color: white;
      border-radius: 0.5rem;
      overflow: hidden;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }
    .calendar-header {
      background-color: #f8fafc;
      padding: 1rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid #e2e8f0;
    }
    .calendar-header h4 {
      font-weight: 500;
      color: #4a5568;
      margin: 0;
    }
    .calendar-header button {
      background: none;
      border: none;
      cursor: pointer;
      color: #718096;
      padding: 0.25rem;
      border-radius: 0.25rem;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .calendar-header button:hover {
      background-color: #edf2f7;
      color: #4a5568;
    }
    .calendar-body {
      padding: 0.5rem;
    }
    .calendar-days {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      gap: 2px;
      margin-bottom: 4px;
    }
    .calendar-day-header {
      text-align: center;
      font-size: 0.75rem;
      font-weight: 500;
      color: #718096;
      padding: 0.5rem 0;
    }
    .calendar-day {
      text-align: center;
      padding: 0.5rem 0;
      font-size: 0.875rem;
      color: #4a5568;
      cursor: pointer;
      border-radius: 0.25rem;
    }
    .calendar-day:hover {
      background-color: #edf2f7;
    }
    .calendar-day.selected {
      background-color: #4a6cf7;
      color: white;
    }
    .calendar-day.today {
      border: 1px solid #4a6cf7;
    }
    .calendar-day.disabled {
      color: #cbd5e0;
      cursor: not-allowed;
    }
    .calendar-day.in-range {
      background-color: #ebf4ff;
    }
    .date-input {
      position: relative;
    }
    .date-input-icon {
      position: absolute;
      right: 10px;
      top: 50%;
      transform: translateY(-50%);
      color: #718096;
      pointer-events: none;
    }
    .time-picker {
      display: flex;
      justify-content: center;
      padding: 0.5rem;
      border-top: 1px solid #e2e8f0;
    }
    .time-picker select {
      margin: 0 0.25rem;
      padding: 0.25rem;
      border: 1px solid #e2e8f0;
      border-radius: 0.25rem;
      background-color: white;
      font-size: 0.875rem;
    }
    .relative {
      position: relative;
    }
  </style>
</head>
<body class="bg-gray-50 p-8">
  <div class="container mx-auto max-w-5xl">
    <h1 class="text-3xl font-bold mb-8 text-gray-800">日期选择器</h1>
    <p class="text-lg text-gray-600 mb-8">
      日期选择器为用户提供了直观的界面来选择日期和时间，支持多种模式和格式化选项。
    </p>

    <!-- 基础日期选择器 -->
    <div class="component-container bg-white shadow-sm">
      <h2 class="text-xl font-semibold mb-4 text-gray-800">基础日期选择器</h2>
      <p class="text-gray-600 mb-6">
        基础日期选择器用于选择某一天，点击输入框可弹出日历面板进行选择。
      </p>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="form-group">
          <label class="label">选择日期</label>
          <div class="date-input">
            <input type="text" class="form-control" placeholder="请选择日期" value="2023-11-15">
            <div class="date-input-icon">
              <i class="ri-calendar-line"></i>
            </div>
          </div>
        </div>
        <div class="form-group">
          <label class="label">禁用状态</label>
          <div class="date-input">
            <input type="text" class="form-control" placeholder="请选择日期" value="2023-11-15" disabled>
            <div class="date-input-icon">
              <i class="ri-calendar-line"></i>
            </div>
          </div>
        </div>
      </div>

      <div class="mt-8">
        <h3 class="text-lg font-medium mb-4 text-gray-700">日历面板</h3>
        <div class="calendar">
          <div class="calendar-header">
            <button><i class="ri-arrow-left-s-line"></i></button>
            <h4>2023年11月</h4>
            <button><i class="ri-arrow-right-s-line"></i></button>
          </div>
          <div class="calendar-body">
            <div class="calendar-days">
              <div class="calendar-day-header">日</div>
              <div class="calendar-day-header">一</div>
              <div class="calendar-day-header">二</div>
              <div class="calendar-day-header">三</div>
              <div class="calendar-day-header">四</div>
              <div class="calendar-day-header">五</div>
              <div class="calendar-day-header">六</div>

              <div class="calendar-day disabled">29</div>
              <div class="calendar-day disabled">30</div>
              <div class="calendar-day disabled">31</div>
              <div class="calendar-day">1</div>
              <div class="calendar-day">2</div>
              <div class="calendar-day">3</div>
              <div class="calendar-day">4</div>
              <div class="calendar-day">5</div>
              <div class="calendar-day">6</div>
              <div class="calendar-day">7</div>
              <div class="calendar-day">8</div>
              <div class="calendar-day">9</div>
              <div class="calendar-day">10</div>
              <div class="calendar-day">11</div>
              <div class="calendar-day">12</div>
              <div class="calendar-day">13</div>
              <div class="calendar-day">14</div>
              <div class="calendar-day selected">15</div>
              <div class="calendar-day today">16</div>
              <div class="calendar-day">17</div>
              <div class="calendar-day">18</div>
              <div class="calendar-day">19</div>
              <div class="calendar-day">20</div>
              <div class="calendar-day">21</div>
              <div class="calendar-day">22</div>
              <div class="calendar-day">23</div>
              <div class="calendar-day">24</div>
              <div class="calendar-day">25</div>
              <div class="calendar-day">26</div>
              <div class="calendar-day">27</div>
              <div class="calendar-day">28</div>
              <div class="calendar-day">29</div>
              <div class="calendar-day">30</div>
              <div class="calendar-day disabled">1</div>
              <div class="calendar-day disabled">2</div>
            </div>
          </div>
        </div>
      </div>

      <div class="code-block">
<!-- 基础日期选择器示例 -->
<template>
  <!-- 基础日期选择器 -->
  <FormItem label="选择日期">
    <DatePicker 
      v-model="date" 
      placeholder="请选择日期"
      format="YYYY-MM-DD"
    />
  </FormItem>

  <!-- 禁用状态 -->
  <FormItem label="截止日期">
    <DatePicker 
      v-model="deadline" 
      disabled 
    />
  </FormItem>
</template>
      </div>
    </div>

    <!-- 日期范围选择器 -->
    <div class="component-container bg-white shadow-sm">
      <h2 class="text-xl font-semibold mb-4 text-gray-800">日期范围选择器</h2>
      <p class="text-gray-600 mb-6">
        日期范围选择器允许用户选择一个日期区间，适用于需要筛选一段时间内数据的场景。
      </p>

      <div class="form-group">
        <label class="label">选择日期范围</label>
        <div class="date-input">
          <input type="text" class="form-control" placeholder="开始日期 - 结束日期" value="2023-11-10 - 2023-11-20">
          <div class="date-input-icon">
            <i class="ri-calendar-line"></i>
          </div>
        </div>
      </div>

      <div class="mt-8">
        <h3 class="text-lg font-medium mb-4 text-gray-700">日期范围选择面板</h3>
        <div class="calendar">
          <div class="calendar-header">
            <button><i class="ri-arrow-left-s-line"></i></button>
            <h4>2023年11月</h4>
            <button><i class="ri-arrow-right-s-line"></i></button>
          </div>
          <div class="calendar-body">
            <div class="calendar-days">
              <div class="calendar-day-header">日</div>
              <div class="calendar-day-header">一</div>
              <div class="calendar-day-header">二</div>
              <div class="calendar-day-header">三</div>
              <div class="calendar-day-header">四</div>
              <div class="calendar-day-header">五</div>
              <div class="calendar-day-header">六</div>

              <div class="calendar-day disabled">29</div>
              <div class="calendar-day disabled">30</div>
              <div class="calendar-day disabled">31</div>
              <div class="calendar-day">1</div>
              <div class="calendar-day">2</div>
              <div class="calendar-day">3</div>
              <div class="calendar-day">4</div>
              <div class="calendar-day">5</div>
              <div class="calendar-day">6</div>
              <div class="calendar-day">7</div>
              <div class="calendar-day">8</div>
              <div class="calendar-day">9</div>
              <div class="calendar-day selected">10</div>
              <div class="calendar-day in-range">11</div>
              <div class="calendar-day in-range">12</div>
              <div class="calendar-day in-range">13</div>
              <div class="calendar-day in-range">14</div>
              <div class="calendar-day in-range">15</div>
              <div class="calendar-day in-range">16</div>
              <div class="calendar-day in-range">17</div>
              <div class="calendar-day in-range">18</div>
              <div class="calendar-day in-range">19</div>
              <div class="calendar-day selected">20</div>
              <div class="calendar-day">21</div>
              <div class="calendar-day">22</div>
              <div class="calendar-day">23</div>
              <div class="calendar-day">24</div>
              <div class="calendar-day">25</div>
              <div class="calendar-day">26</div>
              <div class="calendar-day">27</div>
              <div class="calendar-day">28</div>
              <div class="calendar-day">29</div>
              <div class="calendar-day">30</div>
              <div class="calendar-day disabled">1</div>
              <div class="calendar-day disabled">2</div>
            </div>
          </div>
        </div>
      </div>

      <div class="code-block">
<!-- 日期范围选择器示例 -->
<template>
  <!-- 日期范围选择器 -->
  <FormItem label="选择日期范围">
    <DateRangePicker 
      v-model="dateRange" 
      placeholder="开始日期 - 结束日期"
      format="YYYY-MM-DD"
    />
  </FormItem>
</template>
      </div>
    </div>

    <!-- 时间选择器 -->
    <div class="component-container bg-white shadow-sm">
      <h2 class="text-xl font-semibold mb-4 text-gray-800">时间选择器</h2>
      <p class="text-gray-600 mb-6">
        时间选择器用于选择具体时间，支持小时、分钟和秒的精确选择。
      </p>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="form-group">
          <label class="label">选择时间</label>
          <div class="date-input">
            <input type="text" class="form-control" placeholder="请选择时间" value="14:30:00">
            <div class="date-input-icon">
              <i class="ri-time-line"></i>
            </div>
          </div>
        </div>
        <div class="form-group">
          <label class="label">12小时制</label>
          <div class="date-input">
            <input type="text" class="form-control" placeholder="请选择时间" value="02:30 PM">
            <div class="date-input-icon">
              <i class="ri-time-line"></i>
            </div>
          </div>
        </div>
      </div>

      <div class="mt-8">
        <h3 class="text-lg font-medium mb-4 text-gray-700">时间选择面板</h3>
        <div class="calendar">
          <div class="time-picker">
            <select>
              <option>00</option>
              <option>01</option>
              <option>02</option>
              <option>...</option>
              <option selected>14</option>
              <option>15</option>
              <option>...</option>
              <option>23</option>
            </select>
            <span class="text-gray-500 mx-1">:</span>
            <select>
              <option>00</option>
              <option>...</option>
              <option selected>30</option>
              <option>...</option>
              <option>59</option>
            </select>
            <span class="text-gray-500 mx-1">:</span>
            <select>
              <option selected>00</option>
              <option>01</option>
              <option>...</option>
              <option>59</option>
            </select>
          </div>
        </div>
      </div>

      <div class="code-block">
<!-- 时间选择器示例 -->
<template>
  <!-- 基础时间选择器 -->
  <FormItem label="选择时间">
    <TimePicker 
      v-model="time" 
      placeholder="请选择时间"
      format="HH:mm:ss"
    />
  </FormItem>

  <!-- 12小时制 -->
  <FormItem label="选择时间">
    <TimePicker 
      v-model="time12" 
      placeholder="请选择时间"
      format="hh:mm A"
      use12Hours
    />
  </FormItem>
</template>
      </div>
    </div>

    <!-- 日期时间选择器 -->
    <div class="component-container bg-white shadow-sm">
      <h2 class="text-xl font-semibold mb-4 text-gray-800">日期时间选择器</h2>
      <p class="text-gray-600 mb-6">
        日期时间选择器结合了日期选择和时间选择功能，允许用户同时选择日期和具体时间。
      </p>

      <div class="form-group">
        <label class="label">选择日期和时间</label>
        <div class="date-input">
          <input type="text" class="form-control" placeholder="请选择日期和时间" value="2023-11-15 14:30:00">
          <div class="date-input-icon">
            <i class="ri-calendar-event-line"></i>
          </div>
        </div>
      </div>

      <div class="mt-8">
        <h3 class="text-lg font-medium mb-4 text-gray-700">日期时间选择面板</h3>
        <div class="calendar">
          <div class="calendar-header">
            <button><i class="ri-arrow-left-s-line"></i></button>
            <h4>2023年11月</h4>
            <button><i class="ri-arrow-right-s-line"></i></button>
          </div>
          <div class="calendar-body">
            <div class="calendar-days">
              <div class="calendar-day-header">日</div>
              <div class="calendar-day-header">一</div>
              <div class="calendar-day-header">二</div>
              <div class="calendar-day-header">三</div>
              <div class="calendar-day-header">四</div>
              <div class="calendar-day-header">五</div>
              <div class="calendar-day-header">六</div>

              <div class="calendar-day disabled">29</div>
              <div class="calendar-day disabled">30</div>
              <div class="calendar-day disabled">31</div>
              <div class="calendar-day">1</div>
              <div class="calendar-day">2</div>
              <div class="calendar-day">3</div>
              <div class="calendar-day">4</div>
              <div class="calendar-day">5</div>
              <div class="calendar-day">6</div>
              <div class="calendar-day">7</div>
              <div class="calendar-day">8</div>
              <div class="calendar-day">9</div>
              <div class="calendar-day">10</div>
              <div class="calendar-day">11</div>
              <div class="calendar-day">12</div>
              <div class="calendar-day">13</div>
              <div class="calendar-day">14</div>
              <div class="calendar-day selected">15</div>
              <div class="calendar-day">16</div>
              <div class="calendar-day">17</div>
              <div class="calendar-day">18</div>
              <div class="calendar-day">19</div>
              <div class="calendar-day">20</div>
              <div class="calendar-day">21</div>
              <div class="calendar-day">22</div>
              <div class="calendar-day">23</div>
              <div class="calendar-day">24</div>
              <div class="calendar-day">25</div>
              <div class="calendar-day">26</div>
              <div class="calendar-day">27</div>
              <div class="calendar-day">28</div>
              <div class="calendar-day">29</div>
              <div class="calendar-day">30</div>
              <div class="calendar-day disabled">1</div>
              <div class="calendar-day disabled">2</div>
            </div>
          </div>
          <div class="time-picker">
            <select>
              <option>00</option>
              <option>01</option>
              <option>...</option>
              <option selected>14</option>
              <option>...</option>
              <option>23</option>
            </select>
            <span class="text-gray-500 mx-1">:</span>
            <select>
              <option>00</option>
              <option>...</option>
              <option selected>30</option>
              <option>...</option>
              <option>59</option>
            </select>
            <span class="text-gray-500 mx-1">:</span>
            <select>
              <option selected>00</option>
              <option>01</option>
              <option>...</option>
              <option>59</option>
            </select>
          </div>
        </div>
      </div>

      <div class="code-block">
<!-- 日期时间选择器示例 -->
<template>
  <!-- 日期时间选择器 -->
  <FormItem label="选择日期和时间">
    <DateTimePicker 
      v-model="dateTime" 
      placeholder="请选择日期和时间"
      format="YYYY-MM-DD HH:mm:ss"
    />
  </FormItem>
</template>
      </div>
    </div>

    <!-- 应用场景 -->
    <div class="component-container bg-white shadow-sm">
      <h2 class="text-xl font-semibold mb-4 text-gray-800">应用场景示例</h2>
      <p class="text-gray-600 mb-6">
        以下是日期选择器在各种场景中的应用示例。
      </p>

      <div class="mb-8">
        <h3 class="text-lg font-medium mb-4 text-gray-700">数据筛选</h3>
        <div class="bg-white p-6 rounded-lg shadow border border-gray-200">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="form-group mb-0">
              <label class="label">日期范围</label>
              <div class="date-input">
                <input type="text" class="form-control" placeholder="开始日期 - 结束日期" value="2023-11-01 - 2023-11-30">
                <div class="date-input-icon">
                  <i class="ri-calendar-line"></i>
                </div>
              </div>
            </div>
            <div class="form-group mb-0">
              <label class="label">数据类型</label>
              <select class="form-control">
                <option>全部</option>
                <option selected>类型A</option>
                <option>类型B</option>
                <option>类型C</option>
              </select>
            </div>
            <div class="form-group mb-0">
              <label class="label">&nbsp;</label>
              <button class="bg-blue-600 text-white w-full py-2 rounded hover:bg-blue-700 transition">
                <i class="ri-search-line mr-1"></i>
                搜索
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="mb-8">
        <h3 class="text-lg font-medium mb-4 text-gray-700">活动预约</h3>
        <div class="bg-white p-6 rounded-lg shadow border border-gray-200">
          <h4 class="text-xl font-semibold mb-4">预约活动时间</h4>
          <div class="space-y-4">
            <div class="form-group">
              <label class="label">活动日期</label>
              <div class="date-input">
                <input type="text" class="form-control" value="2023-12-15">
                <div class="date-input-icon">
                  <i class="ri-calendar-line"></i>
                </div>
              </div>
            </div>
            <div class="form-group">
              <label class="label">时间段</label>
              <div class="grid grid-cols-2 gap-4">
                <div class="date-input">
                  <input type="text" class="form-control" value="14:00">
                  <div class="date-input-icon">
                    <i class="ri-time-line"></i>
                  </div>
                </div>
                <div class="date-input">
                  <input type="text" class="form-control" value="16:00">
                  <div class="date-input-icon">
                    <i class="ri-time-line"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="form-group">
              <label class="label">参与人数</label>
              <select class="form-control">
                <option>1人</option>
                <option selected>2人</option>
                <option>3人</option>
                <option>4人及以上</option>
              </select>
            </div>
            <div class="pt-4">
              <button class="bg-blue-600 text-white w-full py-2 rounded hover:bg-blue-700 transition">
                提交预约
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="text-center mt-12 mb-8">
      <a href="./index.html" class="text-blue-600 hover:text-blue-800 font-medium">
        <i class="ri-arrow-left-line mr-1"></i> 返回表单组件总览
      </a>
    </div>
  </div>
</body>
</html>