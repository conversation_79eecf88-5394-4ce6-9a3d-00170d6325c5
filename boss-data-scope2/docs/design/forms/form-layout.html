<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>表单布局组件 - DataScope UI组件库</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
  <style>
    .component-container {
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      padding: 24px;
      margin-bottom: 24px;
    }
    .code-block {
      background-color: #f8fafc;
      border-radius: 6px;
      padding: 16px;
      margin-top: 16px;
      font-family: monospace;
      font-size: 14px;
      white-space: pre-wrap;
      color: #334155;
    }
    .form-control {
      display: block;
      width: 100%;
      padding: 0.5rem 0.75rem;
      font-size: 1rem;
      line-height: 1.5;
      color: #333;
      background-color: #fff;
      background-clip: padding-box;
      border: 2px solid #cbd5e0;
      border-radius: 0.25rem;
      transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }
    .form-control:focus {
      border-color: #4a6cf7;
      outline: 0;
      box-shadow: 0 0 0 0.2rem rgba(74, 108, 247, 0.25);
    }
    .label {
      display: inline-block;
      margin-bottom: 0.5rem;
      font-weight: 500;
      color: #4a5568;
    }
    .form-group {
      margin-bottom: 1rem;
    }
    .help-text {
      display: block;
      margin-top: 0.25rem;
      font-size: 0.875rem;
      color: #718096;
    }
    .error-text {
      display: block;
      margin-top: 0.25rem;
      font-size: 0.875rem;
      color: #e53e3e;
    }
    .required::after {
      content: '*';
      color: #e53e3e;
      margin-left: 4px;
    }
    .checkbox {
      -webkit-appearance: none;
      appearance: none;
      width: 18px;
      height: 18px;
      border: 2px solid #cbd5e0;
      border-radius: 4px;
      outline: none;
      cursor: pointer;
      position: relative;
      vertical-align: middle;
    }
    .checkbox:checked {
      background-color: #4f46e5;
      border-color: #4f46e5;
    }
    .checkbox:checked::after {
      content: '✓';
      font-size: 14px;
      color: white;
      position: absolute;
      top: -1px;
      left: 3px;
    }
    .radio {
      -webkit-appearance: none;
      appearance: none;
      width: 18px;
      height: 18px;
      border: 2px solid #cbd5e0;
      border-radius: 50%;
      outline: none;
      cursor: pointer;
      position: relative;
      vertical-align: middle;
    }
    .radio:checked {
      border-color: #4f46e5;
    }
    .radio:checked::after {
      content: '';
      width: 8px;
      height: 8px;
      background-color: #4f46e5;
      border-radius: 50%;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  </style>
</head>
<body class="bg-gray-50 p-8">
  <div class="container mx-auto max-w-5xl">
    <h1 class="text-3xl font-bold mb-8 text-gray-800">表单布局组件</h1>
    <p class="text-lg text-gray-600 mb-8">
      表单布局组件用于组织和排列表单控件，提供结构化的表单展示方式，使表单更易于阅读和使用。
    </p>

    <!-- 表单项组件 -->
    <div class="component-container bg-white shadow-sm">
      <h2 class="text-xl font-semibold mb-4 text-gray-800">表单项组件</h2>
      <p class="text-gray-600 mb-6">
        表单项组件用于包裹单个表单控件，提供一致的布局结构，包括标签、控件和辅助文本。
      </p>

      <div class="space-y-6">
        <div class="form-group">
          <label class="label required">用户名</label>
          <input type="text" class="form-control" placeholder="请输入用户名">
          <span class="help-text">用户名长度为2-20个字符</span>
        </div>

        <div class="form-group">
          <label class="label required">邮箱</label>
          <input type="email" class="form-control input-error" placeholder="请输入邮箱地址" value="invalid">
          <span class="error-text">请输入有效的邮箱地址</span>
        </div>

        <div class="form-group">
          <label class="label">备注</label>
          <textarea class="form-control" rows="3" placeholder="请输入备注信息（选填）"></textarea>
        </div>
      </div>

      <div class="code-block">
<!-- 表单项组件示例 -->
<template>
  <!-- 基础表单项 -->
  <FormItem label="用户名" required>
    <Input v-model="username" placeholder="请输入用户名" />
    <template #help>用户名长度为2-20个字符</template>
  </FormItem>

  <!-- 带错误提示的表单项 -->
  <FormItem label="邮箱" required error="请输入有效的邮箱地址">
    <Input v-model="email" status="error" placeholder="请输入邮箱地址" />
  </FormItem>

  <!-- 带子标签的表单项 -->
  <FormItem label="网站" optional>
    <Input v-model="website" placeholder="请输入网站地址" />
  </FormItem>
</template>
      </div>
    </div>

    <!-- 表单布局 -->
    <div class="component-container bg-white shadow-sm">
      <h2 class="text-xl font-semibold mb-4 text-gray-800">表单布局</h2>
      <p class="text-gray-600 mb-6">
        根据不同的使用场景，表单可以有不同的布局方式，包括垂直布局、水平布局、行内布局等。
      </p>

      <div class="space-y-8">
        <div>
          <h3 class="text-lg font-medium mb-4 text-gray-700">垂直布局（默认）</h3>
          <div class="space-y-4">
            <div class="form-group">
              <label class="label">用户名</label>
              <input type="text" class="form-control" placeholder="请输入用户名">
            </div>
            <div class="form-group">
              <label class="label">密码</label>
              <input type="password" class="form-control" placeholder="请输入密码">
            </div>
          </div>
        </div>

        <div>
          <h3 class="text-lg font-medium mb-4 text-gray-700">水平布局</h3>
          <div class="space-y-4">
            <div class="flex items-center">
              <label class="label w-24 mb-0">用户名</label>
              <div class="flex-1">
                <input type="text" class="form-control" placeholder="请输入用户名">
              </div>
            </div>
            <div class="flex items-center">
              <label class="label w-24 mb-0">密码</label>
              <div class="flex-1">
                <input type="password" class="form-control" placeholder="请输入密码">
              </div>
            </div>
          </div>
        </div>

        <div>
          <h3 class="text-lg font-medium mb-4 text-gray-700">行内布局</h3>
          <div class="flex items-center space-x-4">
            <div class="form-group mb-0">
              <label class="label">用户名</label>
              <input type="text" class="form-control" placeholder="用户名">
            </div>
            <div class="form-group mb-0">
              <label class="label">密码</label>
              <input type="password" class="form-control" placeholder="密码">
            </div>
            <button class="bg-blue-600 text-white px-4 py-2 rounded-md mt-6">
              登录
            </button>
          </div>
        </div>
      </div>

      <div class="code-block">
<!-- 表单布局示例 -->
<template>
  <!-- 垂直布局 -->
  <Form layout="vertical">
    <FormItem label="用户名">
      <Input v-model="username" placeholder="请输入用户名" />
    </FormItem>
    <FormItem label="密码">
      <Input v-model="password" type="password" placeholder="请输入密码" />
    </FormItem>
  </Form>

  <!-- 水平布局 -->
  <Form layout="horizontal" :label-width="80">
    <FormItem label="用户名">
      <Input v-model="username" placeholder="请输入用户名" />
    </FormItem>
    <FormItem label="密码">
      <Input v-model="password" type="password" placeholder="请输入密码" />
    </FormItem>
  </Form>

  <!-- 行内布局 -->
  <Form layout="inline">
    <FormItem label="用户名">
      <Input v-model="username" placeholder="用户名" />
    </FormItem>
    <FormItem label="密码">
      <Input v-model="password" type="password" placeholder="密码" />
    </FormItem>
    <FormItem>
      <Button type="primary">登录</Button>
    </FormItem>
  </Form>
</template>
      </div>
    </div>

    <!-- 表单分组 -->
    <div class="component-container bg-white shadow-sm">
      <h2 class="text-xl font-semibold mb-4 text-gray-800">表单分组</h2>
      <p class="text-gray-600 mb-6">
        表单分组用于将相关的表单项组织在一起，使表单结构更加清晰。
      </p>

      <div class="space-y-8">
        <div class="border border-gray-200 rounded-md">
          <div class="bg-gray-50 px-4 py-3 border-b border-gray-200">
            <h3 class="font-medium">基本信息</h3>
          </div>
          <div class="p-4 space-y-4">
            <div class="form-group">
              <label class="label required">姓名</label>
              <input type="text" class="form-control" placeholder="请输入姓名">
            </div>
            <div class="form-group">
              <label class="label required">邮箱</label>
              <input type="email" class="form-control" placeholder="请输入邮箱">
            </div>
            <div class="form-group">
              <label class="label">电话</label>
              <input type="tel" class="form-control" placeholder="请输入电话">
            </div>
          </div>
        </div>

        <div class="border border-gray-200 rounded-md">
          <div class="bg-gray-50 px-4 py-3 border-b border-gray-200">
            <h3 class="font-medium">地址信息</h3>
          </div>
          <div class="p-4 space-y-4">
            <div class="form-group">
              <label class="label">国家/地区</label>
              <select class="form-control">
                <option value="cn">中国</option>
                <option value="us">美国</option>
                <option value="jp">日本</option>
              </select>
            </div>
            <div class="form-group">
              <label class="label">详细地址</label>
              <textarea class="form-control" rows="2" placeholder="请输入详细地址"></textarea>
            </div>
          </div>
        </div>
      </div>

      <div class="code-block">
<!-- 表单分组示例 -->
<template>
  <Form>
    <FormGroup title="基本信息">
      <FormItem label="姓名" required>
        <Input v-model="name" placeholder="请输入姓名" />
      </FormItem>
      <FormItem label="邮箱" required>
        <Input v-model="email" placeholder="请输入邮箱" />
      </FormItem>
      <FormItem label="电话">
        <Input v-model="phone" placeholder="请输入电话" />
      </FormItem>
    </FormGroup>

    <FormGroup title="地址信息">
      <FormItem label="国家/地区">
        <Select v-model="country" placeholder="请选择国家/地区">
          <Option value="cn">中国</Option>
          <Option value="us">美国</Option>
          <Option value="jp">日本</Option>
        </Select>
      </FormItem>
      <FormItem label="详细地址">
        <TextArea v-model="address" placeholder="请输入详细地址" :rows="2" />
      </FormItem>
    </FormGroup>
  </Form>
</template>
      </div>
    </div>

    <!-- 表单验证 -->
    <div class="component-container bg-white shadow-sm">
      <h2 class="text-xl font-semibold mb-4 text-gray-800">表单验证</h2>
      <p class="text-gray-600 mb-6">
        表单验证用于确保用户输入的数据符合预期的格式和规则，提高数据质量。
      </p>

      <div class="space-y-4">
        <div class="form-group">
          <label class="label required">用户名</label>
          <input type="text" class="form-control" placeholder="请输入用户名" value="a">
          <span class="error-text">用户名长度应为2-20个字符</span>
        </div>
        <div class="form-group">
          <label class="label required">邮箱</label>
          <input type="email" class="form-control" placeholder="请输入邮箱" value="invalid-email">
          <span class="error-text">请输入有效的邮箱地址</span>
        </div>
        <div class="form-group">
          <label class="label required">密码</label>
          <input type="password" class="form-control" placeholder="请输入密码" value="weak">
          <span class="error-text">密码应包含字母、数字和特殊字符，长度至少为8位</span>
        </div>
        <div class="form-group">
          <label class="label required">确认密码</label>
          <input type="password" class="form-control" placeholder="请再次输入密码" value="different">
          <span class="error-text">两次输入的密码不一致</span>
        </div>
      </div>

      <div class="code-block">
<!-- 表单验证示例 -->
<template>
  <Form :model="form" :rules="rules" ref="formRef">
    <FormItem label="用户名" prop="username" required>
      <Input v-model="form.username" placeholder="请输入用户名" />
    </FormItem>
    <FormItem label="邮箱" prop="email" required>
      <Input v-model="form.email" placeholder="请输入邮箱" />
    </FormItem>
    <FormItem label="密码" prop="password" required>
      <Input v-model="form.password" type="password" placeholder="请输入密码" />
    </FormItem>
    <FormItem label="确认密码" prop="confirmPassword" required>
      <Input v-model="form.confirmPassword" type="password" placeholder="请再次输入密码" />
    </FormItem>
    <FormItem>
      <Button type="primary" @click="submitForm">提交</Button>
      <Button @click="resetForm" class="ml-2">重置</Button>
    </FormItem>
  </Form>
</template>

<script>
export default {
  data() {
    // 自定义验证规则：确认密码
    const validateConfirmPassword = (rule, value, callback) => {
      if (value !== this.form.password) {
        callback(new Error('两次输入的密码不一致'));
      } else {
        callback();
      }
    };
    
    return {
      form: {
        username: '',
        email: '',
        password: '',
        confirmPassword: ''
      },
      rules: {
        username: [
          { required: true, message: '请输入用户名' },
          { min: 2, max: 20, message: '用户名长度应为2-20个字符' }
        ],
        email: [
          { required: true, message: '请输入邮箱' },
          { type: 'email', message: '请输入有效的邮箱地址' }
        ],
        password: [
          { required: true, message: '请输入密码' },
          { pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/, 
            message: '密码应包含字母、数字和特殊字符，长度至少为8位' }
        ],
        confirmPassword: [
          { required: true, message: '请再次输入密码' },
          { validator: validateConfirmPassword, trigger: 'blur' }
        ]
      }
    };
  },
  methods: {
    submitForm() {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          console.log('表单验证通过');
          // 提交表单逻辑
        } else {
          console.log('表单验证失败');
        }
      });
    },
    resetForm() {
      this.$refs.formRef.resetFields();
    }
  }
}
</script>
      </div>
    </div>

    <!-- 应用场景 -->
    <div class="component-container bg-white shadow-sm">
      <h2 class="text-xl font-semibold mb-4 text-gray-800">应用场景示例</h2>
      <p class="text-gray-600 mb-6">
        以下是表单布局在常见场景中的应用示例。
      </p>

      <div class="mb-8">
        <h3 class="text-lg font-medium mb-4 text-gray-700">注册表单</h3>
        <div class="bg-white p-6 rounded-lg shadow border border-gray-200">
          <h4 class="text-xl font-semibold mb-6">创建账号</h4>
          <div class="space-y-4">
            <div class="form-group">
              <label class="label required">用户名</label>
              <input type="text" class="form-control" placeholder="请输入用户名">
            </div>
            <div class="form-group">
              <label class="label required">电子邮箱</label>
              <input type="email" class="form-control" placeholder="请输入电子邮箱">
              <span class="help-text">我们将发送验证邮件到此地址</span>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="form-group">
                <label class="label required">密码</label>
                <input type="password" class="form-control" placeholder="请输入密码">
              </div>
              <div class="form-group">
                <label class="label required">确认密码</label>
                <input type="password" class="form-control" placeholder="请再次输入密码">
              </div>
            </div>
            <div class="form-group">
              <div class="flex items-center">
                <input type="checkbox" id="agreement" class="checkbox mr-2">
                <label for="agreement">我已阅读并同意<a href="#" class="text-blue-600 hover:text-blue-800">服务条款</a>和<a href="#" class="text-blue-600 hover:text-blue-800">隐私政策</a></label>
              </div>
            </div>
            <div class="pt-4">
              <button class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out">
                注册
              </button>
            </div>
            <div class="text-center text-sm text-gray-600">
              已有账号？<a href="#" class="text-blue-600 hover:text-blue-800">立即登录</a>
            </div>
          </div>
        </div>
      </div>

      <div class="mb-8">
        <h3 class="text-lg font-medium mb-4 text-gray-700">多步骤表单</h3>
        <div class="bg-white p-6 rounded-lg shadow border border-gray-200">
          <div class="mb-8">
            <div class="flex items-center justify-between">
              <div class="w-full flex items-center">
                <div class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-medium">1</div>
                <div class="h-1 bg-blue-600 flex-1"></div>
              </div>
              <div class="w-full flex items-center">
                <div class="w-8 h-8 bg-gray-200 text-gray-600 rounded-full flex items-center justify-center font-medium">2</div>
                <div class="h-1 bg-gray-200 flex-1"></div>
              </div>
              <div class="w-8 h-8 bg-gray-200 text-gray-600 rounded-full flex items-center justify-center font-medium">3</div>
            </div>
            <div class="flex justify-between mt-2 text-sm">
              <div class="text-blue-600 font-medium">基本信息</div>
              <div class="text-gray-500">联系方式</div>
              <div class="text-gray-500">完成</div>
            </div>
          </div>

          <div class="space-y-4">
            <div class="form-group">
              <label class="label required">姓名</label>
              <input type="text" class="form-control" placeholder="请输入姓名">
            </div>
            <div class="form-group">
              <label class="label required">性别</label>
              <div class="space-x-4">
                <label class="inline-flex items-center">
                  <input type="radio" class="radio" name="gender" checked>
                  <span class="ml-2">男</span>
                </label>
                <label class="inline-flex items-center">
                  <input type="radio" class="radio" name="gender">
                  <span class="ml-2">女</span>
                </label>
              </div>
            </div>
            <div class="form-group">
              <label class="label required">出生日期</label>
              <input type="date" class="form-control">
            </div>
            <div class="pt-4 flex justify-between">
              <button class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out">
                上一步
              </button>
              <button class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out">
                下一步
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="text-center mt-12 mb-8">
      <a href="../index.html" class="text-blue-600 hover:text-blue-800 font-medium">
        <i class="ri-arrow-left-line mr-1"></i> 返回组件总览
      </a>
    </div>
  </div>
</body>
</html>