<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>上传组件 - DataScope UI组件库</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
  <style>
    .component-container {
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      padding: 24px;
      margin-bottom: 24px;
    }
    .code-block {
      background-color: #f8fafc;
      border-radius: 6px;
      padding: 16px;
      margin-top: 16px;
      font-family: monospace;
      font-size: 14px;
      white-space: pre-wrap;
      color: #334155;
    }
    .form-control {
      display: block;
      width: 100%;
      padding: 0.5rem 0.75rem;
      font-size: 1rem;
      line-height: 1.5;
      color: #333;
      background-color: #fff;
      background-clip: padding-box;
      border: 2px solid #cbd5e0;
      border-radius: 0.25rem;
      transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }
    .form-control:focus {
      border-color: #4a6cf7;
      outline: 0;
      box-shadow: 0 0 0 0.2rem rgba(74, 108, 247, 0.25);
    }
    .label {
      display: inline-block;
      margin-bottom: 0.5rem;
      font-weight: 500;
      color: #4a5568;
    }
    .form-group {
      margin-bottom: 1rem;
    }
    .upload-area {
      border: 2px dashed #cbd5e0;
      border-radius: 0.5rem;
      padding: 2rem;
      text-align: center;
      cursor: pointer;
      transition: all 0.2s ease-in-out;
    }
    .upload-area:hover {
      border-color: #4a6cf7;
    }
    .upload-area.active {
      border-color: #4a6cf7;
      background-color: #ebf4ff;
    }
    .upload-list {
      margin-top: 1rem;
    }
    .upload-item {
      display: flex;
      align-items: center;
      padding: 0.5rem;
      border: 1px solid #e2e8f0;
      border-radius: 0.25rem;
      margin-bottom: 0.5rem;
      background-color: white;
    }
    .upload-item-name {
      flex-grow: 1;
      margin-left: 0.5rem;
      font-size: 0.875rem;
    }
    .upload-item-size {
      color: #718096;
      font-size: 0.75rem;
      margin-right: 0.5rem;
    }
    .upload-item-actions {
      display: flex;
      align-items: center;
    }
    .upload-item-actions button {
      background: none;
      border: none;
      cursor: pointer;
      color: #718096;
      padding: 0.25rem;
      font-size: 1rem;
      line-height: 1;
    }
    .upload-item-actions button:hover {
      color: #e53e3e;
    }
    .upload-progress {
      height: 4px;
      background-color: #edf2f7;
      border-radius: 2px;
      overflow: hidden;
      margin-top: 0.25rem;
    }
    .upload-progress-bar {
      height: 100%;
      background-color: #4a6cf7;
      border-radius: 2px;
    }
    .upload-progress-text {
      font-size: 0.75rem;
      color: #718096;
    }
    .upload-list-image {
      display: flex;
      flex-wrap: wrap;
      gap: 0.5rem;
      margin-top: 1rem;
    }
    .upload-image-item {
      width: 104px;
      height: 104px;
      border-radius: 0.25rem;
      overflow: hidden;
      position: relative;
    }
    .upload-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    .upload-image-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.2s;
    }
    .upload-image-item:hover .upload-image-overlay {
      opacity: 1;
    }
    .upload-image-overlay button {
      background: none;
      border: none;
      cursor: pointer;
      color: white;
      padding: 0.25rem;
      font-size: 1.25rem;
      line-height: 1;
    }
    .avatar-uploader {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    .avatar-upload {
      width: 100px;
      height: 100px;
      border-radius: 50%;
      overflow: hidden;
      position: relative;
      cursor: pointer;
      background-color: #f1f5f9;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #718096;
    }
    .avatar-upload img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    .avatar-upload-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.2s;
    }
    .avatar-upload:hover .avatar-upload-overlay {
      opacity: 1;
    }
  </style>
</head>
<body class="bg-gray-50 p-8">
  <div class="container mx-auto max-w-5xl">
    <h1 class="text-3xl font-bold mb-8 text-gray-800">上传组件</h1>
    <p class="text-lg text-gray-600 mb-8">
      上传组件提供了用户上传文件和图片的交互界面，支持单文件和多文件上传、拖拽上传、图片预览等功能。
    </p>

    <!-- 基础上传 -->
    <div class="component-container bg-white shadow-sm">
      <h2 class="text-xl font-semibold mb-4 text-gray-800">基础上传</h2>
      <p class="text-gray-600 mb-6">
        基础上传组件允许用户通过点击选择文件进行上传，并显示上传列表。
      </p>

      <div class="form-group">
        <label class="label">文件上传</label>
        <div>
          <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded transition">
            <i class="ri-upload-line mr-1"></i> 点击上传
          </button>
          <span class="text-sm text-gray-500 ml-2">支持 .jpg、.jpeg、.png、.pdf、.doc、.docx 格式</span>
        </div>
        <div class="upload-list">
          <div class="upload-item">
            <i class="ri-file-line text-gray-500"></i>
            <div class="upload-item-name">document.pdf</div>
            <div class="upload-item-size">1.2MB</div>
            <div class="upload-item-actions">
              <button><i class="ri-close-line"></i></button>
            </div>
          </div>
          <div class="upload-item">
            <i class="ri-file-word-line text-blue-500"></i>
            <div class="upload-item-name">report.docx</div>
            <div class="upload-item-size">523KB</div>
            <div class="upload-item-actions">
              <button><i class="ri-close-line"></i></button>
            </div>
          </div>
          <div class="upload-item">
            <i class="ri-file-line text-gray-500"></i>
            <div class="upload-item-name">data.xlsx</div>
            <div class="upload-item-size">845KB</div>
            <div class="upload-progress">
              <div class="upload-progress-bar" style="width: 70%;"></div>
            </div>
            <div class="upload-progress-text ml-2">70%</div>
          </div>
        </div>
      </div>

      <div class="code-block">
<!-- 基础上传示例 -->
<template>
  <!-- 基础上传 -->
  <FormItem label="文件上传">
    <Upload 
      action="/api/upload" 
      :max-size="5 * 1024 * 1024"
      :accept="['.jpg', '.jpeg', '.png', '.pdf', '.doc', '.docx']"
      @on-success="handleSuccess"
      @on-error="handleError"
    >
      <Button type="primary" icon="ri-upload-line">点击上传</Button>
      <template #tip>
        <div class="text-sm text-gray-500 mt-1">
          支持 .jpg、.jpeg、.png、.pdf、.doc、.docx 格式
        </div>
      </template>
    </Upload>
  </FormItem>
</template>
      </div>
    </div>

    <!-- 拖拽上传 -->
    <div class="component-container bg-white shadow-sm">
      <h2 class="text-xl font-semibold mb-4 text-gray-800">拖拽上传</h2>
      <p class="text-gray-600 mb-6">
        拖拽上传允许用户通过拖放文件到指定区域进行上传，提供更直观的操作方式。
      </p>

      <div class="form-group">
        <label class="label">拖拽上传区域</label>
        <div class="upload-area">
          <i class="ri-upload-cloud-line text-4xl text-gray-400 mb-2"></i>
          <p class="text-gray-600">点击此处或拖拽文件到此区域进行上传</p>
          <p class="text-gray-500 text-sm mt-1">支持多文件同时上传</p>
        </div>
        <div class="upload-list">
          <div class="upload-item">
            <i class="ri-file-image-line text-green-500"></i>
            <div class="upload-item-name">image.jpg</div>
            <div class="upload-item-size">2.3MB</div>
            <div class="upload-item-actions">
              <button><i class="ri-close-line"></i></button>
            </div>
          </div>
        </div>
      </div>

      <div class="mt-8">
        <h3 class="text-lg font-medium mb-4 text-gray-700">拖拽上传状态</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <p class="text-sm text-gray-600 mb-2">默认状态</p>
            <div class="upload-area">
              <i class="ri-upload-cloud-line text-4xl text-gray-400 mb-2"></i>
              <p class="text-gray-600">点击此处或拖拽文件到此区域进行上传</p>
            </div>
          </div>
          <div>
            <p class="text-sm text-gray-600 mb-2">拖拽悬停状态</p>
            <div class="upload-area active">
              <i class="ri-upload-cloud-line text-4xl text-blue-500 mb-2"></i>
              <p class="text-blue-600">释放文件开始上传</p>
            </div>
          </div>
        </div>
      </div>

      <div class="code-block">
<!-- 拖拽上传示例 -->
<template>
  <!-- 拖拽上传 -->
  <FormItem label="拖拽上传区域">
    <Upload 
      action="/api/upload" 
      type="drag" 
      multiple
      :max-count="5"
      @on-success="handleSuccess"
      @on-error="handleError"
    >
      <div class="flex flex-col items-center justify-center p-6">
        <i class="ri-upload-cloud-line text-4xl text-gray-400 mb-2"></i>
        <p class="text-gray-600">点击此处或拖拽文件到此区域进行上传</p>
        <p class="text-gray-500 text-sm mt-1">支持多文件同时上传</p>
      </div>
    </Upload>
  </FormItem>
</template>
      </div>
    </div>

    <!-- 图片上传 -->
    <div class="component-container bg-white shadow-sm">
      <h2 class="text-xl font-semibold mb-4 text-gray-800">图片上传</h2>
      <p class="text-gray-600 mb-6">
        图片上传组件提供了图片预览和管理功能，适用于需要上传和展示图片的场景。
      </p>

      <div class="form-group">
        <label class="label">图片上传</label>
        <div>
          <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded transition">
            <i class="ri-image-add-line mr-1"></i> 上传图片
          </button>
          <span class="text-sm text-gray-500 ml-2">只支持 .jpg、.jpeg、.png、.gif 格式</span>
        </div>
        <div class="upload-list-image">
          <div class="upload-image-item">
            <img src="https://images.unsplash.com/photo-1604537466608-109fa2f16c3b?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8M3x8bW91bnRhaW4lMjBsYW5kc2NhcGV8ZW58MHx8MHx8&auto=format&fit=crop&w=200&q=60" alt="Preview" class="upload-image">
            <div class="upload-image-overlay">
              <button><i class="ri-eye-line"></i></button>
              <button><i class="ri-delete-bin-line"></i></button>
            </div>
          </div>
          <div class="upload-image-item">
            <img src="https://images.unsplash.com/photo-1682687982501-1e58ab814714?ixlib=rb-4.0.3&ixid=MnwxMjA3fDF8MHxzZWFyY2h8MXx8c2VhfGVufDB8fDB8fA%3D%3D&auto=format&fit=crop&w=200&q=60" alt="Preview" class="upload-image">
            <div class="upload-image-overlay">
              <button><i class="ri-eye-line"></i></button>
              <button><i class="ri-delete-bin-line"></i></button>
            </div>
          </div>
          <div class="upload-image-item">
            <div class="w-full h-full flex items-center justify-center bg-gray-100 cursor-pointer">
              <i class="ri-add-line text-2xl text-gray-400"></i>
            </div>
          </div>
        </div>
      </div>

      <div class="code-block">
<!-- 图片上传示例 -->
<template>
  <!-- 图片上传 -->
  <FormItem label="图片上传">
    <ImageUpload 
      action="/api/upload/image" 
      :max-count="9"
      :max-size="2 * 1024 * 1024"
      accept="image/*"
      @on-preview="handlePreview"
      @on-remove="handleRemove"
    >
      <Button type="primary" icon="ri-image-add-line">上传图片</Button>
      <template #tip>
        <div class="text-sm text-gray-500 mt-1">
          只支持 .jpg、.jpeg、.png、.gif 格式
        </div>
      </template>
    </ImageUpload>
  </FormItem>
</template>
      </div>
    </div>

    <!-- 头像上传 -->
    <div class="component-container bg-white shadow-sm">
      <h2 class="text-xl font-semibold mb-4 text-gray-800">头像上传</h2>
      <p class="text-gray-600 mb-6">
        头像上传组件专用于用户头像的上传和裁剪，提供圆形预览。
      </p>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="form-group">
          <label class="label">基础头像上传</label>
          <div class="avatar-uploader">
            <div class="avatar-upload">
              <img src="https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8M3x8cG9ydHJhaXR8ZW58MHx8MHx8&auto=format&fit=crop&w=200&q=60" alt="Avatar">
              <div class="avatar-upload-overlay">
                <i class="ri-camera-line text-lg text-white"></i>
              </div>
            </div>
            <div class="text-sm text-gray-500 mt-2">点击更换头像</div>
          </div>
        </div>
        <div class="form-group">
          <label class="label">空状态</label>
          <div class="avatar-uploader">
            <div class="avatar-upload">
              <i class="ri-user-add-line text-3xl"></i>
              <div class="avatar-upload-overlay">
                <i class="ri-camera-line text-lg text-white"></i>
              </div>
            </div>
            <div class="text-sm text-gray-500 mt-2">点击上传头像</div>
          </div>
        </div>
      </div>

      <div class="code-block">
<!-- 头像上传示例 -->
<template>
  <!-- 头像上传 -->
  <FormItem label="头像上传">
    <AvatarUpload 
      v-model="avatar" 
      action="/api/upload/avatar"
      :max-size="1 * 1024 * 1024"
      :width="100"
      :height="100"
      crop
      @on-success="handleAvatarSuccess"
      @on-error="handleAvatarError"
    />
  </FormItem>
</template>
      </div>
    </div>

    <!-- 应用场景 -->
    <div class="component-container bg-white shadow-sm">
      <h2 class="text-xl font-semibold mb-4 text-gray-800">应用场景示例</h2>
      <p class="text-gray-600 mb-6">
        以下是上传组件在实际场景中的应用示例。
      </p>

      <div class="mb-8">
        <h3 class="text-lg font-medium mb-4 text-gray-700">个人资料设置</h3>
        <div class="bg-white p-6 rounded-lg shadow border border-gray-200">
          <h4 class="text-xl font-semibold mb-6">编辑个人资料</h4>
          <div class="flex flex-col md:flex-row gap-6">
            <div class="md:w-1/3">
              <div class="avatar-uploader">
                <div class="avatar-upload">
                  <img src="https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8M3x8cG9ydHJhaXR8ZW58MHx8MHx8&auto=format&fit=crop&w=200&q=60" alt="Avatar">
                  <div class="avatar-upload-overlay">
                    <i class="ri-camera-line text-lg text-white"></i>
                  </div>
                </div>
                <div class="text-sm text-gray-500 mt-2">点击更换头像</div>
              </div>
            </div>
            <div class="md:w-2/3">
              <div class="space-y-4">
                <div class="form-group">
                  <label class="label">姓名</label>
                  <input type="text" class="form-control" value="张三">
                </div>
                <div class="form-group">
                  <label class="label">个人简介</label>
                  <textarea class="form-control" rows="3">喜欢摄影、旅行和阅读的产品经理</textarea>
                </div>
                <div class="form-group">
                  <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded transition">
                    保存修改
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="mb-8">
        <h3 class="text-lg font-medium mb-4 text-gray-700">产品图片上传</h3>
        <div class="bg-white p-6 rounded-lg shadow border border-gray-200">
          <h4 class="text-xl font-semibold mb-6">上传产品图片</h4>
          <div class="space-y-4">
            <div class="form-group">
              <label class="label">产品主图</label>
              <div class="upload-area">
                <i class="ri-image-add-line text-4xl text-gray-400 mb-2"></i>
                <p class="text-gray-600">点击或拖拽上传产品主图</p>
                <p class="text-gray-500 text-sm mt-1">建议尺寸：800x800px，最大不超过2MB</p>
              </div>
            </div>
            <div class="form-group">
              <label class="label">产品展示图（最多5张）</label>
              <div class="upload-list-image">
                <div class="upload-image-item">
                  <img src="https://images.unsplash.com/photo-1542291026-7eec264c27ff?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8c2hvZXN8ZW58MHx8MHx8&auto=format&fit=crop&w=200&q=60" alt="Product" class="upload-image">
                  <div class="upload-image-overlay">
                    <button><i class="ri-eye-line"></i></button>
                    <button><i class="ri-delete-bin-line"></i></button>
                  </div>
                </div>
                <div class="upload-image-item">
                  <img src="https://images.unsplash.com/photo-1549298916-b41d501d3772?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8M3x8c2hvZXN8ZW58MHx8MHx8&auto=format&fit=crop&w=200&q=60" alt="Product" class="upload-image">
                  <div class="upload-image-overlay">
                    <button><i class="ri-eye-line"></i></button>
                    <button><i class="ri-delete-bin-line"></i></button>
                  </div>
                </div>
                <div class="upload-image-item">
                  <div class="w-full h-full flex items-center justify-center bg-gray-100 cursor-pointer">
                    <i class="ri-add-line text-2xl text-gray-400"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="form-group">
              <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded transition">
                保存图片
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="text-center mt-12 mb-8">
      <a href="./index.html" class="text-blue-600 hover:text-blue-800 font-medium">
        <i class="ri-arrow-left-line mr-1"></i> 返回表单组件总览
      </a>
    </div>
  </div>
</body>
</html>