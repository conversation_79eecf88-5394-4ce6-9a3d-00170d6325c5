<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DataScope 开关组件</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
    <style>
        .component-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #eaeaea;
        }
        .preview-section {
            margin-bottom: 30px;
        }
        .demo-item {
            margin: 10px;
            padding: 10px;
            border: 1px solid #eaeaea;
            border-radius: 8px;
        }
        
        /* 开关动画 */
        .switch-transition {
            transition: all 0.2s ease-in-out;
        }
        
        .switch-enter-active,
        .switch-leave-active {
            transition: transform 0.2s ease-in-out;
        }
        
        .switch-enter-from,
        .switch-leave-to {
            transform: translateX(0);
        }
        
        .switch-enter-to,
        .switch-leave-from {
            transform: translateX(16px);
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold mb-8 text-center">DataScope 开关组件</h1>
        
        <div class="bg-white shadow rounded-lg p-6 mb-8">
            <h2 class="component-title">基本开关</h2>
            
            <div class="demo-item">
                <p class="text-sm text-gray-500 mb-2">默认开关</p>
                <div class="flex flex-col space-y-4">
                    <div class="flex items-center">
                        <button type="button" class="bg-gray-200 relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <span class="sr-only">关闭</span>
                            <span class="translate-x-0 relative inline-block h-5 w-5 rounded-full bg-white shadow transform switch-transition"></span>
                        </button>
                        <span class="ml-3 text-sm text-gray-900">关闭状态</span>
                    </div>
                    
                    <div class="flex items-center">
                        <button type="button" class="bg-indigo-600 relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <span class="sr-only">开启</span>
                            <span class="translate-x-5 relative inline-block h-5 w-5 rounded-full bg-white shadow transform switch-transition"></span>
                        </button>
                        <span class="ml-3 text-sm text-gray-900">开启状态</span>
                    </div>
                    
                    <div class="flex items-center">
                        <button type="button" disabled class="bg-gray-200 relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-not-allowed transition-colors ease-in-out duration-200 focus:outline-none opacity-50">
                            <span class="sr-only">禁用关闭</span>
                            <span class="translate-x-0 relative inline-block h-5 w-5 rounded-full bg-white shadow transform switch-transition"></span>
                        </button>
                        <span class="ml-3 text-sm text-gray-400">禁用关闭状态</span>
                    </div>
                    
                    <div class="flex items-center">
                        <button type="button" disabled class="bg-indigo-300 relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-not-allowed transition-colors ease-in-out duration-200 focus:outline-none opacity-50">
                            <span class="sr-only">禁用开启</span>
                            <span class="translate-x-5 relative inline-block h-5 w-5 rounded-full bg-white shadow transform switch-transition"></span>
                        </button>
                        <span class="ml-3 text-sm text-gray-400">禁用开启状态</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="bg-white shadow rounded-lg p-6 mb-8">
            <h2 class="component-title">开关尺寸</h2>
            
            <div class="demo-item">
                <p class="text-sm text-gray-500 mb-2">小号开关</p>
                <div class="flex items-center">
                    <button type="button" class="bg-indigo-600 relative inline-flex flex-shrink-0 h-4 w-8 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <span class="sr-only">开启</span>
                        <span class="translate-x-3 relative inline-block h-3 w-3 rounded-full bg-white shadow transform switch-transition"></span>
                    </button>
                    <span class="ml-3 text-xs text-gray-900">小号开关</span>
                </div>
            </div>
            
            <div class="demo-item">
                <p class="text-sm text-gray-500 mb-2">默认开关</p>
                <div class="flex items-center">
                    <button type="button" class="bg-indigo-600 relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <span class="sr-only">开启</span>
                        <span class="translate-x-5 relative inline-block h-5 w-5 rounded-full bg-white shadow transform switch-transition"></span>
                    </button>
                    <span class="ml-3 text-sm text-gray-900">默认开关</span>
                </div>
            </div>
            
            <div class="demo-item">
                <p class="text-sm text-gray-500 mb-2">大号开关</p>
                <div class="flex items-center">
                    <button type="button" class="bg-indigo-600 relative inline-flex flex-shrink-0 h-8 w-14 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <span class="sr-only">开启</span>
                        <span class="translate-x-7 relative inline-block h-7 w-7 rounded-full bg-white shadow transform switch-transition"></span>
                    </button>
                    <span class="ml-3 text-base text-gray-900">大号开关</span>
                </div>
            </div>
        </div>
        
        <div class="bg-white shadow rounded-lg p-6 mb-8">
            <h2 class="component-title">开关颜色</h2>
            
            <div class="demo-item">
                <p class="text-sm text-gray-500 mb-2">不同颜色的开关</p>
                <div class="flex flex-col space-y-4">
                    <div class="flex items-center">
                        <button type="button" class="bg-indigo-600 relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <span class="sr-only">开启</span>
                            <span class="translate-x-5 relative inline-block h-5 w-5 rounded-full bg-white shadow transform switch-transition"></span>
                        </button>
                        <span class="ml-3 text-sm text-gray-900">主题色开关</span>
                    </div>
                    
                    <div class="flex items-center">
                        <button type="button" class="bg-green-600 relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                            <span class="sr-only">开启</span>
                            <span class="translate-x-5 relative inline-block h-5 w-5 rounded-full bg-white shadow transform switch-transition"></span>
                        </button>
                        <span class="ml-3 text-sm text-gray-900">成功色开关</span>
                    </div>
                    
                    <div class="flex items-center">
                        <button type="button" class="bg-yellow-500 relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500">
                            <span class="sr-only">开启</span>
                            <span class="translate-x-5 relative inline-block h-5 w-5 rounded-full bg-white shadow transform switch-transition"></span>
                        </button>
                        <span class="ml-3 text-sm text-gray-900">警告色开关</span>
                    </div>
                    
                    <div class="flex items-center">
                        <button type="button" class="bg-red-600 relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                            <span class="sr-only">开启</span>
                            <span class="translate-x-5 relative inline-block h-5 w-5 rounded-full bg-white shadow transform switch-transition"></span>
                        </button>
                        <span class="ml-3 text-sm text-gray-900">危险色开关</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="bg-white shadow rounded-lg p-6 mb-8">
            <h2 class="component-title">开关变体</h2>
            
            <div class="demo-item">
                <p class="text-sm text-gray-500 mb-2">带图标的开关</p>
                <div class="flex flex-col space-y-4">
                    <div class="flex items-center">
                        <button type="button" class="bg-gray-200 relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <span aria-hidden="true" class="pointer-events-none absolute h-full w-full rounded-md bg-white"></span>
                            <span aria-hidden="true" class="translate-x-0 pointer-events-none absolute left-0 inline-block h-5 w-5 rounded-full bg-white shadow transform ring-0 transition-transform ease-in-out duration-200">
                                <span class="absolute inset-0 h-full w-full flex items-center justify-center transition-opacity text-gray-400">
                                    <i class="fas fa-times text-xs"></i>
                                </span>
                            </span>
                        </button>
                        <span class="ml-3 text-sm text-gray-900">关闭状态（带图标）</span>
                    </div>
                    
                    <div class="flex items-center">
                        <button type="button" class="bg-indigo-600 relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <span aria-hidden="true" class="pointer-events-none absolute h-full w-full rounded-md bg-white"></span>
                            <span aria-hidden="true" class="translate-x-5 pointer-events-none absolute left-0 inline-block h-5 w-5 rounded-full bg-white shadow transform ring-0 transition-transform ease-in-out duration-200">
                                <span class="absolute inset-0 h-full w-full flex items-center justify-center transition-opacity text-indigo-600">
                                    <i class="fas fa-check text-xs"></i>
                                </span>
                            </span>
                        </button>
                        <span class="ml-3 text-sm text-gray-900">开启状态（带图标）</span>
                    </div>
                </div>
            </div>
            
            <div class="demo-item">
                <p class="text-sm text-gray-500 mb-2">带文字的开关</p>
                <div class="flex flex-col space-y-4">
                    <div class="flex items-center">
                        <div class="flex items-center h-6">
                            <button type="button" class="bg-gray-200 relative inline-flex flex-shrink-0 h-6 w-16 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                <span class="sr-only">关闭</span>
                                <span class="absolute left-0 h-6 flex items-center justify-center w-8 text-xs text-gray-900 font-medium ml-1">关</span>
                                <span class="translate-x-0 relative inline-block h-5 w-5 rounded-full bg-white shadow transform switch-transition"></span>
                            </button>
                        </div>
                        <span class="ml-3 text-sm text-gray-900">带文字的关闭状态</span>
                    </div>
                    
                    <div class="flex items-center">
                        <div class="flex items-center h-6">
                            <button type="button" class="bg-indigo-600 relative inline-flex flex-shrink-0 h-6 w-16 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                <span class="sr-only">开启</span>
                                <span class="absolute right-0 h-6 flex items-center justify-center w-8 text-xs text-white font-medium mr-1">开</span>
                                <span class="translate-x-10 relative inline-block h-5 w-5 rounded-full bg-white shadow transform switch-transition"></span>
                            </button>
                        </div>
                        <span class="ml-3 text-sm text-gray-900">带文字的开启状态</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="bg-white shadow rounded-lg p-6 mb-8">
            <h2 class="component-title">开关应用场景</h2>
            
            <div class="demo-item">
                <p class="text-sm text-gray-500 mb-2">设置面板中的开关</p>
                <div class="divide-y divide-gray-200">
                    <div class="py-4 flex items-center justify-between">
                        <div>
                            <h3 class="text-sm font-medium text-gray-900">夜间模式</h3>
                            <p class="text-xs text-gray-500">启用后将使用暗色主题</p>
                        </div>
                        <button type="button" class="bg-gray-200 relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <span class="sr-only">关闭</span>
                            <span class="translate-x-0 relative inline-block h-5 w-5 rounded-full bg-white shadow transform switch-transition"></span>
                        </button>
                    </div>
                    
                    <div class="py-4 flex items-center justify-between">
                        <div>
                            <h3 class="text-sm font-medium text-gray-900">通知提醒</h3>
                            <p class="text-xs text-gray-500">接收应用内消息和邮件通知</p>
                        </div>
                        <button type="button" class="bg-indigo-600 relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <span class="sr-only">开启</span>
                            <span class="translate-x-5 relative inline-block h-5 w-5 rounded-full bg-white shadow transform switch-transition"></span>
                        </button>
                    </div>
                    
                    <div class="py-4 flex items-center justify-between">
                        <div>
                            <h3 class="text-sm font-medium text-gray-900">双因素认证</h3>
                            <p class="text-xs text-gray-500">提高账户安全性</p>
                        </div>
                        <button type="button" class="bg-indigo-600 relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <span class="sr-only">开启</span>
                            <span class="translate-x-5 relative inline-block h-5 w-5 rounded-full bg-white shadow transform switch-transition"></span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="bg-white shadow rounded-lg p-6">
            <h2 class="component-title">开关组件示例代码</h2>
            <div class="bg-gray-100 p-4 rounded">
                <pre class="text-sm overflow-x-auto">
&lt;template&gt;
  &lt;div class="form-item"&gt;
    &lt;div class="flex items-center"&gt;
      &lt;div v-if="labelPosition === 'left'" class="mr-3"&gt;
        &lt;label v-if="label" class="text-sm font-medium text-gray-900"&gt;{{ label }}&lt;/label&gt;
        &lt;p v-if="description" class="text-xs text-gray-500"&gt;{{ description }}&lt;/p&gt;
      &lt;/div>
      
      &lt;button 
        type="button" 
        :disabled="disabled"
        @click="toggle"
        :class="[
          modelValue ? activeColorClass : 'bg-gray-200',
          'relative inline-flex flex-shrink-0 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500',
          disabled ? 'opacity-50 cursor-not-allowed' : '',
          sizeClasses.button
        ]"
      &gt;
        &lt;span class="sr-only"&gt;{{ modelValue ? '开启' : '关闭' }}&lt;/span&gt;
        
        &lt;!-- 带内容的开关 --&gt;
        &lt;template v-if="withText || withIcon"&gt;
          &lt;!-- 文字 --&gt;
          &lt;template v-if="withText"&gt;
            &lt;span v-if="!modelValue" class="absolute left-0 h-full flex items-center justify-center text-xs text-gray-900 font-medium ml-1" :class="sizeClasses.textOff"&gt;关&lt;/span&gt;
            &lt;span v-else class="absolute right-0 h-full flex items-center justify-center text-xs text-white font-medium mr-1" :class="sizeClasses.textOn"&gt;开&lt;/span&gt;
          &lt;/template&gt;
          
          &lt;!-- 图标 --&gt;
          &lt;template v-if="withIcon"&gt;
            &lt;span aria-hidden="true" class="pointer-events-none absolute h-full w-full rounded-md bg-white"&gt;&lt;/span&gt;
            &lt;span 
              aria-hidden="true" 
              :class="[
                modelValue ? translateClasses.checked : translateClasses.unchecked,
                'pointer-events-none absolute left-0 inline-block rounded-full bg-white shadow transform ring-0 transition-transform ease-in-out duration-200'
              ]"
              :style="sizeClasses.thumb"
            &gt;
              &lt;span class="absolute inset-0 h-full w-full flex items-center justify-center transition-opacity" :class="modelValue ? 'text-indigo-600' : 'text-gray-400'"&gt;
                &lt;i :class="[modelValue ? 'fas fa-check' : 'fas fa-times', sizeClasses.icon]"&gt;&lt;/i&gt;
              &lt;/span&gt;
            &lt;/span&gt;
          &lt;/template&gt;
        &lt;/template&gt;
        
        &lt;!-- 普通开关 --&gt;
        &lt;span 
          v-else
          :class="[
            modelValue ? translateClasses.checked : translateClasses.unchecked,
            'relative inline-block rounded-full bg-white shadow transform transition-transform ease-in-out duration-200'
          ]"
          :style="sizeClasses.thumb"
        &gt;&lt;/span&gt;
      &lt;/button&gt;
      
      &lt;div v-if="labelPosition === 'right'" class="ml-3"&gt;
        &lt;label v-if="label" class="text-sm font-medium text-gray-900"&gt;{{ label }}&lt;/label&gt;
        &lt;p v-if="description" class="text-xs text-gray-500"&gt;{{ description }}&lt;/p&gt;
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { computed } from 'vue';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  label: String,
  description: String,
  disabled: Boolean,
  size: {
    type: String,
    default: 'default',
    validator: (value) => ['small', 'default', 'large'].includes(value)
  },
  activeColor: {
    type: String,
    default: 'primary',
    validator: (value) => ['primary', 'success', 'warning', 'danger'].includes(value)
  },
  withText: Boolean,
  withIcon: Boolean,
  labelPosition: {
    type: String,
    default: 'right',
    validator: (value) => ['left', 'right'].includes(value)
  }
});

const emit = defineEmits(['update:modelValue', 'change']);

const toggle = () => {
  if (props.disabled) return;
  
  const newValue = !props.modelValue;
  emit('update:modelValue', newValue);
  emit('change', newValue);
};

// 开关颜色类
const activeColorClass = computed(() => {
  switch (props.activeColor) {
    case 'primary': return 'bg-indigo-600';
    case 'success': return 'bg-green-600';
    case 'warning': return 'bg-yellow-500';
    case 'danger': return 'bg-red-600';
    default: return 'bg-indigo-600';
  }
});

// 尺寸类
const sizeClasses = computed(() => {
  switch (props.size) {
    case 'small':
      return {
        button: 'h-4 w-8',
        thumb: { height: '12px', width: '12px' },
        textOn: 'w-5',
        textOff: 'w-5',
        icon: 'text-xs'
      };
    case 'large':
      return {
        button: 'h-8 w-14',
        thumb: { height: '28px', width: '28px' },
        textOn: 'w-8',
        textOff: 'w-8',
        icon: 'text-sm'
      };
    default:
      return {
        button: 'h-6 w-11',
        thumb: { height: '20px', width: '20px' },
        textOn: 'w-6',
        textOff: 'w-6',
        icon: 'text-xs'
      };
  }
});

// 平移类
const translateClasses = computed(() => {
  switch (props.size) {
    case 'small':
      return {
        checked: props.withText ? 'translate-x-4' : 'translate-x-3',
        unchecked: 'translate-x-0'
      };
    case 'large':
      return {
        checked: props.withText ? 'translate-x-6' : 'translate-x-7',
        unchecked: 'translate-x-0'
      };
    default:
      return {
        checked: props.withText ? 'translate-x-5' : 'translate-x-5',
        unchecked: 'translate-x-0'
      };
  }
});
&lt;/script&gt;
                </pre>
            </div>
        </div>
    </div>
</body>
</html>