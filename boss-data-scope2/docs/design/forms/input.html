<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DataScope 输入框组件</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
    <style>
        .component-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #eaeaea;
        }
        .preview-section {
            margin-bottom: 30px;
        }
        .input-demo {
            margin: 10px;
            padding: 10px;
            border: 1px solid #eaeaea;
            border-radius: 8px;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold mb-8 text-center">DataScope 输入框组件</h1>
        
        <div class="bg-white shadow rounded-lg p-6 mb-8">
            <h2 class="component-title">基本输入框</h2>
            
            <div class="input-demo">
                <p class="text-sm text-gray-500 mb-2">默认输入框</p>
                <input type="text" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="请输入内容" style="border: 2px solid #d1d5db;">
            </div>
            
            <div class="input-demo">
                <p class="text-sm text-gray-500 mb-2">带标签输入框</p>
                <label for="email" class="block text-sm font-medium text-gray-700 mb-1">邮箱地址</label>
                <input type="email" name="email" id="email" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="<EMAIL>" style="border: 2px solid #d1d5db;">
            </div>
            
            <div class="input-demo">
                <p class="text-sm text-gray-500 mb-2">带帮助文本的输入框</p>
                <label for="name" class="block text-sm font-medium text-gray-700 mb-1">姓名</label>
                <input type="text" name="name" id="name" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="请输入姓名" style="border: 2px solid #d1d5db;">
                <p class="mt-1 text-xs text-gray-500">请输入您的真实姓名</p>
            </div>
        </div>
        
        <div class="bg-white shadow rounded-lg p-6 mb-8">
            <h2 class="component-title">输入框状态</h2>
            
            <div class="input-demo">
                <p class="text-sm text-gray-500 mb-2">禁用状态</p>
                <input type="text" disabled class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm bg-gray-100 cursor-not-allowed" value="禁用输入框" style="border: 2px solid #d1d5db;">
            </div>
            
            <div class="input-demo">
                <p class="text-sm text-gray-500 mb-2">只读状态</p>
                <input type="text" readonly class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" value="只读输入框" style="border: 2px solid #d1d5db;">
            </div>
            
            <div class="input-demo">
                <p class="text-sm text-gray-500 mb-2">错误状态</p>
                <label for="error-input" class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                <input type="text" name="error-input" id="error-input" class="block w-full px-3 py-2 border border-red-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm" value="admin123" style="border: 2px solid #f87171;">
                <p class="mt-1 text-xs text-red-600">用户名已被占用</p>
            </div>
            
            <div class="input-demo">
                <p class="text-sm text-gray-500 mb-2">成功状态</p>
                <label for="success-input" class="block text-sm font-medium text-gray-700 mb-1">域名</label>
                <input type="text" name="success-input" id="success-input" class="block w-full px-3 py-2 border border-green-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm" value="example.com" style="border: 2px solid #86efac;">
                <p class="mt-1 text-xs text-green-600">该域名可用</p>
            </div>
        </div>
        
        <div class="bg-white shadow rounded-lg p-6 mb-8">
            <h2 class="component-title">输入框尺寸</h2>
            
            <div class="input-demo">
                <p class="text-sm text-gray-500 mb-2">小尺寸</p>
                <input type="text" class="block w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-xs" placeholder="小尺寸输入框" style="border: 2px solid #d1d5db;">
            </div>
            
            <div class="input-demo">
                <p class="text-sm text-gray-500 mb-2">默认尺寸</p>
                <input type="text" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="默认尺寸输入框" style="border: 2px solid #d1d5db;">
            </div>
            
            <div class="input-demo">
                <p class="text-sm text-gray-500 mb-2">大尺寸</p>
                <input type="text" class="block w-full px-4 py-3 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-base" placeholder="大尺寸输入框" style="border: 2px solid #d1d5db;">
            </div>
        </div>
        
        <div class="bg-white shadow rounded-lg p-6 mb-8">
            <h2 class="component-title">带图标的输入框</h2>
            
            <div class="input-demo">
                <p class="text-sm text-gray-500 mb-2">前置图标</p>
                <div class="mt-1 relative rounded-md shadow-sm">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <input type="text" class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="搜索...">
                </div>
            </div>
            
            <div class="input-demo">
                <p class="text-sm text-gray-500 mb-2">后置图标</p>
                <div class="mt-1 relative rounded-md shadow-sm">
                    <input type="text" class="block w-full pl-3 pr-10 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="请输入网址">
                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <i class="fas fa-link text-gray-400"></i>
                    </div>
                </div>
            </div>
            
            <div class="input-demo">
                <p class="text-sm text-gray-500 mb-2">可交互图标</p>
                <div class="mt-1 relative rounded-md shadow-sm">
                    <input type="password" class="block w-full pl-3 pr-10 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="请输入密码">
                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                        <button type="button" class="text-gray-400 hover:text-gray-500 focus:outline-none">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="bg-white shadow rounded-lg p-6 mb-8">
            <h2 class="component-title">特殊输入框</h2>
            
            <div class="input-demo">
                <p class="text-sm text-gray-500 mb-2">文本域</p>
                <textarea rows="3" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="请输入多行文本"></textarea>
            </div>
            
            <div class="input-demo">
                <p class="text-sm text-gray-500 mb-2">数字输入框</p>
                <div class="mt-1 relative rounded-md shadow-sm">
                    <input type="number" min="0" max="100" step="1" class="block w-full pl-3 pr-12 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="0">
                    <div class="absolute inset-y-0 right-0 flex items-center">
                        <div class="h-full flex flex-col">
                            <button type="button" class="h-1/2 w-5 border-l border-gray-300 flex items-center justify-center text-gray-500 hover:text-gray-700 bg-gray-50">
                                <i class="fas fa-chevron-up text-xs"></i>
                            </button>
                            <button type="button" class="h-1/2 w-5 border-l border-t border-gray-300 flex items-center justify-center text-gray-500 hover:text-gray-700 bg-gray-50">
                                <i class="fas fa-chevron-down text-xs"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="input-demo">
                <p class="text-sm text-gray-500 mb-2">密码输入框</p>
                <div class="mt-1 relative rounded-md shadow-sm">
                    <input type="password" class="block w-full pl-3 pr-10 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="请输入密码">
                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                        <button type="button" class="text-gray-400 hover:text-gray-500 focus:outline-none">
                            <i class="fas fa-eye-slash"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="bg-white shadow rounded-lg p-6">
            <h2 class="component-title">输入框组件示例代码</h2>
            <div class="bg-gray-100 p-4 rounded">
                <pre class="text-sm overflow-x-auto">
&lt;template&gt;
  &lt;div class="form-item"&gt;
    &lt;label v-if="label" :for="id" class="block text-sm font-medium text-gray-700 mb-1"&gt;{{ label }}&lt;/label&gt;
    &lt;div class="relative rounded-md shadow-sm"&gt;
      &lt;div v-if="prefixIcon" class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"&gt;
        &lt;i :class="`fas fa-${prefixIcon} text-gray-400`"&gt;&lt;/i&gt;
      &lt;/div&gt;
      
      &lt;input
        :id="id"
        :type="type"
        :value="modelValue"
        @input="$emit('update:modelValue', $event.target.value)"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :class="[
          'block w-full border rounded-md shadow-sm focus:outline-none sm:text-sm',
          prefixIcon ? 'pl-10' : 'pl-3',
          suffixIcon || clearable ? 'pr-10' : 'pr-3',
          sizeClasses,
          statusClasses,
        ]"
      /&gt;
      
      &lt;div v-if="suffixIcon || clearable" class="absolute inset-y-0 right-0 pr-3 flex items-center"&gt;
        &lt;button 
          v-if="clearable && modelValue" 
          type="button" 
          @click="$emit('update:modelValue', '')"
          class="text-gray-400 hover:text-gray-500 focus:outline-none"
        &gt;
          &lt;i class="fas fa-times-circle"&gt;&lt;/i&gt;
        &lt;/button&gt;
        &lt;i v-else-if="suffixIcon" :class="`fas fa-${suffixIcon} text-gray-400`"&gt;&lt;/i&gt;
      &lt;/div&gt;
    &lt;/div&gt;
    
    &lt;p v-if="errorMessage" class="mt-1 text-xs text-red-600"&gt;{{ errorMessage }}&lt;/p&gt;
    &lt;p v-else-if="helpText" class="mt-1 text-xs text-gray-500"&gt;{{ helpText }}&lt;/p&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
defineProps({
  modelValue: {
    type: [String, Number],
    default: ''
  },
  label: String,
  placeholder: String,
  type: {
    type: String,
    default: 'text'
  },
  id: {
    type: String,
    default: () => `input-${Math.random().toString(36).substring(2, 9)}`
  },
  size: {
    type: String,
    default: 'default',
    validator: (value) => ['small', 'default', 'large'].includes(value)
  },
  disabled: Boolean,
  readonly: Boolean,
  prefixIcon: String,
  suffixIcon: String,
  clearable: Boolean,
  status: {
    type: String,
    validator: (value) => ['success', 'error', 'warning', ''].includes(value)
  },
  errorMessage: String,
  helpText: String
});

defineEmits(['update:modelValue']);

const sizeClasses = computed(() => {
  switch (props.size) {
    case 'small': return 'px-2 py-1 text-xs';
    case 'large': return 'px-4 py-3 text-base';
    default: return 'px-3 py-2 text-sm';
  }
});

const statusClasses = computed(() => {
  switch (props.status) {
    case 'success': return 'border-green-300 focus:ring-green-500 focus:border-green-500';
    case 'error': return 'border-red-300 focus:ring-red-500 focus:border-red-500';
    case 'warning': return 'border-yellow-300 focus:ring-yellow-500 focus:border-yellow-500';
    default: return 'border-gray-300 focus:ring-indigo-500 focus:border-indigo-500';
  }
});
&lt;/script&gt;
                </pre>
            </div>
        </div>
    </div>
</body>
</html>