<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DataScope 选择器组件</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
    <style>
        .component-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #eaeaea;
        }
        .preview-section {
            margin-bottom: 30px;
        }
        .demo-item {
            margin: 10px;
            padding: 10px;
            border: 1px solid #eaeaea;
            border-radius: 8px;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold mb-8 text-center">DataScope 选择器组件</h1>
        
        <div class="bg-white shadow rounded-lg p-6 mb-8">
            <h2 class="component-title">基本选择器</h2>
            
            <div class="demo-item">
                <p class="text-sm text-gray-500 mb-2">默认选择器</p>
                <select class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                    <option>请选择选项</option>
                    <option>选项一</option>
                    <option>选项二</option>
                    <option>选项三</option>
                </select>
            </div>
            
            <div class="demo-item">
                <p class="text-sm text-gray-500 mb-2">带标签选择器</p>
                <label for="location" class="block text-sm font-medium text-gray-700 mb-1">地区</label>
                <select id="location" name="location" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                    <option>请选择地区</option>
                    <option>北京</option>
                    <option>上海</option>
                    <option>广州</option>
                    <option>深圳</option>
                </select>
            </div>
            
            <div class="demo-item">
                <p class="text-sm text-gray-500 mb-2">带帮助文本的选择器</p>
                <label for="role" class="block text-sm font-medium text-gray-700 mb-1">角色</label>
                <select id="role" name="role" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                    <option>请选择角色</option>
                    <option>管理员</option>
                    <option>编辑</option>
                    <option>访客</option>
                </select>
                <p class="mt-1 text-xs text-gray-500">不同角色拥有不同的权限等级</p>
            </div>
        </div>
        
        <div class="bg-white shadow rounded-lg p-6 mb-8">
            <h2 class="component-title">选择器状态</h2>
            
            <div class="demo-item">
                <p class="text-sm text-gray-500 mb-2">禁用状态</p>
                <select disabled class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 bg-gray-100 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                    <option>禁用选择器</option>
                    <option>选项一</option>
                    <option>选项二</option>
                </select>
            </div>
            
            <div class="demo-item">
                <p class="text-sm text-gray-500 mb-2">错误状态</p>
                <label for="error-select" class="block text-sm font-medium text-gray-700 mb-1">产品类型</label>
                <select id="error-select" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-red-300 focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm rounded-md">
                    <option>请选择产品类型</option>
                    <option>类型一</option>
                    <option>类型二</option>
                </select>
                <p class="mt-1 text-xs text-red-600">产品类型不能为空</p>
            </div>
        </div>
        
        <div class="bg-white shadow rounded-lg p-6 mb-8">
            <h2 class="component-title">选择器尺寸</h2>
            
            <div class="demo-item">
                <p class="text-sm text-gray-500 mb-2">小尺寸</p>
                <select class="mt-1 block w-full pl-2 pr-8 py-1 text-xs border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 rounded-md">
                    <option>小尺寸选择器</option>
                    <option>选项一</option>
                    <option>选项二</option>
                </select>
            </div>
            
            <div class="demo-item">
                <p class="text-sm text-gray-500 mb-2">默认尺寸</p>
                <select class="mt-1 block w-full pl-3 pr-10 py-2 text-sm border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 rounded-md">
                    <option>默认尺寸选择器</option>
                    <option>选项一</option>
                    <option>选项二</option>
                </select>
            </div>
            
            <div class="demo-item">
                <p class="text-sm text-gray-500 mb-2">大尺寸</p>
                <select class="mt-1 block w-full pl-4 pr-10 py-3 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 rounded-md">
                    <option>大尺寸选择器</option>
                    <option>选项一</option>
                    <option>选项二</option>
                </select>
            </div>
        </div>
        
        <div class="bg-white shadow rounded-lg p-6 mb-8">
            <h2 class="component-title">自定义选择器</h2>
            
            <div class="demo-item">
                <p class="text-sm text-gray-500 mb-2">带图标的选择器</p>
                <div class="mt-1 relative">
                    <select class="block w-full pl-10 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md appearance-none">
                        <option>选择颜色主题</option>
                        <option>明亮模式</option>
                        <option>暗黑模式</option>
                        <option>自动模式</option>
                    </select>
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-palette text-gray-400"></i>
                    </div>
                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <i class="fas fa-chevron-down text-gray-400"></i>
                    </div>
                </div>
            </div>
            
            <div class="demo-item">
                <p class="text-sm text-gray-500 mb-2">分组选择器</p>
                <select class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                    <option>请选择部门</option>
                    <optgroup label="技术部">
                        <option>前端组</option>
                        <option>后端组</option>
                        <option>测试组</option>
                    </optgroup>
                    <optgroup label="产品部">
                        <option>产品经理</option>
                        <option>UI设计师</option>
                        <option>UX设计师</option>
                    </optgroup>
                    <optgroup label="市场部">
                        <option>市场营销</option>
                        <option>销售</option>
                    </optgroup>
                </select>
            </div>
            
            <div class="demo-item">
                <p class="text-sm text-gray-500 mb-2">多选选择器（模拟）</p>
                <div class="mt-1 relative">
                    <div class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus-within:ring-1 focus-within:ring-indigo-500 focus-within:border-indigo-500">
                        <div class="flex flex-wrap gap-2 mb-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-md text-sm font-medium bg-indigo-100 text-indigo-800">
                                JavaScript
                                <button type="button" class="ml-1 inline-flex text-indigo-500 focus:outline-none">
                                    <i class="fas fa-times-circle"></i>
                                </button>
                            </span>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-md text-sm font-medium bg-indigo-100 text-indigo-800">
                                TypeScript
                                <button type="button" class="ml-1 inline-flex text-indigo-500 focus:outline-none">
                                    <i class="fas fa-times-circle"></i>
                                </button>
                            </span>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-md text-sm font-medium bg-indigo-100 text-indigo-800">
                                Vue
                                <button type="button" class="ml-1 inline-flex text-indigo-500 focus:outline-none">
                                    <i class="fas fa-times-circle"></i>
                                </button>
                            </span>
                        </div>
                        <input type="text" class="block w-full p-0 border-0 focus:ring-0 text-sm" placeholder="请选择技术栈">
                    </div>
                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <i class="fas fa-chevron-down text-gray-400"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="bg-white shadow rounded-lg p-6 mb-8">
            <h2 class="component-title">级联选择器（模拟）</h2>
            
            <div class="demo-item">
                <p class="text-sm text-gray-500 mb-2">地区级联选择器</p>
                <div class="flex space-x-4">
                    <div class="w-1/3">
                        <select class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                            <option>选择省份</option>
                            <option selected>广东省</option>
                            <option>江苏省</option>
                            <option>浙江省</option>
                        </select>
                    </div>
                    <div class="w-1/3">
                        <select class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                            <option>选择城市</option>
                            <option>广州市</option>
                            <option selected>深圳市</option>
                            <option>东莞市</option>
                            <option>佛山市</option>
                        </select>
                    </div>
                    <div class="w-1/3">
                        <select class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                            <option>选择区县</option>
                            <option>南山区</option>
                            <option>福田区</option>
                            <option selected>罗湖区</option>
                            <option>宝安区</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="bg-white shadow rounded-lg p-6">
            <h2 class="component-title">选择器组件示例代码</h2>
            <div class="bg-gray-100 p-4 rounded">
                <pre class="text-sm overflow-x-auto">
&lt;template&gt;
  &lt;div class="form-item"&gt;
    &lt;label v-if="label" :for="id" class="block text-sm font-medium text-gray-700 mb-1"&gt;{{ label }}&lt;/label&gt;
    
    &lt;div class="relative rounded-md shadow-sm"&gt;
      &lt;div v-if="prefixIcon" class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"&gt;
        &lt;i :class="`fas fa-${prefixIcon} text-gray-400`"&gt;&lt;/i&gt;
      &lt;/div&gt;
      
      &lt;select
        :id="id"
        :value="modelValue"
        @change="$emit('update:modelValue', $event.target.value)"
        :disabled="disabled"
        :class="[
          'block w-full border rounded-md shadow-sm focus:outline-none appearance-none',
          prefixIcon ? 'pl-10' : 'pl-3',
          'pr-10',
          sizeClasses,
          statusClasses,
        ]"
      &gt;
        &lt;option v-if="placeholder" value="" disabled selected&gt;{{ placeholder }}&lt;/option&gt;
        
        &lt;template v-if="options.length && !hasOptionGroups"&gt;
          &lt;option 
            v-for="option in options" 
            :key="option.value" 
            :value="option.value"
            :disabled="option.disabled"
          &gt;
            {{ option.label }}
          &lt;/option&gt;
        &lt;/template&gt;
        
        &lt;template v-else-if="optionGroups.length"&gt;
          &lt;optgroup 
            v-for="group in optionGroups" 
            :key="group.label" 
            :label="group.label"
          &gt;
            &lt;option 
              v-for="option in group.options" 
              :key="option.value" 
              :value="option.value"
              :disabled="option.disabled"
            &gt;
              {{ option.label }}
            &lt;/option&gt;
          &lt;/optgroup&gt;
        &lt;/template&gt;
        
        &lt;slot&gt;&lt;/slot&gt;
      &lt;/select&gt;
      
      &lt;div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none"&gt;
        &lt;i class="fas fa-chevron-down text-gray-400"&gt;&lt;/i&gt;
      &lt;/div&gt;
    &lt;/div&gt;
    
    &lt;p v-if="errorMessage" class="mt-1 text-xs text-red-600"&gt;{{ errorMessage }}&lt;/p&gt;
    &lt;p v-else-if="helpText" class="mt-1 text-xs text-gray-500"&gt;{{ helpText }}&lt;/p&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { computed } from 'vue';

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: ''
  },
  label: String,
  placeholder: String,
  id: {
    type: String,
    default: () => `select-${Math.random().toString(36).substring(2, 9)}`
  },
  options: {
    type: Array,
    default: () => []
  },
  optionGroups: {
    type: Array,
    default: () => []
  },
  size: {
    type: String,
    default: 'default',
    validator: (value) => ['small', 'default', 'large'].includes(value)
  },
  disabled: Boolean,
  prefixIcon: String,
  status: {
    type: String,
    validator: (value) => ['success', 'error', 'warning', ''].includes(value)
  },
  errorMessage: String,
  helpText: String
});

defineEmits(['update:modelValue']);

const hasOptionGroups = computed(() => props.optionGroups.length > 0);

const sizeClasses = computed(() => {
  switch (props.size) {
    case 'small': return 'py-1 text-xs';
    case 'large': return 'py-3 text-base';
    default: return 'py-2 text-sm';
  }
});

const statusClasses = computed(() => {
  switch (props.status) {
    case 'success': return 'border-green-300 focus:ring-green-500 focus:border-green-500';
    case 'error': return 'border-red-300 focus:ring-red-500 focus:border-red-500';
    case 'warning': return 'border-yellow-300 focus:ring-yellow-500 focus:border-yellow-500';
    default: return 'border-gray-300 focus:ring-indigo-500 focus:border-indigo-500';
  }
});
&lt;/script&gt;
                </pre>
            </div>
        </div>
    </div>
</body>
</html>