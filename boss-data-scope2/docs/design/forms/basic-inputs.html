<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>表单输入控件 - DataScope UI组件库</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
  <style>
    .component-container {
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      padding: 24px;
      margin-bottom: 24px;
    }
    .code-block {
      background-color: #f8fafc;
      border-radius: 6px;
      padding: 16px;
      margin-top: 16px;
      font-family: monospace;
      font-size: 14px;
      white-space: pre-wrap;
      color: #334155;
    }
    .form-control {
      display: block;
      width: 100%;
      padding: 0.5rem 0.75rem;
      font-size: 1rem;
      line-height: 1.5;
      color: #333;
      background-color: #fff;
      background-clip: padding-box;
      border: 2px solid #cbd5e0;
      border-radius: 0.25rem;
      transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }
    .form-control:focus {
      border-color: #4a6cf7;
      outline: 0;
      box-shadow: 0 0 0 0.2rem rgba(74, 108, 247, 0.25);
    }
    .form-control:disabled {
      background-color: #f1f5f9;
      opacity: 1;
    }
    .input-error {
      border-color: #e53e3e;
    }
    .input-error:focus {
      border-color: #e53e3e;
      box-shadow: 0 0 0 0.2rem rgba(229, 62, 62, 0.25);
    }
    .input-success {
      border-color: #38a169;
    }
    .input-success:focus {
      border-color: #38a169;
      box-shadow: 0 0 0 0.2rem rgba(56, 161, 105, 0.25);
    }
    .input-sm {
      padding: 0.25rem 0.5rem;
      font-size: 0.875rem;
      line-height: 1.5;
    }
    .input-lg {
      padding: 0.75rem 1rem;
      font-size: 1.125rem;
      line-height: 1.5;
    }
    .label {
      display: inline-block;
      margin-bottom: 0.5rem;
      font-weight: 500;
      color: #4a5568;
    }
    .form-group {
      margin-bottom: 1rem;
    }
    .help-text {
      display: block;
      margin-top: 0.25rem;
      font-size: 0.875rem;
      color: #718096;
    }
    .error-text {
      display: block;
      margin-top: 0.25rem;
      font-size: 0.875rem;
      color: #e53e3e;
    }
    .success-text {
      display: block;
      margin-top: 0.25rem;
      font-size: 0.875rem;
      color: #38a169;
    }
  </style>
</head>
<body class="bg-gray-50">
  <header class="bg-white border-b border-gray-200 sticky top-0 z-50">
    <div class="container mx-auto px-4 py-4">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <a href="../index.html" class="flex items-center">
                    <h1 class="text-xl font-bold text-gray-900">DataScope UI</h1>
                </a>
            </div>
            <nav class="flex items-center space-x-4">
                <a href="../index.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">首页</a>
                <a href="../components.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">组件</a>
                <a href="../guides/index.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">使用指引</a>
                <a href="../sitemap.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">网站地图</a>
            </nav>
        </div>
    </div>
  </header>

  <div class="flex min-h-screen">
    <!-- 左侧导航菜单 -->
    <div class="w-64 bg-white border-r border-gray-200 overflow-y-auto">
        <div class="p-4">
            <h3 class="text-lg font-semibold text-gray-900 mb-3">组件分类</h3>
            
            <div class="space-y-4">
                <!-- 基础组件 -->
                <div>
                    <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                        <i class="ri-layout-2-line mr-2 text-blue-600"></i>
                        <span>基础组件</span>
                    </h4>
                    <ul class="ml-6 space-y-1">
                        <li>
                            <a href="../basic/buttons.html" class="block text-gray-700 hover:text-indigo-600 py-1">按钮</a>
                        </li>
                        <li>
                            <a href="../basic/icon-buttons.html" class="block text-gray-700 hover:text-indigo-600 py-1">图标按钮</a>
                        </li>
                        <li>
                            <a href="../basic/inputs.html" class="block text-gray-700 hover:text-indigo-600 py-1">输入框</a>
                        </li>
                        <li>
                            <a href="../basic/badges.html" class="block text-gray-700 hover:text-indigo-600 py-1">状态标签</a>
                        </li>
                        <li>
                            <a href="../basic/index.html" class="block text-gray-700 hover:text-indigo-600 py-1">全部基础组件</a>
                        </li>
                    </ul>
                </div>
                
                <!-- 表单组件 -->
                <div>
                    <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                        <i class="ri-file-list-3-line mr-2 text-green-600"></i>
                        <span>表单组件</span>
                    </h4>
                    <ul class="ml-6 space-y-1">
                        <li>
                            <a href="./basic-inputs.html" class="block text-gray-700 hover:text-indigo-600 py-1 font-medium text-indigo-600">基础输入</a>
                        </li>
                        <li>
                            <a href="./selectors.html" class="block text-gray-700 hover:text-indigo-600 py-1">选择器</a>
                        </li>
                        <li>
                            <a href="./toggles.html" class="block text-gray-700 hover:text-indigo-600 py-1">开关控件</a>
                        </li>
                        <li>
                            <a href="./date-picker.html" class="block text-gray-700 hover:text-indigo-600 py-1">日期选择器</a>
                        </li>
                        <li>
                            <a href="./index.html" class="block text-gray-700 hover:text-indigo-600 py-1">全部表单组件</a>
                        </li>
                    </ul>
                </div>
                
                <!-- 容器组件 -->
                <div>
                    <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                        <i class="ri-layout-masonry-line mr-2 text-purple-600"></i>
                        <span>容器组件</span>
                    </h4>
                    <ul class="ml-6 space-y-1">
                        <li>
                            <a href="../container/cards.html" class="block text-gray-700 hover:text-indigo-600 py-1">卡片容器</a>
                        </li>
                        <li>
                            <a href="../container/panels.html" class="block text-gray-700 hover:text-indigo-600 py-1">面板容器</a>
                        </li>
                        <li>
                            <a href="../container/boxes.html" class="block text-gray-700 hover:text-indigo-600 py-1">盒子容器</a>
                        </li>
                        <li>
                            <a href="../container/index.html" class="block text-gray-700 hover:text-indigo-600 py-1">全部容器组件</a>
                        </li>
                    </ul>
                </div>
                
                <!-- 数据展示 -->
                <div>
                    <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                        <i class="ri-table-line mr-2 text-yellow-600"></i>
                        <span>数据展示</span>
                    </h4>
                    <ul class="ml-6 space-y-1">
                        <li>
                            <a href="../display/data-table.html" class="block text-gray-700 hover:text-indigo-600 py-1">数据表格</a>
                        </li>
                        <li>
                            <a href="../display/action-buttons.html" class="block text-gray-700 hover:text-indigo-600 py-1">操作按钮</a>
                        </li>
                        <li>
                            <a href="../display/page-header.html" class="block text-gray-700 hover:text-indigo-600 py-1">页面标题</a>
                        </li>
                        <li>
                            <a href="../display/index.html" class="block text-gray-700 hover:text-indigo-600 py-1">全部数据展示</a>
                        </li>
                    </ul>
                </div>
                
                <!-- 反馈组件 -->
                <div>
                    <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                        <i class="ri-notification-3-line mr-2 text-indigo-600"></i>
                        <span>反馈组件</span>
                    </h4>
                    <ul class="ml-6 space-y-1">
                        <li>
                            <a href="../feedback/message.html" class="block text-gray-700 hover:text-indigo-600 py-1">消息提示</a>
                        </li>
                        <li>
                            <a href="../feedback/notification.html" class="block text-gray-700 hover:text-indigo-600 py-1">通知提醒</a>
                        </li>
                        <li>
                            <a href="../feedback/dialog.html" class="block text-gray-700 hover:text-indigo-600 py-1">对话框</a>
                        </li>
                        <li>
                            <a href="../feedback/alert.html" class="block text-gray-700 hover:text-indigo-600 py-1">警告提示</a>
                        </li>
                        <li>
                            <a href="../feedback/index.html" class="block text-gray-700 hover:text-indigo-600 py-1">全部反馈组件</a>
                        </li>
                    </ul>
                </div>
                
                <!-- 动画效果 -->
                <div>
                    <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                        <i class="ri-slideshow-3-line mr-2 text-pink-600"></i>
                        <span>动画效果</span>
                    </h4>
                    <ul class="ml-6 space-y-1">
                        <li>
                            <a href="../animations/transitions.html" class="block text-gray-700 hover:text-indigo-600 py-1">过渡动画</a>
                        </li>
                        <li>
                            <a href="../animations/loading.html" class="block text-gray-700 hover:text-indigo-600 py-1">加载动画</a>
                        </li>
                        <li>
                            <a href="../animations/index.html" class="block text-gray-700 hover:text-indigo-600 py-1">全部动画效果</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 右侧内容区 -->
    <div class="flex-1 p-6 overflow-y-auto">
      <div class="container mx-auto px-4 py-8">
        <div class="flex items-center mb-8">
            <a href="../index.html" class="text-gray-500 hover:text-indigo-600 mr-2">
                <i class="ri-home-line"></i>
            </a>
            <span class="text-gray-400 mx-2">/</span>
            <a href="../components.html" class="text-gray-500 hover:text-indigo-600 mr-2">
                组件
            </a>
            <span class="text-gray-400 mx-2">/</span>
            <a href="./index.html" class="text-gray-500 hover:text-indigo-600 mr-2">
                表单组件
            </a>
            <span class="text-gray-400 mx-2">/</span>
            <span class="text-gray-900 font-medium">表单输入控件</span>
        </div>

        <div class="mb-8">
            <h2 class="text-3xl font-bold mb-4 text-gray-900">表单输入控件</h2>
            <p class="text-lg text-gray-600 max-w-4xl">
              表单输入控件是用户与应用程序交互的主要方式，提供了输入和编辑信息的界面。这些控件应当具有一致的视觉风格和行为模式，以提供良好的用户体验。
            </p>
        </div>

        <!-- 基础文本输入框 -->
        <div class="component-container bg-white shadow-sm">
          <h2 class="text-xl font-semibold mb-4 text-gray-800">基础文本输入框</h2>
          <p class="text-gray-600 mb-6">
            基础文本输入框用于单行文本内容的输入，支持各种状态和尺寸变化。
          </p>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="form-group">
              <label class="label">默认输入框</label>
              <input type="text" class="form-control" placeholder="请输入内容">
            </div>
            <div class="form-group">
              <label class="label">带默认值的输入框</label>
              <input type="text" class="form-control" value="默认内容">
            </div>
            <div class="form-group">
              <label class="label">禁用状态</label>
              <input type="text" class="form-control" placeholder="禁用状态" disabled>
            </div>
            <div class="form-group">
              <label class="label">只读状态</label>
              <input type="text" class="form-control" value="只读内容" readonly>
            </div>
            <div class="form-group">
              <label class="label">错误状态</label>
              <input type="text" class="form-control input-error" value="错误内容">
              <span class="error-text">请输入有效的内容</span>
            </div>
            <div class="form-group">
              <label class="label">成功状态</label>
              <input type="text" class="form-control input-success" value="成功内容">
              <span class="success-text">验证通过</span>
            </div>
          </div>

          <div class="mt-8">
            <h3 class="text-lg font-medium mb-4 text-gray-700">不同尺寸</h3>
            <div class="space-y-4">
              <div class="form-group">
                <label class="label">小尺寸输入框</label>
                <input type="text" class="form-control input-sm" placeholder="小尺寸">
              </div>
              <div class="form-group">
                <label class="label">中尺寸输入框（默认）</label>
                <input type="text" class="form-control" placeholder="中尺寸">
              </div>
              <div class="form-group">
                <label class="label">大尺寸输入框</label>
                <input type="text" class="form-control input-lg" placeholder="大尺寸">
              </div>
            </div>
          </div>

          <div class="code-block">
    <!-- 基础输入框示例 -->
    <template>
      <!-- 默认输入框 -->
      <FormItem label="用户名">
        <Input v-model="username" placeholder="请输入用户名" />
      </FormItem>

      <!-- 禁用状态 -->
      <FormItem label="账号">
        <Input v-model="account" disabled />
      </FormItem>

      <!-- 错误状态 -->
      <FormItem label="邮箱" error="请输入有效的邮箱地址">
        <Input v-model="email" status="error" />
      </FormItem>

      <!-- 成功状态 -->
      <FormItem label="验证码" success="验证码正确">
        <Input v-model="code" status="success" />
      </FormItem>

      <!-- 不同尺寸 -->
      <Input v-model="value" size="sm" placeholder="小尺寸" />
      <Input v-model="value" placeholder="默认尺寸" />
      <Input v-model="value" size="lg" placeholder="大尺寸" />
    </template>
          </div>
        </div>

        <!-- 带图标的输入框 -->
        <div class="component-container bg-white shadow-sm">
          <h2 class="text-xl font-semibold mb-4 text-gray-800">带图标的输入框</h2>
          <p class="text-gray-600 mb-6">
            在输入框内添加图标可以增强视觉提示，帮助用户理解输入框的用途。
          </p>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="form-group">
              <label class="label">前置图标</label>
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <i class="ri-user-line text-gray-400"></i>
                </div>
                <input type="text" class="form-control pl-10" placeholder="请输入用户名">
              </div>
            </div>
            <div class="form-group">
              <label class="label">后置图标</label>
              <div class="relative">
                <input type="text" class="form-control pr-10" placeholder="请输入搜索内容">
                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <i class="ri-search-line text-gray-400"></i>
                </div>
              </div>
            </div>
            <div class="form-group">
              <label class="label">可点击图标（清除）</label>
              <div class="relative">
                <input type="text" class="form-control pr-10" value="可清除的内容">
                <div class="absolute inset-y-0 right-0 pr-3 flex items-center cursor-pointer">
                  <i class="ri-close-circle-line text-gray-400 hover:text-gray-600"></i>
                </div>
              </div>
            </div>
            <div class="form-group">
              <label class="label">带前后缀图标</label>
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <i class="ri-lock-line text-gray-400"></i>
                </div>
                <input type="password" class="form-control pl-10 pr-10" placeholder="请输入密码">
                <div class="absolute inset-y-0 right-0 pr-3 flex items-center cursor-pointer">
                  <i class="ri-eye-off-line text-gray-400 hover:text-gray-600"></i>
                </div>
              </div>
            </div>
          </div>

          <div class="code-block">
    <!-- 带图标的输入框示例 -->
    <template>
      <!-- 前置图标 -->
      <Input v-model="username" placeholder="请输入用户名" prefix-icon="user-line" />

      <!-- 后置图标 -->
      <Input v-model="search" placeholder="请输入搜索内容" suffix-icon="search-line" />

      <!-- 可清除输入 -->
      <Input v-model="content" placeholder="输入内容后可清除" clearable />

      <!-- 密码输入框 -->
      <Input v-model="password" type="password" prefix-icon="lock-line" password-toggle />
    </template>
          </div>
        </div>

        <!-- 文本域 -->
        <div class="component-container bg-white shadow-sm">
          <h2 class="text-xl font-semibold mb-4 text-gray-800">文本域</h2>
          <p class="text-gray-600 mb-6">
            文本域用于多行文本内容的输入，如备注、描述等。
          </p>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="form-group">
              <label class="label">基础文本域</label>
              <textarea class="form-control" rows="4" placeholder="请输入多行内容"></textarea>
            </div>
            <div class="form-group">
              <label class="label">禁用状态</label>
              <textarea class="form-control" rows="4" placeholder="禁用状态" disabled></textarea>
            </div>
            <div class="form-group">
              <label class="label">限制字数</label>
              <textarea class="form-control" rows="4" placeholder="请输入内容，最多200字"></textarea>
              <div class="flex justify-end mt-1">
                <span class="text-sm text-gray-500">0/200</span>
              </div>
            </div>
            <div class="form-group">
              <label class="label">自动调整高度</label>
              <textarea class="form-control" rows="2" placeholder="输入更多内容会自动扩展高度"></textarea>
              <span class="help-text">此文本域会根据内容自动调整高度</span>
            </div>
          </div>

          <div class="code-block">
    <!-- 文本域示例 -->
    <template>
      <!-- 基础文本域 -->
      <FormItem label="备注">
        <TextArea v-model="remark" placeholder="请输入备注信息" :rows="4" />
      </FormItem>

      <!-- 字数限制 -->
      <FormItem label="描述">
        <TextArea v-model="description" placeholder="请输入描述" :max-length="200" show-count />
      </FormItem>

      <!-- 自动调整高度 -->
      <FormItem label="内容">
        <TextArea v-model="content" auto-size placeholder="输入内容会自动调整高度" />
      </FormItem>
    </template>
          </div>
        </div>

        <!-- 密码输入框 -->
        <div class="component-container bg-white shadow-sm">
          <h2 class="text-xl font-semibold mb-4 text-gray-800">密码输入框</h2>
          <p class="text-gray-600 mb-6">
            密码输入框用于输入敏感信息，可以切换密码的可见性。
          </p>

          <div class="space-y-4">
            <div class="form-group">
              <label class="label">基础密码输入框</label>
              <input type="password" class="form-control" placeholder="请输入密码">
            </div>
            <div class="form-group">
              <label class="label">带切换显示功能</label>
              <div class="relative">
                <input type="password" class="form-control pr-10" placeholder="请输入密码">
                <div class="absolute inset-y-0 right-0 pr-3 flex items-center cursor-pointer">
                  <i class="ri-eye-off-line text-gray-400 hover:text-gray-600"></i>
                </div>
              </div>
            </div>
            <div class="form-group">
              <label class="label">带强度指示器</label>
              <input type="password" class="form-control mb-2" placeholder="请输入密码">
              <div class="h-1 bg-gray-200 rounded-full overflow-hidden">
                <div class="h-full bg-yellow-500 w-1/2"></div>
              </div>
              <span class="text-sm text-yellow-600 mt-1 inline-block">密码强度：中</span>
            </div>
          </div>

          <div class="code-block">
    <!-- 密码输入框示例 -->
    <template>
      <!-- 基础密码框 -->
      <FormItem label="密码">
        <Input v-model="password" type="password" placeholder="请输入密码" />
      </FormItem>

      <!-- 带显示切换 -->
      <FormItem label="密码">
        <Input v-model="password" type="password" placeholder="请输入密码" password-toggle />
      </FormItem>

      <!-- 带强度指示 -->
      <FormItem label="设置密码">
        <PasswordInput 
          v-model="password" 
          placeholder="请输入密码" 
          show-strength 
          @strength-change="onStrengthChange" 
        />
      </FormItem>
    </template>
          </div>
        </div>

        <!-- 数字输入框 -->
        <div class="component-container bg-white shadow-sm">
          <h2 class="text-xl font-semibold mb-4 text-gray-800">数字输入框</h2>
          <p class="text-gray-600 mb-6">
            数字输入框用于输入数值，支持步进器和精度控制。
          </p>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="form-group">
              <label class="label">基础数字输入框</label>
              <div class="relative">
                <input type="number" class="form-control" placeholder="请输入数字">
              </div>
            </div>
            <div class="form-group">
              <label class="label">带步进器</label>
              <div class="flex">
                <button class="px-3 bg-gray-100 border-2 border-r-0 border-gray-300 rounded-l-md flex items-center justify-center hover:bg-gray-200">
                  <i class="ri-subtract-line"></i>
                </button>
                <input type="number" class="form-control rounded-none border-l-0 border-r-0 text-center" value="1">
                <button class="px-3 bg-gray-100 border-2 border-l-0 border-gray-300 rounded-r-md flex items-center justify-center hover:bg-gray-200">
                  <i class="ri-add-line"></i>
                </button>
              </div>
            </div>
            <div class="form-group">
              <label class="label">限制范围</label>
              <div class="flex">
                <button class="px-3 bg-gray-100 border-2 border-r-0 border-gray-300 rounded-l-md flex items-center justify-center hover:bg-gray-200">
                  <i class="ri-subtract-line"></i>
                </button>
                <input type="number" class="form-control rounded-none border-l-0 border-r-0 text-center" value="5" min="0" max="10">
                <button class="px-3 bg-gray-100 border-2 border-l-0 border-gray-300 rounded-r-md flex items-center justify-center hover:bg-gray-200">
                  <i class="ri-add-line"></i>
                </button>
              </div>
              <span class="help-text">范围：0-10</span>
            </div>
            <div class="form-group">
              <label class="label">精度控制</label>
              <div class="flex">
                <button class="px-3 bg-gray-100 border-2 border-r-0 border-gray-300 rounded-l-md flex items-center justify-center hover:bg-gray-200">
                  <i class="ri-subtract-line"></i>
                </button>
                <input type="number" class="form-control rounded-none border-l-0 border-r-0 text-center" value="1.50" step="0.1">
                <button class="px-3 bg-gray-100 border-2 border-l-0 border-gray-300 rounded-r-md flex items-center justify-center hover:bg-gray-200">
                  <i class="ri-add-line"></i>
                </button>
              </div>
              <span class="help-text">步进值：0.1</span>
            </div>
          </div>

          <div class="code-block">
    <!-- 数字输入框示例 -->
    <template>
      <!-- 基础数字输入 -->
      <FormItem label="数量">
        <NumberInput v-model="quantity" placeholder="请输入数量" />
      </FormItem>

      <!-- 带步进器和范围限制 -->
      <FormItem label="数量">
        <NumberInput v-model="quantity" :min="0" :max="10" step-controls />
      </FormItem>

      <!-- 精度控制 -->
      <FormItem label="金额">
        <NumberInput v-model="amount" :min="0" :precision="2" :step="0.1" step-controls />
      </FormItem>
    </template>
          </div>
        </div>

        <!-- 应用场景 -->
        <div class="component-container bg-white shadow-sm">
          <h2 class="text-xl font-semibold mb-4 text-gray-800">应用场景示例</h2>
          <p class="text-gray-600 mb-6">
            以下是输入控件在常见表单场景中的应用示例。
          </p>

          <div class="mb-8">
            <h3 class="text-lg font-medium mb-4 text-gray-700">登录表单</h3>
            <div class="max-w-md mx-auto bg-white p-6 rounded-lg shadow">
              <h4 class="text-xl font-semibold text-center mb-6">用户登录</h4>
              <div class="space-y-4">
                <div class="form-group">
                  <label class="label">用户名</label>
                  <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <i class="ri-user-line text-gray-400"></i>
                    </div>
                    <input type="text" class="form-control pl-10" placeholder="请输入用户名">
                  </div>
                </div>
                <div class="form-group">
                  <label class="label">密码</label>
                  <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <i class="ri-lock-line text-gray-400"></i>
                    </div>
                    <input type="password" class="form-control pl-10 pr-10" placeholder="请输入密码">
                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center cursor-pointer">
                      <i class="ri-eye-off-line text-gray-400 hover:text-gray-600"></i>
                    </div>
                  </div>
                </div>
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <input type="checkbox" id="remember-me" class="h-4 w-4 text-blue-600 border-gray-300 rounded">
                    <label for="remember-me" class="ml-2 block text-sm text-gray-700">记住我</label>
                  </div>
                  <a href="#" class="text-sm text-blue-600 hover:text-blue-800">忘记密码？</a>
                </div>
                <button class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out">
                  登录
                </button>
              </div>
            </div>
          </div>

          <div class="mb-8">
            <h3 class="text-lg font-medium mb-4 text-gray-700">搜索表单</h3>
            <div class="p-4 bg-white rounded-lg shadow">
              <div class="flex flex-wrap -mx-2">
                <div class="px-2 w-full md:w-1/3 mb-4">
                  <div class="form-group mb-0">
                    <label class="label">关键词</label>
                    <div class="relative">
                      <input type="text" class="form-control pr-10" placeholder="请输入搜索关键词">
                      <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                        <i class="ri-search-line text-gray-400"></i>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="px-2 w-full md:w-1/3 mb-4">
                  <div class="form-group mb-0">
                    <label class="label">类型</label>
                    <select class="form-control">
                      <option value="">全部类型</option>
                      <option value="1">类型一</option>
                      <option value="2">类型二</option>
                      <option value="3">类型三</option>
                    </select>
                  </div>
                </div>
                <div class="px-2 w-full md:w-1/3 mb-4">
                  <div class="form-group mb-0">
                    <label class="label">日期范围</label>
                    <input type="text" class="form-control" placeholder="选择日期范围">
                  </div>
                </div>
                <div class="px-2 w-full flex justify-end">
                  <button class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out mr-2">
                    搜索
                  </button>
                  <button class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out">
                    重置
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>
</html>