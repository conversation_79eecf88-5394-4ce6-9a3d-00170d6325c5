<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DataScope 复选框和单选框组件</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
    <style>
        .component-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #eaeaea;
        }
        .preview-section {
            margin-bottom: 30px;
        }
        .demo-item {
            margin: 10px;
            padding: 10px;
            border: 1px solid #eaeaea;
            border-radius: 8px;
        }
        
        /* 自定义复选框样式 */
        .custom-checkbox {
            position: relative;
            padding-left: 28px;
            cursor: pointer;
            display: block;
            line-height: 20px;
        }
        
        .custom-checkbox input {
            position: absolute;
            opacity: 0;
            cursor: pointer;
            height: 0;
            width: 0;
        }
        
        .checkmark {
            position: absolute;
            top: 0;
            left: 0;
            height: 20px;
            width: 20px;
            background-color: #fff;
            border: 1px solid #d1d5db;
            border-radius: 4px;
        }
        
        .custom-checkbox:hover input ~ .checkmark {
            background-color: #f3f4f6;
        }
        
        .custom-checkbox input:checked ~ .checkmark {
            background-color: #4f46e5;
            border-color: #4f46e5;
        }
        
        .checkmark:after {
            content: "";
            position: absolute;
            display: none;
        }
        
        .custom-checkbox input:checked ~ .checkmark:after {
            display: block;
        }
        
        .custom-checkbox .checkmark:after {
            left: 7px;
            top: 3px;
            width: 6px;
            height: 10px;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
        }
        
        /* 自定义单选框样式 */
        .custom-radio {
            position: relative;
            padding-left: 28px;
            cursor: pointer;
            display: block;
            line-height: 20px;
        }
        
        .custom-radio input {
            position: absolute;
            opacity: 0;
            cursor: pointer;
            height: 0;
            width: 0;
        }
        
        .radiomark {
            position: absolute;
            top: 0;
            left: 0;
            height: 20px;
            width: 20px;
            background-color: #fff;
            border: 1px solid #d1d5db;
            border-radius: 50%;
        }
        
        .custom-radio:hover input ~ .radiomark {
            background-color: #f3f4f6;
        }
        
        .custom-radio input:checked ~ .radiomark {
            background-color: #fff;
            border-color: #4f46e5;
        }
        
        .radiomark:after {
            content: "";
            position: absolute;
            display: none;
        }
        
        .custom-radio input:checked ~ .radiomark:after {
            display: block;
        }
        
        .custom-radio .radiomark:after {
            top: 5px;
            left: 5px;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #4f46e5;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold mb-8 text-center">DataScope 复选框和单选框组件</h1>
        
        <!-- 复选框部分 -->
        <div class="bg-white shadow rounded-lg p-6 mb-8">
            <h2 class="component-title">基本复选框</h2>
            
            <div class="demo-item">
                <p class="text-sm text-gray-500 mb-2">默认复选框</p>
                <div class="flex flex-col space-y-3">
                    <div class="flex items-center">
                        <input id="checkbox-1" name="checkbox-1" type="checkbox" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                        <label for="checkbox-1" class="ml-2 block text-sm text-gray-900">默认选项</label>
                    </div>
                    <div class="flex items-center">
                        <input id="checkbox-2" name="checkbox-2" type="checkbox" checked class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                        <label for="checkbox-2" class="ml-2 block text-sm text-gray-900">选中状态</label>
                    </div>
                    <div class="flex items-center">
                        <input id="checkbox-3" name="checkbox-3" type="checkbox" disabled class="h-4 w-4 text-gray-400 focus:ring-gray-400 border-gray-300 rounded cursor-not-allowed">
                        <label for="checkbox-3" class="ml-2 block text-sm text-gray-400">禁用状态</label>
                    </div>
                    <div class="flex items-center">
                        <input id="checkbox-4" name="checkbox-4" type="checkbox" checked disabled class="h-4 w-4 text-gray-400 focus:ring-gray-400 border-gray-300 rounded cursor-not-allowed">
                        <label for="checkbox-4" class="ml-2 block text-sm text-gray-400">禁用选中</label>
                    </div>
                </div>
            </div>
            
            <div class="demo-item">
                <p class="text-sm text-gray-500 mb-2">自定义样式复选框</p>
                <div class="flex flex-col space-y-3">
                    <label class="custom-checkbox text-sm text-gray-900">
                        默认选项
                        <input type="checkbox">
                        <span class="checkmark"></span>
                    </label>
                    <label class="custom-checkbox text-sm text-gray-900">
                        选中状态
                        <input type="checkbox" checked>
                        <span class="checkmark"></span>
                    </label>
                    <label class="custom-checkbox text-sm text-gray-400">
                        禁用状态
                        <input type="checkbox" disabled>
                        <span class="checkmark opacity-60"></span>
                    </label>
                </div>
            </div>
            
            <div class="demo-item">
                <p class="text-sm text-gray-500 mb-2">复选框组</p>
                <fieldset>
                    <legend class="text-base font-medium text-gray-900 mb-2">选择技术栈</legend>
                    <div class="flex flex-wrap gap-4">
                        <div class="flex items-center">
                            <input id="js" name="tech-stack" type="checkbox" checked class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                            <label for="js" class="ml-2 block text-sm text-gray-900">JavaScript</label>
                        </div>
                        <div class="flex items-center">
                            <input id="ts" name="tech-stack" type="checkbox" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                            <label for="ts" class="ml-2 block text-sm text-gray-900">TypeScript</label>
                        </div>
                        <div class="flex items-center">
                            <input id="vue" name="tech-stack" type="checkbox" checked class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                            <label for="vue" class="ml-2 block text-sm text-gray-900">Vue</label>
                        </div>
                        <div class="flex items-center">
                            <input id="react" name="tech-stack" type="checkbox" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                            <label for="react" class="ml-2 block text-sm text-gray-900">React</label>
                        </div>
                    </div>
                </fieldset>
            </div>
        </div>
        
        <div class="bg-white shadow rounded-lg p-6 mb-8">
            <h2 class="component-title">复选框变体</h2>
            
            <div class="demo-item">
                <p class="text-sm text-gray-500 mb-2">带边框复选框</p>
                <div class="flex flex-col space-y-3">
                    <div class="relative flex items-start">
                        <div class="flex items-center h-5">
                            <input id="bordered-1" name="bordered-1" type="checkbox" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                        </div>
                        <div class="ml-3 -mt-0.5 border border-gray-200 rounded-md p-3 w-full hover:border-indigo-300">
                            <label for="bordered-1" class="font-medium text-gray-700 block text-sm">基本计划</label>
                            <p class="text-gray-500 text-xs mt-1">适合个人使用，包含基本功能</p>
                        </div>
                    </div>
                    <div class="relative flex items-start">
                        <div class="flex items-center h-5">
                            <input id="bordered-2" name="bordered-2" type="checkbox" checked class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                        </div>
                        <div class="ml-3 -mt-0.5 border border-indigo-300 rounded-md p-3 w-full bg-indigo-50">
                            <label for="bordered-2" class="font-medium text-gray-700 block text-sm">专业计划</label>
                            <p class="text-gray-500 text-xs mt-1">适合团队使用，包含全部功能</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="demo-item">
                <p class="text-sm text-gray-500 mb-2">开关式复选框</p>
                <div class="flex flex-col space-y-3">
                    <div class="flex items-center">
                        <button type="button" class="bg-gray-200 relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2" role="switch" aria-checked="false">
                            <span class="translate-x-0 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"></span>
                        </button>
                        <span class="ml-3 text-sm text-gray-900">关闭状态</span>
                    </div>
                    <div class="flex items-center">
                        <button type="button" class="bg-indigo-600 relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2" role="switch" aria-checked="true">
                            <span class="translate-x-5 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"></span>
                        </button>
                        <span class="ml-3 text-sm text-gray-900">开启状态</span>
                    </div>
                    <div class="flex items-center">
                        <button type="button" class="bg-gray-200 relative inline-flex h-6 w-11 flex-shrink-0 cursor-not-allowed rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none opacity-60" role="switch" aria-checked="false" disabled>
                            <span class="translate-x-0 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"></span>
                        </button>
                        <span class="ml-3 text-sm text-gray-400">禁用状态</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 单选框部分 -->
        <div class="bg-white shadow rounded-lg p-6 mb-8">
            <h2 class="component-title">基本单选框</h2>
            
            <div class="demo-item">
                <p class="text-sm text-gray-500 mb-2">默认单选框</p>
                <div class="flex flex-col space-y-3">
                    <div class="flex items-center">
                        <input id="radio-1" name="default-radio" type="radio" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300">
                        <label for="radio-1" class="ml-2 block text-sm text-gray-900">选项一</label>
                    </div>
                    <div class="flex items-center">
                        <input id="radio-2" name="default-radio" type="radio" checked class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300">
                        <label for="radio-2" class="ml-2 block text-sm text-gray-900">选项二（选中）</label>
                    </div>
                    <div class="flex items-center">
                        <input id="radio-3" name="disabled-radio" type="radio" disabled class="h-4 w-4 text-gray-400 focus:ring-gray-400 border-gray-300 cursor-not-allowed">
                        <label for="radio-3" class="ml-2 block text-sm text-gray-400">禁用状态</label>
                    </div>
                    <div class="flex items-center">
                        <input id="radio-4" name="disabled-radio" type="radio" checked disabled class="h-4 w-4 text-gray-400 focus:ring-gray-400 border-gray-300 cursor-not-allowed">
                        <label for="radio-4" class="ml-2 block text-sm text-gray-400">禁用选中</label>
                    </div>
                </div>
            </div>
            
            <div class="demo-item">
                <p class="text-sm text-gray-500 mb-2">自定义样式单选框</p>
                <div class="flex flex-col space-y-3">
                    <label class="custom-radio text-sm text-gray-900">
                        选项一
                        <input type="radio" name="custom-radio">
                        <span class="radiomark"></span>
                    </label>
                    <label class="custom-radio text-sm text-gray-900">
                        选项二（选中）
                        <input type="radio" name="custom-radio" checked>
                        <span class="radiomark"></span>
                    </label>
                    <label class="custom-radio text-sm text-gray-400">
                        禁用状态
                        <input type="radio" name="custom-disabled" disabled>
                        <span class="radiomark opacity-60"></span>
                    </label>
                </div>
            </div>
        </div>
        
        <div class="bg-white shadow rounded-lg p-6 mb-8">
            <h2 class="component-title">单选框变体</h2>
            
            <div class="demo-item">
                <p class="text-sm text-gray-500 mb-2">带边框单选框</p>
                <div class="flex flex-col space-y-3">
                    <div class="relative flex items-start">
                        <div class="flex items-center h-5">
                            <input id="radio-border-1" name="radio-border" type="radio" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300">
                        </div>
                        <div class="ml-3 -mt-0.5 border border-gray-200 rounded-md p-3 w-full hover:border-indigo-300">
                            <label for="radio-border-1" class="font-medium text-gray-700 block text-sm">月付计划</label>
                            <p class="text-gray-500 text-xs mt-1">$9.99/月，灵活取消</p>
                        </div>
                    </div>
                    <div class="relative flex items-start">
                        <div class="flex items-center h-5">
                            <input id="radio-border-2" name="radio-border" type="radio" checked class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300">
                        </div>
                        <div class="ml-3 -mt-0.5 border border-indigo-300 rounded-md p-3 w-full bg-indigo-50">
                            <label for="radio-border-2" class="font-medium text-gray-700 block text-sm">年付计划（省20%）</label>
                            <p class="text-gray-500 text-xs mt-1">$95.88/年，相当于$7.99/月</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="demo-item">
                <p class="text-sm text-gray-500 mb-2">卡片式单选框</p>
                <div class="grid grid-cols-3 gap-4">
                    <div>
                        <input type="radio" id="card-1" name="card-radio" class="hidden peer" checked>
                        <label for="card-1" class="block p-4 cursor-pointer text-center border rounded-lg peer-checked:border-indigo-500 peer-checked:bg-indigo-50 hover:bg-gray-50">
                            <div class="w-full text-lg font-semibold">入门版</div>
                            <div class="w-full text-sm text-gray-500">适合个人使用</div>
                            <div class="w-full text-2xl font-bold mt-2">免费</div>
                        </label>
                    </div>
                    <div>
                        <input type="radio" id="card-2" name="card-radio" class="hidden peer">
                        <label for="card-2" class="block p-4 cursor-pointer text-center border rounded-lg peer-checked:border-indigo-500 peer-checked:bg-indigo-50 hover:bg-gray-50">
                            <div class="w-full text-lg font-semibold">标准版</div>
                            <div class="w-full text-sm text-gray-500">适合小团队</div>
                            <div class="w-full text-2xl font-bold mt-2">$19.99</div>
                        </label>
                    </div>
                    <div>
                        <input type="radio" id="card-3" name="card-radio" class="hidden peer">
                        <label for="card-3" class="block p-4 cursor-pointer text-center border rounded-lg peer-checked:border-indigo-500 peer-checked:bg-indigo-50 hover:bg-gray-50">
                            <div class="w-full text-lg font-semibold">企业版</div>
                            <div class="w-full text-sm text-gray-500">适合大型企业</div>
                            <div class="w-full text-2xl font-bold mt-2">$49.99</div>
                        </label>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="bg-white shadow rounded-lg p-6">
            <h2 class="component-title">复选框和单选框组件示例代码</h2>
            <div class="bg-gray-100 p-4 rounded">
                <pre class="text-sm overflow-x-auto">
&lt;template&gt;
  &lt;div class="form-item"&gt;
    &lt;fieldset&gt;
      &lt;legend v-if="label" class="text-sm font-medium text-gray-700 mb-2"&gt;{{ label }}&lt;/legend&gt;
      
      &lt;div :class="['flex', direction === 'horizontal' ? 'flex-row gap-4' : 'flex-col gap-2']"&gt;
        &lt;div v-for="(option, index) in options" :key="index" :class="['flex items-center', {'opacity-60': option.disabled}]"&gt;
          &lt;input
            :id="`${id}-${index}`"
            :name="name"
            :type="type"
            :value="option.value"
            :checked="isChecked(option.value)"
            :disabled="option.disabled"
            @change="handleChange(option.value)"
            :class="[
              type === 'checkbox' ? 'rounded' : '',
              'h-4 w-4 focus:ring-indigo-500 border-gray-300',
              option.disabled ? 'text-gray-400 cursor-not-allowed' : 'text-indigo-600 cursor-pointer'
            ]"
          /&gt;
          &lt;label 
            :for="`${id}-${index}`" 
            :class="[
              'ml-2 block text-sm',
              option.disabled ? 'text-gray-400' : 'text-gray-900'
            ]"
          &gt;{{ option.label }}&lt;/label&gt;
        &lt;/div&gt;
      &lt;/div&gt;
      
      &lt;p v-if="errorMessage" class="mt-1 text-xs text-red-600"&gt;{{ errorMessage }}&lt;/p&gt;
      &lt;p v-else-if="helpText" class="mt-1 text-xs text-gray-500"&gt;{{ helpText }}&lt;/p&gt;
    &lt;/fieldset&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { computed } from 'vue';

const props = defineProps({
  modelValue: {
    type: [Array, String, Number, Boolean],
    default: () => type === 'checkbox' ? [] : ''
  },
  label: String,
  name: {
    type: String,
    default: () => `input-group-${Math.random().toString(36).substring(2, 9)}`
  },
  id: {
    type: String,
    default: () => `input-group-${Math.random().toString(36).substring(2, 9)}`
  },
  options: {
    type: Array,
    default: () => [],
    required: true
  },
  type: {
    type: String,
    default: 'checkbox',
    validator: (value) => ['checkbox', 'radio'].includes(value)
  },
  direction: {
    type: String,
    default: 'vertical',
    validator: (value) => ['vertical', 'horizontal'].includes(value)
  },
  disabled: Boolean,
  errorMessage: String,
  helpText: String
});

const emit = defineEmits(['update:modelValue']);

const isChecked = (value) => {
  if (props.type === 'checkbox') {
    return Array.isArray(props.modelValue) && props.modelValue.includes(value);
  } else {
    return props.modelValue === value;
  }
};

const handleChange = (value) => {
  if (props.type === 'checkbox') {
    const newValue = [...(Array.isArray(props.modelValue) ? props.modelValue : [])];
    const index = newValue.indexOf(value);
    
    if (index === -1) {
      newValue.push(value);
    } else {
      newValue.splice(index, 1);
    }
    
    emit('update:modelValue', newValue);
  } else {
    emit('update:modelValue', value);
  }
};
&lt;/script&gt;
                </pre>
            </div>
        </div>
    </div>
</body>
</html>