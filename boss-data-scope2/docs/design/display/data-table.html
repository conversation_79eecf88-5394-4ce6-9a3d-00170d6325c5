<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据表格 - DataScope UI</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        .preview-section {
            margin-bottom: 30px;
        }
        .component-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #eaeaea;
        }
    </style>
</head>
<body class="bg-gray-50">
    <header class="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="../index.html" class="flex items-center">
                        <h1 class="text-xl font-bold text-gray-900">DataScope UI</h1>
                    </a>
                </div>
                <nav class="flex items-center space-x-4">
                    <a href="../index.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">首页</a>
                    <a href="../components.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">组件</a>
                    <a href="../guides/index.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">使用指引</a>
                    <a href="../sitemap.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">网站地图</a>
                </nav>
            </div>
        </div>
    </header>

    <div class="flex min-h-screen">
        <!-- 左侧导航菜单 -->
        <div class="w-64 bg-white border-r border-gray-200 overflow-y-auto">
            <div class="p-4">
                <h3 class="text-lg font-semibold text-gray-900 mb-3">组件分类</h3>
                
                <div class="space-y-4">
                    <!-- 基础组件 -->
                    <div>
                        <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                            <i class="ri-layout-2-line mr-2 text-blue-600"></i>
                            <span>基础组件</span>
                        </h4>
                        <ul class="ml-6 space-y-1">
                            <li>
                                <a href="../basic/buttons.html" class="block text-gray-700 hover:text-indigo-600 py-1">按钮</a>
                            </li>
                            <li>
                                <a href="../basic/icon-buttons.html" class="block text-gray-700 hover:text-indigo-600 py-1">图标按钮</a>
                            </li>
                            <li>
                                <a href="../basic/inputs.html" class="block text-gray-700 hover:text-indigo-600 py-1">输入框</a>
                            </li>
                            <li>
                                <a href="../basic/badges.html" class="block text-gray-700 hover:text-indigo-600 py-1">状态标签</a>
                            </li>
                            <li>
                                <a href="../basic/index.html" class="block text-gray-700 hover:text-indigo-600 py-1">全部基础组件</a>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- 表单组件 -->
                    <div>
                        <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                            <i class="ri-file-list-3-line mr-2 text-green-600"></i>
                            <span>表单组件</span>
                        </h4>
                        <ul class="ml-6 space-y-1">
                            <li>
                                <a href="../forms/basic-inputs.html" class="block text-gray-700 hover:text-indigo-600 py-1">基础输入</a>
                            </li>
                            <li>
                                <a href="../forms/selectors.html" class="block text-gray-700 hover:text-indigo-600 py-1">选择器</a>
                            </li>
                            <li>
                                <a href="../forms/toggles.html" class="block text-gray-700 hover:text-indigo-600 py-1">开关控件</a>
                            </li>
                            <li>
                                <a href="../forms/date-picker.html" class="block text-gray-700 hover:text-indigo-600 py-1">日期选择器</a>
                            </li>
                            <li>
                                <a href="../forms/index.html" class="block text-gray-700 hover:text-indigo-600 py-1">全部表单组件</a>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- 容器组件 -->
                    <div>
                        <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                            <i class="ri-layout-masonry-line mr-2 text-purple-600"></i>
                            <span>容器组件</span>
                        </h4>
                        <ul class="ml-6 space-y-1">
                            <li>
                                <a href="../container/cards.html" class="block text-gray-700 hover:text-indigo-600 py-1">卡片容器</a>
                            </li>
                            <li>
                                <a href="../container/panels.html" class="block text-gray-700 hover:text-indigo-600 py-1">面板容器</a>
                            </li>
                            <li>
                                <a href="../container/boxes.html" class="block text-gray-700 hover:text-indigo-600 py-1">盒子容器</a>
                            </li>
                            <li>
                                <a href="../container/index.html" class="block text-gray-700 hover:text-indigo-600 py-1">全部容器组件</a>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- 数据展示 -->
                    <div>
                        <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                            <i class="ri-table-line mr-2 text-yellow-600"></i>
                            <span>数据展示</span>
                        </h4>
                        <ul class="ml-6 space-y-1">
                            <li>
                                <a href="./data-table.html" class="block text-gray-700 hover:text-indigo-600 py-1 font-medium text-indigo-600">数据表格</a>
                            </li>
                            <li>
                                <a href="./action-buttons.html" class="block text-gray-700 hover:text-indigo-600 py-1">操作按钮</a>
                            </li>
                            <li>
                                <a href="./page-header.html" class="block text-gray-700 hover:text-indigo-600 py-1">页面标题</a>
                            </li>
                            <li>
                                <a href="./index.html" class="block text-gray-700 hover:text-indigo-600 py-1">全部数据展示</a>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- 反馈组件 -->
                    <div>
                        <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                            <i class="ri-notification-3-line mr-2 text-indigo-600"></i>
                            <span>反馈组件</span>
                        </h4>
                        <ul class="ml-6 space-y-1">
                            <li>
                                <a href="../feedback/message.html" class="block text-gray-700 hover:text-indigo-600 py-1">消息提示</a>
                            </li>
                            <li>
                                <a href="../feedback/notification.html" class="block text-gray-700 hover:text-indigo-600 py-1">通知提醒</a>
                            </li>
                            <li>
                                <a href="../feedback/dialog.html" class="block text-gray-700 hover:text-indigo-600 py-1">对话框</a>
                            </li>
                            <li>
                                <a href="../feedback/alert.html" class="block text-gray-700 hover:text-indigo-600 py-1">警告提示</a>
                            </li>
                            <li>
                                <a href="../feedback/index.html" class="block text-gray-700 hover:text-indigo-600 py-1">全部反馈组件</a>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- 动画效果 -->
                    <div>
                        <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                            <i class="ri-slideshow-3-line mr-2 text-pink-600"></i>
                            <span>动画效果</span>
                        </h4>
                        <ul class="ml-6 space-y-1">
                            <li>
                                <a href="../animations/transitions.html" class="block text-gray-700 hover:text-indigo-600 py-1">过渡动画</a>
                            </li>
                            <li>
                                <a href="../animations/loading.html" class="block text-gray-700 hover:text-indigo-600 py-1">加载动画</a>
                            </li>
                            <li>
                                <a href="../animations/index.html" class="block text-gray-700 hover:text-indigo-600 py-1">全部动画效果</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧内容区 -->
        <div class="flex-1 p-6 overflow-y-auto">
            <div class="container mx-auto px-4 py-8">
                <div class="flex items-center mb-8">
                    <a href="../index.html" class="text-gray-500 hover:text-indigo-600 mr-2">
                        <i class="ri-home-line"></i>
                    </a>
                    <span class="text-gray-400 mx-2">/</span>
                    <a href="../components.html" class="text-gray-500 hover:text-indigo-600 mr-2">
                        组件
                    </a>
                    <span class="text-gray-400 mx-2">/</span>
                    <a href="./index.html" class="text-gray-500 hover:text-indigo-600 mr-2">数据展示</a>
                    <span class="text-gray-400 mx-2">/</span>
                    <span class="text-gray-900 font-medium">数据表格</span>
                </div>

                <div class="mb-12">
                    <h2 class="text-3xl font-bold mb-4 text-gray-900">数据表格</h2>
                    <p class="text-lg text-gray-600 max-w-3xl">
                        数据表格组件用于展示和处理大量数据，支持排序、筛选、分页等功能，并且可以在表格中进行操作。
                    </p>
                </div>
                
                <div class="bg-white shadow rounded-lg p-6 mb-8">
                    <h2 class="component-title">基本数据表格</h2>
                    
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">名称</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">MySQL示例数据库</td>
                                    <td class="px-6 py-4 whitespace-nowrap">MySQL</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center rounded-md px-2 py-1 text-xs font-medium bg-green-100 text-green-800">
                                            正常
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <a href="#" class="text-indigo-600 hover:text-indigo-900 mr-2">查看</a>
                                        <a href="#" class="text-indigo-600 hover:text-indigo-900 mr-2">编辑</a>
                                        <a href="#" class="text-red-600 hover:text-red-900">删除</a>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">PostgreSQL生产库</td>
                                    <td class="px-6 py-4 whitespace-nowrap">PostgreSQL</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center rounded-md px-2 py-1 text-xs font-medium bg-green-100 text-green-800">
                                            正常
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <a href="#" class="text-indigo-600 hover:text-indigo-900 mr-2">查看</a>
                                        <a href="#" class="text-indigo-600 hover:text-indigo-900 mr-2">编辑</a>
                                        <a href="#" class="text-red-600 hover:text-red-900">删除</a>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">SQL Server测试库</td>
                                    <td class="px-6 py-4 whitespace-nowrap">SQL Server</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center rounded-md px-2 py-1 text-xs font-medium bg-red-100 text-red-800">
                                            错误
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <a href="#" class="text-indigo-600 hover:text-indigo-900 mr-2">查看</a>
                                        <a href="#" class="text-indigo-600 hover:text-indigo-900 mr-2">编辑</a>
                                        <a href="#" class="text-red-600 hover:text-red-900">删除</a>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="text-sm text-gray-500 mt-4">
                        <p>基本数据表格，包含表头和表体部分，适用于展示数据列表。</p>
                    </div>
                </div>
                
                <div class="bg-white shadow rounded-lg p-6 mb-8">
                    <h2 class="component-title">带排序的数据表格</h2>
                    
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <div class="flex items-center space-x-1">
                                            <span>名称</span>
                                            <button class="text-gray-400 hover:text-gray-600">
                                                <svg class="h-4 w-4 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <div class="flex items-center space-x-1">
                                            <span>类型</span>
                                            <button class="text-gray-400 hover:text-gray-600">
                                                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <div class="flex items-center space-x-1">
                                            <span>状态</span>
                                            <button class="text-gray-400 hover:text-gray-600">
                                                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">MySQL示例数据库</td>
                                    <td class="px-6 py-4 whitespace-nowrap">MySQL</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center rounded-md px-2 py-1 text-xs font-medium bg-green-100 text-green-800">
                                            正常
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <a href="#" class="text-indigo-600 hover:text-indigo-900 mr-2">查看</a>
                                        <a href="#" class="text-indigo-600 hover:text-indigo-900 mr-2">编辑</a>
                                        <a href="#" class="text-red-600 hover:text-red-900">删除</a>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">PostgreSQL生产库</td>
                                    <td class="px-6 py-4 whitespace-nowrap">PostgreSQL</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center rounded-md px-2 py-1 text-xs font-medium bg-green-100 text-green-800">
                                            正常
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <a href="#" class="text-indigo-600 hover:text-indigo-900 mr-2">查看</a>
                                        <a href="#" class="text-indigo-600 hover:text-indigo-900 mr-2">编辑</a>
                                        <a href="#" class="text-red-600 hover:text-red-900">删除</a>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">SQL Server测试库</td>
                                    <td class="px-6 py-4 whitespace-nowrap">SQL Server</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center rounded-md px-2 py-1 text-xs font-medium bg-red-100 text-red-800">
                                            错误
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <a href="#" class="text-indigo-600 hover:text-indigo-900 mr-2">查看</a>
                                        <a href="#" class="text-indigo-600 hover:text-indigo-900 mr-2">编辑</a>
                                        <a href="#" class="text-red-600 hover:text-red-900">删除</a>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="text-sm text-gray-500 mt-4">
                        <p>带排序功能的数据表格，用户可以点击表头中的排序图标进行排序。当前"名称"列为升序排序状态。</p>
                    </div>
                </div>
                
                <div class="bg-white shadow rounded-lg p-6 mb-8">
                    <h2 class="component-title">带分页的数据表格</h2>
                    
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">名称</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">MySQL示例数据库</td>
                                    <td class="px-6 py-4 whitespace-nowrap">MySQL</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center rounded-md px-2 py-1 text-xs font-medium bg-green-100 text-green-800">
                                            正常
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <a href="#" class="text-indigo-600 hover:text-indigo-900 mr-2">查看</a>
                                        <a href="#" class="text-indigo-600 hover:text-indigo-900 mr-2">编辑</a>
                                        <a href="#" class="text-red-600 hover:text-red-900">删除</a>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">PostgreSQL生产库</td>
                                    <td class="px-6 py-4 whitespace-nowrap">PostgreSQL</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center rounded-md px-2 py-1 text-xs font-medium bg-green-100 text-green-800">
                                            正常
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <a href="#" class="text-indigo-600 hover:text-indigo-900 mr-2">查看</a>
                                        <a href="#" class="text-indigo-600 hover:text-indigo-900 mr-2">编辑</a>
                                        <a href="#" class="text-red-600 hover:text-red-900">删除</a>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">SQL Server测试库</td>
                                    <td class="px-6 py-4 whitespace-nowrap">SQL Server</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center rounded-md px-2 py-1 text-xs font-medium bg-red-100 text-red-800">
                                            错误
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <a href="#" class="text-indigo-600 hover:text-indigo-900 mr-2">查看</a>
                                        <a href="#" class="text-indigo-600 hover:text-indigo-900 mr-2">编辑</a>
                                        <a href="#" class="text-red-600 hover:text-red-900">删除</a>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        
                        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                            <div class="flex items-center">
                                <span class="text-sm text-gray-700">
                                    共 <span class="font-medium">42</span> 条
                                </span>
                                <select class="ml-2 rounded-md border-gray-300 py-1 text-base focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                    <option value="10">10 条/页</option>
                                    <option value="20">20 条/页</option>
                                    <option value="50">50 条/页</option>
                                    <option value="100">100 条/页</option>
                                </select>
                            </div>

                            <div class="flex items-center space-x-2">
                                <button class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                                
                                <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                    1
                                </button>
                                
                                <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-indigo-50 text-sm font-medium text-indigo-600 hover:bg-indigo-100">
                                    2
                                </button>
                                
                                <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                    3
                                </button>
                                
                                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                                    ...
                                </span>
                                
                                <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                    8
                                </button>
                                
                                <button class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-sm text-gray-500 mt-4">
                        <p>带分页功能的数据表格，包含页码导航和每页显示条数选择器。</p>
                    </div>
                </div>
                
                <div class="bg-white shadow rounded-lg p-6 mb-8">
                    <h2 class="component-title">加载状态</h2>
                    
                    <div class="w-full h-64 flex items-center justify-center">
                        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
                    </div>
                    
                    <div class="text-sm text-gray-500 mt-4">
                        <p>数据表格的加载状态，在数据加载过程中显示加载动画。</p>
                    </div>
                </div>
                
                <div class="bg-white shadow rounded-lg p-6 mb-8">
                    <h2 class="component-title">空状态</h2>
                    
                    <div class="w-full py-12 flex flex-col items-center justify-center">
                        <div class="rounded-full bg-gray-100 h-16 w-16 flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-database text-gray-400 text-2xl"></i>
                        </div>
                        <h3 class="text-sm font-medium text-gray-900">暂无数据</h3>
                        <p class="mt-1 text-sm text-gray-500">
                            没有符合条件的数据源
                        </p>
                    </div>
                    
                    <div class="text-sm text-gray-500 mt-4">
                        <p>数据表格的空状态，当没有数据时显示友好的提示。</p>
                    </div>
                </div>
                
                <div class="bg-white shadow rounded-lg p-6">
                    <h2 class="component-title">如何使用</h2>
                    <div class="bg-gray-100 p-4 rounded">
                        <pre class="text-sm overflow-x-auto">
&lt;template&gt;
  &lt;DataTable
    :columns="columns"
    :data-source="dataSource"
    :pagination="pagination"
    :loading="loading"
    @change="handleTableChange"
  &gt;
    &lt;template #action="{ record }"&gt;
      &lt;button @click="viewDetail(record)" class="text-indigo-600 hover:text-indigo-900 mr-2"&gt;查看&lt;/button&gt;
      &lt;button @click="editItem(record)" class="text-indigo-600 hover:text-indigo-900 mr-2"&gt;编辑&lt;/button&gt;
      &lt;button @click="deleteItem(record)" class="text-red-600 hover:text-red-900"&gt;删除&lt;/button&gt;
    &lt;/template&gt;
  &lt;/DataTable&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref } from 'vue';
import DataTable from '@/components/common/DataTable.vue';

const loading = ref(false);

const columns = [
  {
    key: 'name',
    title: '名称',
    dataIndex: 'name',
    sortable: true
  },
  {
    key: 'type',
    title: '类型',
    dataIndex: 'type'
  },
  {
    key: 'status',
    title: '状态',
    dataIndex: 'status',
    slots: { customRender: 'status' }
  },
  {
    key: 'action',
    title: '操作',
    slots: { customRender: 'action' }
  }
];

const dataSource = ref([
  { id: '1', name: 'MySQL示例数据库', type: 'MySQL', status: 'active' },
  { id: '2', name: 'PostgreSQL生产库', type: 'PostgreSQL', status: 'active' },
  { id: '3', name: 'SQL Server测试库', type: 'SQL Server', status: 'error' }
]);

const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 42,
  showSizeChanger: true
});

const handleTableChange = (event) => {
  console.log('表格变化:', event);
  // 处理排序、筛选、分页变化
};

const viewDetail = (record) => {
  console.log('查看详情:', record);
};

const editItem = (record) => {
  console.log('编辑项:', record);
};

const deleteItem = (record) => {
  console.log('删除项:', record);
};
&lt;/script&gt;
                        </pre>
                    </div>
                </div>
                
                <div class="text-center mb-12">
                    <a href="./index.html" class="inline-flex items-center px-5 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <i class="ri-arrow-left-line mr-1"></i> 返回数据展示组件
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <footer class="bg-white border-t border-gray-200 py-6 mt-12">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="mb-4 md:mb-0">
                    <p class="text-gray-500">© 2023 DataScope UI 组件库</p>
                </div>
                <div class="flex space-x-4">
                    <a href="../guides/changelog.html" class="text-gray-500 hover:text-indigo-600">
                        更新日志
                    </a>
                    <a href="../sitemap.html" class="text-gray-500 hover:text-indigo-600">
                        网站地图
                    </a>
                    <a href="https://github.com/yourusername/datascope-ui" target="_blank" class="text-gray-500 hover:text-indigo-600">
                        <i class="ri-github-fill text-xl"></i>
                    </a>
                </div>
            </div>
        </div>
    </footer>
</body>
</html>