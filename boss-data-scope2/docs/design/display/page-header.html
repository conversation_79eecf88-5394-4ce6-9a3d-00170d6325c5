<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面标题 - DataScope UI</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        .preview-section {
            margin-bottom: 30px;
        }
        .component-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #eaeaea;
        }
    </style>
</head>
<body class="bg-gray-50">
    <header class="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="../index.html" class="flex items-center">
                        <h1 class="text-xl font-bold text-gray-900">DataScope UI</h1>
                    </a>
                </div>
                <nav class="flex items-center space-x-4">
                    <a href="../index.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">首页</a>
                    <a href="../components.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">组件</a>
                    <a href="../guides/index.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">使用指引</a>
                    <a href="../sitemap.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">网站地图</a>
                </nav>
            </div>
        </div>
    </header>

    <div class="container mx-auto px-4 py-8">
        <div class="flex items-center mb-8">
            <a href="../index.html" class="text-gray-500 hover:text-indigo-600 mr-2">
                <i class="ri-home-line"></i>
            </a>
            <span class="text-gray-400 mx-2">/</span>
            <a href="../components.html" class="text-gray-500 hover:text-indigo-600 mr-2">
                组件
            </a>
            <span class="text-gray-400 mx-2">/</span>
            <a href="./index.html" class="text-gray-500 hover:text-indigo-600 mr-2">数据展示</a>
            <span class="text-gray-400 mx-2">/</span>
            <span class="text-gray-900 font-medium">页面标题</span>
        </div>

        <div class="mb-12">
            <h2 class="text-3xl font-bold mb-4 text-gray-900">页面标题</h2>
            <p class="text-lg text-gray-600 max-w-3xl">
                页面标题组件用于页面上方的标题区域，包含页面标题、操作按钮以及方便的导航功能。
            </p>
        </div>
        
        <div class="bg-white shadow rounded-lg p-6 mb-8">
            <h2 class="component-title">基本页面标题</h2>
            
            <div class="preview-section border border-gray-200 rounded-lg p-4">
                <div class="page-header">
                    <div class="flex justify-between items-center mb-6">
                        <div class="flex items-center">
                            <h1 class="text-xl font-semibold mb-0">数据源管理</h1>
                        </div>
                    </div>
                </div>
                
                <div class="text-sm text-gray-500 mt-4">
                    <p>最简单的页面标题组件，只包含标题文本。</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white shadow rounded-lg p-6 mb-8">
            <h2 class="component-title">带操作按钮的页面标题</h2>
            
            <div class="preview-section border border-gray-200 rounded-lg p-4">
                <div class="page-header">
                    <div class="flex justify-between items-center mb-6">
                        <div class="flex items-center">
                            <h1 class="text-xl font-semibold mb-0">数据源管理</h1>
                        </div>
                        
                        <div class="flex items-center space-x-2">
                            <button class="inline-flex items-center justify-center rounded-md focus:outline-none transition-colors px-4 py-2 text-sm bg-gray-200 text-gray-800 hover:bg-gray-300 focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                                <i class="fas fa-sync-alt mr-2"></i>
                                刷新
                            </button>
                            
                            <button class="inline-flex items-center justify-center rounded-md focus:outline-none transition-colors px-4 py-2 text-sm bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                <i class="fas fa-plus mr-2"></i>
                                添加数据源
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="text-sm text-gray-500 mt-4">
                    <p>带有操作按钮的页面标题，适用于大多数列表页面。右侧放置主要操作按钮，主要操作使用主题色彩。</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white shadow rounded-lg p-6 mb-8">
            <h2 class="component-title">返回功能的页面标题</h2>
            
            <div class="preview-section border border-gray-200 rounded-lg p-4">
                <div class="page-header">
                    <div class="flex justify-between items-center mb-6">
                        <div class="flex items-center">
                            <h1 class="text-xl font-semibold mb-0">编辑数据源</h1>
                        </div>
                        
                        <div class="flex items-center space-x-2">
                            <button class="inline-flex items-center justify-center rounded-md focus:outline-none transition-colors px-4 py-2 text-sm bg-gray-200 text-gray-800 hover:bg-gray-300 focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                                <i class="fas fa-arrow-left mr-2"></i>
                                返回列表
                            </button>
                            
                            <button class="inline-flex items-center justify-center rounded-md focus:outline-none transition-colors px-4 py-2 text-sm bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                <i class="fas fa-save mr-2"></i>
                                保存
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="text-sm text-gray-500 mt-4">
                    <p>编辑页面的标题，包含返回按钮和保存按钮，适用于表单页面。</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white shadow rounded-lg p-6 mb-8">
            <h2 class="component-title">详情页面标题</h2>
            
            <div class="preview-section border border-gray-200 rounded-lg p-4">
                <div class="page-header">
                    <div class="flex justify-between items-center mb-6">
                        <div class="flex items-center">
                            <h1 class="text-xl font-semibold mb-0">MySQL示例数据库 - 详情</h1>
                        </div>
                        
                        <div class="flex items-center space-x-2">
                            <button class="inline-flex items-center justify-center rounded-md focus:outline-none transition-colors px-4 py-2 text-sm bg-gray-200 text-gray-800 hover:bg-gray-300 focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                                <i class="fas fa-arrow-left mr-2"></i>
                                返回列表
                            </button>
                            
                            <button class="inline-flex items-center justify-center rounded-md focus:outline-none transition-colors px-4 py-2 text-sm bg-blue-500 text-white hover:bg-blue-600 focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <i class="fas fa-sync-alt mr-2"></i>
                                同步元数据
                            </button>
                            
                            <button class="inline-flex items-center justify-center rounded-md focus:outline-none transition-colors px-4 py-2 text-sm bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                <i class="fas fa-edit mr-2"></i>
                                编辑
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="text-sm text-gray-500 mt-4">
                    <p>详情页面的标题，包含返回按钮和多个操作按钮，适用于详情页面。</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white shadow rounded-lg p-6 mb-8">
            <h2 class="component-title">完整页面标题示例</h2>
            
            <div class="preview-section border border-gray-200 rounded-lg p-4">
                <div class="container mx-auto px-4 py-6">
                    <div class="page-header">
                        <div class="flex justify-between items-center mb-6">
                            <div class="flex items-center">
                                <h1 class="text-xl font-semibold mb-0">数据源管理</h1>
                            </div>
                            
                            <div class="flex items-center space-x-2">
                                <button class="inline-flex items-center justify-center rounded-md focus:outline-none transition-colors px-4 py-2 text-sm bg-gray-200 text-gray-800 hover:bg-gray-300 focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                                    <i class="fas fa-sync-alt mr-2"></i>
                                    刷新
                                </button>
                                
                                <button class="inline-flex items-center justify-center rounded-md focus:outline-none transition-colors px-4 py-2 text-sm bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                    <i class="fas fa-plus mr-2"></i>
                                    添加数据源
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white shadow rounded-lg p-6 mb-6">
                        <!-- 过滤区域 -->
                        <div class="flex items-center justify-between">
                            <div class="w-1/3">
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="fas fa-search text-gray-400"></i>
                                    </div>
                                    <input
                                        type="text"
                                        class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                        placeholder="搜索数据源..."
                                    />
                                </div>
                            </div>
                            
                            <div class="flex space-x-2">
                                <select class="block py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                    <option value="">所有类型</option>
                                    <option value="mysql">MySQL</option>
                                    <option value="postgresql">PostgreSQL</option>
                                </select>
                                
                                <select class="block py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                    <option value="">所有状态</option>
                                    <option value="active">正常</option>
                                    <option value="error">错误</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white shadow rounded-lg">
                        <!-- 示例表格 -->
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">名称</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">MySQL示例数据库</td>
                                    <td class="px-6 py-4 whitespace-nowrap">MySQL</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center rounded-md px-2 py-1 text-xs font-medium bg-green-100 text-green-800">
                                            正常
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <a href="#" class="text-indigo-600 hover:text-indigo-900 mr-2">查看</a>
                                        <a href="#" class="text-indigo-600 hover:text-indigo-900 mr-2">编辑</a>
                                        <a href="#" class="text-red-600 hover:text-red-900">删除</a>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <div class="text-sm text-gray-500 mt-4">
                    <p>完整页面布局示例，包含页面标题、过滤区域和内容区域。</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white shadow rounded-lg p-6">
            <h2 class="component-title">如何使用</h2>
            <div class="bg-gray-100 p-4 rounded">
                <pre class="text-sm overflow-x-auto">
&lt;template&gt;
  &lt;PageHeader 
    title="数据源管理"
    :actionItems="[
      {
        text: '添加数据源',
        type: 'primary',
        icon: 'plus-icon',
        onClick: showAddForm
      },
      {
        text: '刷新',
        type: 'default',
        icon: 'refresh-icon',
        onClick: refreshData
      }
    ]"
  /&gt;
&lt;/template&gt;

&lt;script setup&gt;
import PageHeader from '@/components/common/PageHeader.vue';
import { PlusIcon, RefreshIcon } from '@heroicons/vue/solid';

const showAddForm = () => {
  // 处理添加数据源
};

const refreshData = () => {
  // 处理刷新数据
};
&lt;/script&gt;
                </pre>
            </div>
        </div>
        
        <div class="text-center mb-12">
            <a href="./index.html" class="inline-flex items-center px-5 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <i class="ri-arrow-left-line mr-1"></i> 返回数据展示组件
            </a>
        </div>
    </div>
    
    <footer class="bg-white border-t border-gray-200 py-6 mt-12">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="mb-4 md:mb-0">
                    <p class="text-gray-500">© 2023 DataScope UI 组件库</p>
                </div>
                <div class="flex space-x-4">
                    <a href="../guides/changelog.html" class="text-gray-500 hover:text-indigo-600">
                        更新日志
                    </a>
                    <a href="../sitemap.html" class="text-gray-500 hover:text-indigo-600">
                        网站地图
                    </a>
                    <a href="https://github.com/yourusername/datascope-ui" target="_blank" class="text-gray-500 hover:text-indigo-600">
                        <i class="ri-github-fill text-xl"></i>
                    </a>
                </div>
            </div>
        </div>
    </footer>
</body>
</html>