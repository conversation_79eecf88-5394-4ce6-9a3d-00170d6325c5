<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据展示组件 - DataScope UI</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        .component-card {
            transition: all 0.3s ease;
        }
        .component-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
    </style>
</head>
<body class="bg-gray-50">
    <header class="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="../index.html" class="flex items-center">
                        <h1 class="text-xl font-bold text-gray-900">DataScope UI</h1>
                    </a>
                </div>
                <nav class="flex items-center space-x-4">
                    <a href="../index.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">首页</a>
                    <a href="../components.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">组件</a>
                    <a href="../guides/index.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">使用指引</a>
                    <a href="../sitemap.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">网站地图</a>
                </nav>
            </div>
        </div>
    </header>

    <div class="container mx-auto px-4 py-8">
        <div class="flex items-center mb-8">
            <a href="../index.html" class="text-gray-500 hover:text-indigo-600 mr-2">
                <i class="ri-home-line"></i>
            </a>
            <span class="text-gray-400 mx-2">/</span>
            <a href="../components.html" class="text-gray-500 hover:text-indigo-600 mr-2">
                组件
            </a>
            <span class="text-gray-400 mx-2">/</span>
            <span class="text-gray-900 font-medium">数据展示</span>
        </div>

        <div class="mb-8">
            <h2 class="text-3xl font-bold mb-4 text-gray-900">数据展示组件</h2>
            <p class="text-lg text-gray-600">
                数据展示组件用于呈现和可视化各种数据和内容，如表格、图表、统计数据等，帮助用户理解和分析信息。
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
            <!-- 操作按钮 -->
            <a href="./action-buttons.html" class="block">
                <div class="bg-white rounded-lg shadow-md component-card p-6">
                    <div class="flex items-center mb-4">
                        <div class="flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-green-100 text-green-600 mr-4">
                            <i class="ri-settings-line text-xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold">操作按钮</h3>
                    </div>
                    <p class="text-gray-600 mb-4">用于表格和列表中的操作区域，提供查看、编辑、删除等常见操作。</p>
                    
                    <div class="mt-4">
                        <div class="flex justify-center space-x-2">
                            <button class="w-8 h-8 flex items-center justify-center rounded-full text-blue-600 bg-blue-100 hover:bg-blue-200">
                                <i class="ri-eye-line"></i>
                            </button>
                            <button class="w-8 h-8 flex items-center justify-center rounded-full text-green-600 bg-green-100 hover:bg-green-200">
                                <i class="ri-edit-line"></i>
                            </button>
                            <button class="w-8 h-8 flex items-center justify-center rounded-full text-indigo-600 bg-indigo-100 hover:bg-indigo-200">
                                <i class="ri-download-line"></i>
                            </button>
                            <button class="w-8 h-8 flex items-center justify-center rounded-full text-red-600 bg-red-100 hover:bg-red-200">
                                <i class="ri-delete-bin-line"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </a>
            
            <!-- 数据表格 -->
            <a href="./data-table.html" class="block">
                <div class="bg-white rounded-lg shadow-md component-card p-6">
                    <div class="flex items-center mb-4">
                        <div class="flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 text-blue-600 mr-4">
                            <i class="ri-table-line text-xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold">数据表格</h3>
                    </div>
                    <p class="text-gray-600 mb-4">用于展示结构化数据的表格组件，支持排序、筛选、分页等功能。</p>
                    
                    <div class="mt-4">
                        <div class="w-full border border-gray-200 rounded-lg overflow-hidden">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">名称</th>
                                        <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">状态</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">数据1</td>
                                        <td class="px-3 py-2 whitespace-nowrap">
                                            <span class="px-2 py-0.5 text-xs rounded-full bg-green-100 text-green-800">正常</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">数据2</td>
                                        <td class="px-3 py-2 whitespace-nowrap">
                                            <span class="px-2 py-0.5 text-xs rounded-full bg-yellow-100 text-yellow-800">警告</span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </a>
            
            <!-- 页面标题 -->
            <a href="./page-header.html" class="block">
                <div class="bg-white rounded-lg shadow-md component-card p-6">
                    <div class="flex items-center mb-4">
                        <div class="flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-indigo-100 text-indigo-600 mr-4">
                            <i class="ri-heading text-xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold">页面标题</h3>
                    </div>
                    <p class="text-gray-600 mb-4">用于页面顶部的标题和操作区域，提供面包屑导航和操作按钮。</p>
                    
                    <div class="mt-4">
                        <div class="w-full">
                            <div class="mb-2 flex items-center text-sm text-gray-500">
                                <span>首页</span>
                                <i class="ri-arrow-right-s-line mx-1"></i>
                                <span>数据管理</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <h3 class="text-lg font-medium text-gray-900">数据列表</h3>
                                <div class="flex space-x-2">
                                    <button class="px-3 py-1 bg-indigo-600 text-white rounded-md text-sm">新建</button>
                                    <button class="px-3 py-1 border border-gray-300 text-gray-700 rounded-md text-sm">导出</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </a>
        </div>

        <div class="text-center mb-12">
            <a href="../components.html" class="inline-flex items-center px-5 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <i class="ri-arrow-left-line mr-1"></i> 返回组件列表
            </a>
        </div>
    </div>
    
    <footer class="bg-white border-t border-gray-200 py-6 mt-12">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="mb-4 md:mb-0">
                    <p class="text-gray-500">© 2023 DataScope UI 组件库</p>
                </div>
                <div class="flex space-x-4">
                    <a href="../guides/changelog.html" class="text-gray-500 hover:text-indigo-600">
                        更新日志
                    </a>
                    <a href="../sitemap.html" class="text-gray-500 hover:text-indigo-600">
                        网站地图
                    </a>
                    <a href="https://github.com/yourusername/datascope-ui" target="_blank" class="text-gray-500 hover:text-indigo-600">
                        <i class="ri-github-fill text-xl"></i>
                    </a>
                </div>
            </div>
        </div>
    </footer>
</body>
</html>