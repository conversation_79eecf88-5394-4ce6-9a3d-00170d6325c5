<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导航指南 - DataScope UI</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        .nav-section {
            transition: all 0.3s ease;
        }
        .nav-section:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        .nav-link {
            transition: all 0.2s ease;
        }
        .nav-link:hover {
            background-color: #f3f4f6;
        }
    </style>
</head>
<body class="bg-gray-50">
    <header class="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="./index.html" class="flex items-center">
                        <h1 class="text-xl font-bold text-gray-900">DataScope UI</h1>
                    </a>
                </div>
                <nav class="flex items-center space-x-4">
                    <a href="./index.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">首页</a>
                    <a href="./components.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">组件</a>
                    <a href="./guides/index.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">使用指引</a>
                    <a href="./guides/vue3.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">Vue 3 示例</a>
                    <a href="./navigation.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">导航</a>
                    <a href="./sitemap.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">网站地图</a>
                    <a href="https://github.com/yourusername/datascope-ui" target="_blank" class="text-gray-500 hover:text-indigo-600">
                        <i class="ri-github-fill text-xl"></i>
                    </a>
                </nav>
            </div>
        </div>
    </header>

    <div class="container mx-auto px-4 py-8">
        <div class="flex items-center mb-8">
            <a href="./index.html" class="text-gray-500 hover:text-indigo-600 mr-2">
                <i class="ri-home-line"></i>
            </a>
            <span class="text-gray-400 mx-2">/</span>
            <span class="text-gray-900 font-medium">导航指南</span>
        </div>

        <div class="mb-8">
            <h2 class="text-3xl font-bold mb-4 text-gray-900">文档导航</h2>
            <p class="text-lg text-gray-600 max-w-3xl">
                DataScope UI 组件库的完整文档导航，帮助您快速找到所需的组件和指南。
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-12 gap-6 mb-12">
            <!-- 左侧导航列表 -->
            <div class="md:col-span-3 bg-white rounded-lg shadow-sm p-4">
                <nav class="space-y-1">
                    <a href="#overview" class="block px-3 py-2 rounded-md text-indigo-600 bg-indigo-50 font-medium">概览</a>
                    <a href="#basic" class="block px-3 py-2 rounded-md text-gray-700 hover:bg-gray-50 hover:text-indigo-600">基础组件</a>
                    <a href="#form" class="block px-3 py-2 rounded-md text-gray-700 hover:bg-gray-50 hover:text-indigo-600">表单组件</a>
                    <a href="#container" class="block px-3 py-2 rounded-md text-gray-700 hover:bg-gray-50 hover:text-indigo-600">容器组件</a>
                    <a href="#display" class="block px-3 py-2 rounded-md text-gray-700 hover:bg-gray-50 hover:text-indigo-600">数据展示</a>
                    <a href="#animation" class="block px-3 py-2 rounded-md text-gray-700 hover:bg-gray-50 hover:text-indigo-600">动画效果</a>
                    <a href="#guides" class="block px-3 py-2 rounded-md text-gray-700 hover:bg-gray-50 hover:text-indigo-600">使用指南</a>
                </nav>
            </div>
            
            <!-- 右侧内容区 -->
            <div class="md:col-span-9 space-y-8">
                <!-- 概览 -->
                <section id="overview" class="nav-section bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-xl font-bold mb-4 pb-2 border-b border-gray-200">概览</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <a href="./index.html" class="block p-4 border border-gray-200 rounded-lg nav-link">
                            <div class="flex items-center">
                                <i class="ri-home-line text-xl text-indigo-600 mr-3"></i>
                                <div>
                                    <h4 class="font-medium">首页</h4>
                                    <p class="text-sm text-gray-500">组件库主页和概览</p>
                                </div>
                            </div>
                        </a>
                        <a href="./components.html" class="block p-4 border border-gray-200 rounded-lg nav-link">
                            <div class="flex items-center">
                                <i class="ri-apps-line text-xl text-indigo-600 mr-3"></i>
                                <div>
                                    <h4 class="font-medium">组件概览</h4>
                                    <p class="text-sm text-gray-500">所有组件分类和导航</p>
                                </div>
                            </div>
                        </a>
                        <a href="./guides/index.html" class="block p-4 border border-gray-200 rounded-lg nav-link">
                            <div class="flex items-center">
                                <i class="ri-book-open-line text-xl text-indigo-600 mr-3"></i>
                                <div>
                                    <h4 class="font-medium">使用指南</h4>
                                    <p class="text-sm text-gray-500">设计原则和使用规范</p>
                                </div>
                            </div>
                        </a>
                        <a href="./guides/changelog.html" class="block p-4 border border-gray-200 rounded-lg nav-link">
                            <div class="flex items-center">
                                <i class="ri-history-line text-xl text-indigo-600 mr-3"></i>
                                <div>
                                    <h4 class="font-medium">更新日志</h4>
                                    <p class="text-sm text-gray-500">版本更新和变更记录</p>
                                </div>
                            </div>
                        </a>
                    </div>
                </section>
                
                <!-- 基础组件 -->
                <section id="basic" class="nav-section bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-xl font-bold mb-4 pb-2 border-b border-gray-200">基础组件</h3>
                    <ul class="space-y-2">
                        <li>
                            <a href="./basic/index.html" class="flex items-center py-2 px-3 text-gray-700 hover:bg-gray-50 rounded-md nav-link">
                                <i class="ri-layout-2-line text-indigo-600 mr-3"></i>
                                <span>基础组件概览</span>
                            </a>
                        </li>
                        <li>
                            <a href="./basic/buttons.html" class="flex items-center py-2 px-3 text-gray-700 hover:bg-gray-50 rounded-md nav-link">
                                <i class="ri-radio-button-line text-indigo-600 mr-3"></i>
                                <span>按钮组件</span>
                            </a>
                        </li>
                        <li>
                            <a href="./basic/inputs.html" class="flex items-center py-2 px-3 text-gray-700 hover:bg-gray-50 rounded-md nav-link">
                                <i class="ri-input-cursor-move text-indigo-600 mr-3"></i>
                                <span>输入框组件</span>
                            </a>
                        </li>
                        <li>
                            <a href="./basic/badges.html" class="flex items-center py-2 px-3 text-gray-700 hover:bg-gray-50 rounded-md nav-link">
                                <i class="ri-price-tag-3-line text-indigo-600 mr-3"></i>
                                <span>状态标签</span>
                            </a>
                        </li>
                    </ul>
                </section>
                
                <!-- 表单组件 -->
                <section id="form" class="nav-section bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-xl font-bold mb-4 pb-2 border-b border-gray-200">表单组件</h3>
                    <ul class="space-y-2">
                        <li>
                            <a href="./forms/index.html" class="flex items-center py-2 px-3 text-gray-700 hover:bg-gray-50 rounded-md nav-link">
                                <i class="ri-file-list-3-line text-green-600 mr-3"></i>
                                <span>表单组件概览</span>
                            </a>
                        </li>
                        <li>
                            <a href="./forms/basic-inputs.html" class="flex items-center py-2 px-3 text-gray-700 hover:bg-gray-50 rounded-md nav-link">
                                <i class="ri-text text-green-600 mr-3"></i>
                                <span>基础输入</span>
                            </a>
                        </li>
                        <li>
                            <a href="./forms/selectors.html" class="flex items-center py-2 px-3 text-gray-700 hover:bg-gray-50 rounded-md nav-link">
                                <i class="ri-list-check text-green-600 mr-3"></i>
                                <span>选择器</span>
                            </a>
                        </li>
                        <li>
                            <a href="./forms/toggles.html" class="flex items-center py-2 px-3 text-gray-700 hover:bg-gray-50 rounded-md nav-link">
                                <i class="ri-toggle-line text-green-600 mr-3"></i>
                                <span>开关控件</span>
                            </a>
                        </li>
                        <li>
                            <a href="./forms/date-picker.html" class="flex items-center py-2 px-3 text-gray-700 hover:bg-gray-50 rounded-md nav-link">
                                <i class="ri-calendar-line text-green-600 mr-3"></i>
                                <span>日期选择器</span>
                            </a>
                        </li>
                        <li>
                            <a href="./forms/upload.html" class="flex items-center py-2 px-3 text-gray-700 hover:bg-gray-50 rounded-md nav-link">
                                <i class="ri-upload-2-line text-green-600 mr-3"></i>
                                <span>文件上传</span>
                            </a>
                        </li>
                        <li>
                            <a href="./forms/form-layout.html" class="flex items-center py-2 px-3 text-gray-700 hover:bg-gray-50 rounded-md nav-link">
                                <i class="ri-layout-5-line text-green-600 mr-3"></i>
                                <span>表单布局</span>
                            </a>
                        </li>
                    </ul>
                </section>
                
                <!-- 容器组件 -->
                <section id="container" class="nav-section bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-xl font-bold mb-4 pb-2 border-b border-gray-200">容器组件</h3>
                    <ul class="space-y-2">
                        <li>
                            <a href="./container/index.html" class="flex items-center py-2 px-3 text-gray-700 hover:bg-gray-50 rounded-md nav-link">
                                <i class="ri-layout-masonry-line text-purple-600 mr-3"></i>
                                <span>容器组件概览</span>
                            </a>
                        </li>
                        <li>
                            <a href="./container/cards.html" class="flex items-center py-2 px-3 text-gray-700 hover:bg-gray-50 rounded-md nav-link">
                                <i class="ri-layout-card-line text-purple-600 mr-3"></i>
                                <span>卡片容器</span>
                            </a>
                        </li>
                        <li>
                            <a href="./container/panels.html" class="flex items-center py-2 px-3 text-gray-700 hover:bg-gray-50 rounded-md nav-link">
                                <i class="ri-layout-bottom-line text-purple-600 mr-3"></i>
                                <span>面板容器</span>
                            </a>
                        </li>
                        <li>
                            <a href="./container/boxes.html" class="flex items-center py-2 px-3 text-gray-700 hover:bg-gray-50 rounded-md nav-link">
                                <i class="ri-box-3-line text-purple-600 mr-3"></i>
                                <span>盒子容器</span>
                            </a>
                        </li>
                    </ul>
                </section>
                
                <!-- 数据展示 -->
                <div class="mb-8">
                    <h3 class="text-lg font-semibold mb-2 text-gray-800">数据展示</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <a href="./display/index.html" class="flex items-center p-3 rounded-lg hover:bg-gray-100">
                            <i class="ri-stack-line text-lg text-indigo-600 mr-3"></i>
                            <div>
                                <h4 class="font-medium">数据展示概览</h4>
                                <p class="text-sm text-gray-600">数据展示组件总览</p>
                            </div>
                        </a>
                        <a href="./display/data-table.html" class="flex items-center p-3 rounded-lg hover:bg-gray-100">
                            <i class="ri-table-line text-lg text-indigo-600 mr-3"></i>
                            <div>
                                <h4 class="font-medium">数据表格</h4>
                                <p class="text-sm text-gray-600">展示结构化数据的表格组件</p>
                            </div>
                        </a>
                        <a href="./display/action-buttons.html" class="flex items-center p-3 rounded-lg hover:bg-gray-100">
                            <i class="ri-settings-line text-lg text-indigo-600 mr-3"></i>
                            <div>
                                <h4 class="font-medium">操作按钮</h4>
                                <p class="text-sm text-gray-600">表格和列表中的操作按钮规范</p>
                            </div>
                        </a>
                        <a href="./display/page-header.html" class="flex items-center p-3 rounded-lg hover:bg-gray-100">
                            <i class="ri-layout-top-line text-lg text-indigo-600 mr-3"></i>
                            <div>
                                <h4 class="font-medium">页面标题</h4>
                                <p class="text-sm text-gray-600">页面顶部标题和功能区</p>
                            </div>
                        </a>
                    </div>
                </div>
                
                <!-- 反馈组件 -->
                <div class="mb-8">
                    <h3 class="text-lg font-semibold mb-2 text-gray-800">反馈组件</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <a href="./feedback/index.html" class="flex items-center p-3 rounded-lg hover:bg-gray-100">
                            <i class="ri-notification-3-line text-lg text-purple-600 mr-3"></i>
                            <div>
                                <h4 class="font-medium">反馈组件概览</h4>
                                <p class="text-sm text-gray-600">反馈组件总览</p>
                            </div>
                        </a>
                        <a href="./feedback/message.html" class="flex items-center p-3 rounded-lg hover:bg-gray-100">
                            <i class="ri-message-2-line text-lg text-blue-600 mr-3"></i>
                            <div>
                                <h4 class="font-medium">消息提示</h4>
                                <p class="text-sm text-gray-600">轻量级操作结果反馈</p>
                            </div>
                        </a>
                        <a href="./feedback/dialog.html" class="flex items-center p-3 rounded-lg hover:bg-gray-100">
                            <i class="ri-question-answer-line text-lg text-indigo-600 mr-3"></i>
                            <div>
                                <h4 class="font-medium">对话框</h4>
                                <p class="text-sm text-gray-600">操作确认和信息展示</p>
                            </div>
                        </a>
                        <a href="./feedback/alert.html" class="flex items-center p-3 rounded-lg hover:bg-gray-100">
                            <i class="ri-alert-line text-lg text-yellow-600 mr-3"></i>
                            <div>
                                <h4 class="font-medium">警告提示</h4>
                                <p class="text-sm text-gray-600">页面内信息提醒组件</p>
                            </div>
                        </a>
                    </div>
                </div>
                
                <!-- 动画效果 -->
                <section id="animation" class="nav-section bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-xl font-bold mb-4 pb-2 border-b border-gray-200">动画效果</h3>
                    <ul class="space-y-2">
                        <li>
                            <a href="./animations/index.html" class="flex items-center py-2 px-3 text-gray-700 hover:bg-gray-50 rounded-md nav-link">
                                <i class="ri-slideshow-3-line text-pink-600 mr-3"></i>
                                <span>动画效果概览</span>
                            </a>
                        </li>
                        <li>
                            <a href="./animations/transitions.html" class="flex items-center py-2 px-3 text-gray-700 hover:bg-gray-50 rounded-md nav-link">
                                <i class="ri-slideshow-3-line text-pink-600 mr-3"></i>
                                <span>过渡动画</span>
                            </a>
                        </li>
                        <li>
                            <a href="./animations/loading.html" class="flex items-center py-2 px-3 text-gray-700 hover:bg-gray-50 rounded-md nav-link">
                                <i class="ri-loader-4-line text-pink-600 mr-3"></i>
                                <span>加载动画</span>
                            </a>
                        </li>
                    </ul>
                </section>
                
                <!-- 使用指南 -->
                <section id="guides" class="nav-section bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-xl font-bold mb-4 pb-2 border-b border-gray-200">使用指南</h3>
                    <ul class="space-y-2">
                        <li>
                            <a href="./guides/index.html" class="flex items-center py-2 px-3 text-gray-700 hover:bg-gray-50 rounded-md nav-link">
                                <i class="ri-book-open-line text-indigo-600 mr-3"></i>
                                <span>使用指南首页</span>
                            </a>
                        </li>
                        <li>
                            <a href="./guides/vue3.html" class="flex items-center py-2 px-3 text-gray-700 hover:bg-gray-50 rounded-md nav-link">
                                <i class="ri-vuejs-line text-indigo-600 mr-3"></i>
                                <span>Vue 3 使用示例</span>
                            </a>
                        </li>
                        <li>
                            <a href="./guides/changelog.html" class="flex items-center py-2 px-3 text-gray-700 hover:bg-gray-50 rounded-md nav-link">
                                <i class="ri-history-line text-indigo-600 mr-3"></i>
                                <span>更新日志</span>
                            </a>
                        </li>
                    </ul>
                </section>
            </div>
        </div>
        
        <div class="text-center mb-12">
            <a href="./index.html" class="inline-flex items-center px-5 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <i class="ri-home-line mr-1"></i> 返回首页
            </a>
        </div>
    </div>
    
    <footer class="bg-white border-t border-gray-200 py-6 mt-12">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="mb-4 md:mb-0">
                    <p class="text-gray-500">© 2023 DataScope UI 组件库</p>
                </div>
                <div class="flex space-x-4">
                    <a href="./guides/changelog.html" class="text-gray-500 hover:text-indigo-600">
                        更新日志
                    </a>
                    <a href="./sitemap.html" class="text-gray-500 hover:text-indigo-600">
                        网站地图
                    </a>
                    <a href="https://github.com/yourusername/datascope-ui" target="_blank" class="text-gray-500 hover:text-indigo-600">
                        <i class="ri-github-fill text-xl"></i>
                    </a>
                </div>
            </div>
        </div>
    </footer>
    
    <script>
        // 添加滚动到指定部分的平滑效果
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                
                const targetId = this.getAttribute('href');
                const targetElement = document.querySelector(targetId);
                
                if (targetElement) {
                    window.scrollTo({
                        top: targetElement.offsetTop - 100,
                        behavior: 'smooth'
                    });
                    
                    // 更新左侧导航的活动状态
                    document.querySelectorAll('nav a').forEach(navLink => {
                        navLink.classList.remove('text-indigo-600', 'bg-indigo-50', 'font-medium');
                        navLink.classList.add('text-gray-700', 'hover:bg-gray-50', 'hover:text-indigo-600');
                    });
                    this.classList.remove('text-gray-700', 'hover:bg-gray-50', 'hover:text-indigo-600');
                    this.classList.add('text-indigo-600', 'bg-indigo-50', 'font-medium');
                }
            });
        });
    </script>
</body>
</html>