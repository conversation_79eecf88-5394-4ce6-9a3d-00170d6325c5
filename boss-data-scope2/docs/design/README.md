# DataScope UI 组件库

DataScope UI 是一套专为数据分析平台设计的高效组件库，提供一致的视觉体验和交互模式。本组件库基于 Vue 3 和 Tailwind CSS 构建，旨在满足数据分析、可视化和管理场景的需求。

## 项目结构

```
/design
├── index.html              # 组件库首页
├── components.html         # 组件概览页面
├── navigation.html         # 文档导航页面
├── sitemap.html            # 网站地图
├── README.md               # 项目说明文档
│
├── animations/             # 动画效果相关组件
│   ├── index.html          # 动画效果概览
│   ├── transitions.html    # 过渡动画
│   └── loading.html        # 加载动画
│
├── basic/                  # 基础组件
│   ├── index.html          # 基础组件概览
│   ├── buttons.html        # 按钮组件
│   ├── inputs.html         # 输入框组件
│   └── badges.html         # 状态标签
│
├── container/              # 容器组件
│   ├── index.html          # 容器组件概览
│   ├── cards.html          # 卡片容器
│   ├── panels.html         # 面板容器
│   └── boxes.html          # 盒子容器
│
├── display/                # 数据展示组件
│   ├── index.html          # 数据展示概览
│   ├── data-table.html     # 数据表格
│   ├── action-buttons.html # 操作按钮
│   └── page-header.html    # 页面标题
│
├── feedback/               # 反馈组件
│   ├── index.html          # 反馈组件概览
│   ├── message.html        # 消息提示
│   ├── notification.html   # 通知提醒
│   ├── dialog.html         # 对话框
│   ├── alert.html          # 警告提示
│   ├── progress.html       # 进度指示器
│   └── result.html         # 结果页
│
├── forms/                  # 表单组件
│   ├── index.html          # 表单组件概览
│   ├── basic-inputs.html   # 基础输入
│   ├── selectors.html      # 选择器
│   ├── toggles.html        # 开关控件
│   ├── date-picker.html    # 日期选择器
│   ├── upload.html         # 文件上传
│   └── form-layout.html    # 表单布局
│
└── guides/                 # 使用指南和文档
    ├── index.html          # 使用指南首页
    ├── vue3.html           # Vue 3 使用示例
    └── changelog.html      # 更新日志
```

## 快速开始

访问以下入口页面，了解组件库：

- [首页](./index.html) - 组件库概览和主要分类
- [组件概览](./components.html) - 所有组件的分类和导航
- [使用指南](./guides/index.html) - 设计原则和使用规范
- [Vue 3 示例](./guides/vue3.html) - 基于 Vue 3 的使用示例
- [导航](./navigation.html) - 完整的文档导航页面
- [网站地图](./sitemap.html) - 完整的页面结构索引

## 设计原则

1. **简洁明了** - 组件设计注重简洁明了，避免不必要的视觉元素和复杂交互
2. **一致性** - 保持视觉风格、交互模式和行为的一致性
3. **易用性优先** - 降低用户操作复杂度，提供清晰的反馈和提示
4. **响应式设计** - 支持在不同屏幕尺寸和设备上的良好表现

## 技术栈

- Vue 3 (Composition API)
- Tailwind CSS
- Remix Icon

## 更新日志

查看 [更新日志](./guides/changelog.html) 了解组件库的版本更新和改进历史。