<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DataScope 面板容器组件</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        .preview-section {
            margin-bottom: 30px;
        }
        .component-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #eaeaea;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold mb-8 text-center">DataScope 面板容器组件</h1>
        
        <!-- 基础面板 -->
        <div class="bg-white shadow rounded-lg p-6 mb-8">
            <h2 class="component-title">基础面板</h2>
            <div class="grid grid-cols-1 gap-6">
                <!-- 简单面板 -->
                <div class="bg-white border border-gray-200 rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">简单面板</h3>
                    <p class="text-gray-500">面板是一种轻量级容器，通常用于分组相关内容或功能。</p>
                </div>
                
                <!-- 分组面板 -->
                <div class="space-y-4">
                    <div class="bg-white border border-gray-200 rounded-lg shadow-sm p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">分组面板 - 第一部分</h3>
                        <p class="text-gray-500">面板可以在视觉上分组相关内容，形成内容层次。</p>
                    </div>
                    <div class="bg-white border border-gray-200 rounded-lg shadow-sm p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">分组面板 - 第二部分</h3>
                        <p class="text-gray-500">多个面板串联可以组织复杂的相关信息。</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 可折叠面板 -->
        <div class="bg-white shadow rounded-lg p-6 mb-8">
            <h2 class="component-title">可折叠面板</h2>
            <div class="space-y-4">
                <!-- 单个折叠面板 -->
                <div class="border border-gray-200 rounded-lg shadow-sm overflow-hidden">
                    <div class="bg-gray-50 px-6 py-4 flex justify-between items-center cursor-pointer">
                        <h3 class="text-lg font-medium text-gray-900">可折叠面板标题</h3>
                        <i class="ri-arrow-down-s-line transition-transform"></i>
                    </div>
                    <div class="px-6 py-4 border-t border-gray-200">
                        <p class="text-gray-500">可折叠面板内容区域，点击标题可以展开或收起此内容。</p>
                    </div>
                </div>
                
                <!-- 手风琴面板组 -->
                <div class="border border-gray-200 rounded-lg shadow-sm overflow-hidden">
                    <div class="bg-gray-50 px-6 py-4 flex justify-between items-center cursor-pointer border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">手风琴面板 1</h3>
                        <i class="ri-arrow-down-s-line"></i>
                    </div>
                    <div class="px-6 py-4 border-b border-gray-200">
                        <p class="text-gray-500">手风琴面板允许一次只展开一个面板，其他面板自动收起。</p>
                    </div>
                    
                    <div class="bg-gray-50 px-6 py-4 flex justify-between items-center cursor-pointer border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">手风琴面板 2</h3>
                        <i class="ri-arrow-down-s-line"></i>
                    </div>
                    
                    <div class="bg-gray-50 px-6 py-4 flex justify-between items-center cursor-pointer">
                        <h3 class="text-lg font-medium text-gray-900">手风琴面板 3</h3>
                        <i class="ri-arrow-down-s-line"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 特殊面板 -->
        <div class="bg-white shadow rounded-lg p-6 mb-8">
            <h2 class="component-title">特殊面板</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- 提示面板 -->
                <div class="bg-blue-50 border-l-4 border-blue-400 p-4 rounded">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="ri-information-line text-blue-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-blue-800">信息提示</h3>
                            <div class="mt-2 text-sm text-blue-700">
                                <p>这是一个信息提示面板，用于向用户显示重要信息。</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 警告面板 -->
                <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="ri-alert-line text-yellow-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-yellow-800">警告提示</h3>
                            <div class="mt-2 text-sm text-yellow-700">
                                <p>这是一个警告提示面板，用于向用户发出警告信息。</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 成功面板 -->
                <div class="bg-green-50 border-l-4 border-green-400 p-4 rounded">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="ri-checkbox-circle-line text-green-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-green-800">成功提示</h3>
                            <div class="mt-2 text-sm text-green-700">
                                <p>这是一个成功提示面板，用于向用户表示操作已成功完成。</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 错误面板 -->
                <div class="bg-red-50 border-l-4 border-red-400 p-4 rounded">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="ri-error-warning-line text-red-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800">错误提示</h3>
                            <div class="mt-2 text-sm text-red-700">
                                <p>这是一个错误提示面板，用于向用户表示操作过程中出现了错误。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>