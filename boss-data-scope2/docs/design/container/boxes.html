<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DataScope 盒子容器组件</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        .preview-section {
            margin-bottom: 30px;
        }
        .component-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #eaeaea;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold mb-8 text-center">DataScope 盒子容器组件</h1>
        
        <!-- 基础盒子 -->
        <div class="bg-white shadow rounded-lg p-6 mb-8">
            <h2 class="component-title">基础盒子</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- 简单盒子 -->
                <div class="border border-gray-200 rounded p-4">
                    <p class="text-gray-700">基础盒子容器，提供简单的边框和间距</p>
                </div>
                
                <!-- 带标题盒子 -->
                <div class="border border-gray-200 rounded">
                    <div class="bg-gray-50 px-4 py-2 border-b border-gray-200">
                        <h3 class="font-medium text-gray-700">标题盒子</h3>
                    </div>
                    <div class="p-4">
                        <p class="text-gray-700">带有标题的盒子容器</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 样式变体 -->
        <div class="bg-white shadow rounded-lg p-6 mb-8">
            <h2 class="component-title">样式变体</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- 实线边框盒子 -->
                <div class="border-2 border-gray-300 rounded p-4">
                    <p class="text-gray-700">实线边框盒子</p>
                </div>
                
                <!-- 虚线边框盒子 -->
                <div class="border-2 border-dashed border-gray-300 rounded p-4">
                    <p class="text-gray-700">虚线边框盒子</p>
                </div>
                
                <!-- 点线边框盒子 -->
                <div class="border-2 border-dotted border-gray-300 rounded p-4">
                    <p class="text-gray-700">点线边框盒子</p>
                </div>
                
                <!-- 彩色边框盒子 -->
                <div class="border-2 border-blue-300 rounded p-4">
                    <p class="text-gray-700">彩色边框盒子</p>
                </div>
                
                <!-- 填充背景盒子 -->
                <div class="bg-blue-50 border border-blue-100 rounded p-4">
                    <p class="text-gray-700">填充背景盒子</p>
                </div>
                
                <!-- 带阴影盒子 -->
                <div class="bg-white border border-gray-100 rounded shadow-md p-4">
                    <p class="text-gray-700">带阴影盒子</p>
                </div>
            </div>
        </div>
        
        <!-- 功能盒子 -->
        <div class="bg-white shadow rounded-lg p-6 mb-8">
            <h2 class="component-title">功能盒子</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- 可选中盒子 -->
                <div class="border-2 border-indigo-500 rounded p-4 bg-indigo-50">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 mr-2">
                            <i class="ri-checkbox-circle-fill text-indigo-500"></i>
                        </div>
                        <div>
                            <h3 class="font-medium text-gray-900">已选中盒子</h3>
                            <p class="text-gray-500 mt-1">表示当前选中状态的盒子</p>
                        </div>
                    </div>
                </div>
                
                <!-- 未选中盒子 -->
                <div class="border-2 border-gray-200 rounded p-4 hover:border-indigo-300 hover:bg-indigo-50 cursor-pointer transition-colors">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 mr-2">
                            <i class="ri-checkbox-blank-circle-line text-gray-400"></i>
                        </div>
                        <div>
                            <h3 class="font-medium text-gray-900">可选中盒子</h3>
                            <p class="text-gray-500 mt-1">可以被选中的交互式盒子</p>
                        </div>
                    </div>
                </div>
                
                <!-- 可拖放盒子 -->
                <div class="border-2 border-gray-200 rounded p-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 mr-2 cursor-move">
                            <i class="ri-drag-move-line text-gray-400"></i>
                        </div>
                        <div>
                            <h3 class="font-medium text-gray-900">可拖放盒子</h3>
                            <p class="text-gray-500 mt-1">支持拖放排序的盒子</p>
                        </div>
                    </div>
                </div>
                
                <!-- 带徽标盒子 -->
                <div class="border border-gray-200 rounded p-4 relative">
                    <div class="absolute top-0 right-0 transform translate-x-1/3 -translate-y-1/3">
                        <span class="inline-flex items-center justify-center h-6 w-6 rounded-full bg-red-500 text-white text-xs">5</span>
                    </div>
                    <h3 class="font-medium text-gray-900">带徽标盒子</h3>
                    <p class="text-gray-500 mt-1">右上角带有数字徽标的盒子</p>
                </div>
            </div>
        </div>
        
        <!-- 应用场景 -->
        <div class="bg-white shadow rounded-lg p-6 mb-8">
            <h2 class="component-title">应用场景</h2>
            <div class="space-y-6">
                <!-- 表单分组 -->
                <div class="border border-gray-200 rounded">
                    <div class="bg-gray-50 px-4 py-2 border-b border-gray-200">
                        <h3 class="font-medium text-gray-700">基本信息</h3>
                    </div>
                    <div class="p-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                                <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm" style="border: 2px solid #d1d5db;" placeholder="请输入用户名">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">邮箱</label>
                                <input type="email" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm" style="border: 2px solid #d1d5db;" placeholder="请输入邮箱">
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 数据列表 -->
                <div class="border border-gray-200 rounded">
                    <div class="bg-gray-50 px-4 py-2 border-b border-gray-200 flex justify-between items-center">
                        <h3 class="font-medium text-gray-700">数据项列表</h3>
                        <button class="text-sm text-indigo-600 hover:text-indigo-800">查看全部</button>
                    </div>
                    <div class="divide-y divide-gray-200">
                        <div class="p-4 hover:bg-gray-50">
                            <div class="flex justify-between items-center">
                                <span class="text-gray-900">数据项一</span>
                                <span class="text-gray-500 text-sm">2023-04-10</span>
                            </div>
                        </div>
                        <div class="p-4 hover:bg-gray-50">
                            <div class="flex justify-between items-center">
                                <span class="text-gray-900">数据项二</span>
                                <span class="text-gray-500 text-sm">2023-04-09</span>
                            </div>
                        </div>
                        <div class="p-4 hover:bg-gray-50">
                            <div class="flex justify-between items-center">
                                <span class="text-gray-900">数据项三</span>
                                <span class="text-gray-500 text-sm">2023-04-08</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>