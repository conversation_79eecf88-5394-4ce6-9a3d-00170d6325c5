<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DataScope 卡片容器组件</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        .card {
            transition: all 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        .preview-section {
            margin-bottom: 30px;
        }
        .component-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #eaeaea;
        }
    </style>
</head>
<body class="bg-gray-50">
    <header class="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="../index.html" class="flex items-center">
                        <h1 class="text-xl font-bold text-gray-900">DataScope UI</h1>
                    </a>
                </div>
                <nav class="flex items-center space-x-4">
                    <a href="../index.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">首页</a>
                    <a href="../components.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">组件</a>
                    <a href="../guides/index.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">使用指引</a>
                    <a href="../sitemap.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">网站地图</a>
                </nav>
            </div>
        </div>
    </header>

    <div class="flex min-h-screen">
        <!-- 左侧导航菜单 -->
        <div class="w-64 bg-white border-r border-gray-200 overflow-y-auto">
            <div class="p-4">
                <h3 class="text-lg font-semibold text-gray-900 mb-3">组件分类</h3>
                
                <div class="space-y-4">
                    <!-- 基础组件 -->
                    <div>
                        <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                            <i class="ri-layout-2-line mr-2 text-blue-600"></i>
                            <span>基础组件</span>
                        </h4>
                        <ul class="ml-6 space-y-1">
                            <li>
                                <a href="../basic/buttons.html" class="block text-gray-700 hover:text-indigo-600 py-1">按钮</a>
                            </li>
                            <li>
                                <a href="../basic/icon-buttons.html" class="block text-gray-700 hover:text-indigo-600 py-1">图标按钮</a>
                            </li>
                            <li>
                                <a href="../basic/inputs.html" class="block text-gray-700 hover:text-indigo-600 py-1">输入框</a>
                            </li>
                            <li>
                                <a href="../basic/badges.html" class="block text-gray-700 hover:text-indigo-600 py-1">状态标签</a>
                            </li>
                            <li>
                                <a href="../basic/index.html" class="block text-gray-700 hover:text-indigo-600 py-1">全部基础组件</a>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- 表单组件 -->
                    <div>
                        <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                            <i class="ri-file-list-3-line mr-2 text-green-600"></i>
                            <span>表单组件</span>
                        </h4>
                        <ul class="ml-6 space-y-1">
                            <li>
                                <a href="../forms/basic-inputs.html" class="block text-gray-700 hover:text-indigo-600 py-1">基础输入</a>
                            </li>
                            <li>
                                <a href="../forms/selectors.html" class="block text-gray-700 hover:text-indigo-600 py-1">选择器</a>
                            </li>
                            <li>
                                <a href="../forms/toggles.html" class="block text-gray-700 hover:text-indigo-600 py-1">开关控件</a>
                            </li>
                            <li>
                                <a href="../forms/date-picker.html" class="block text-gray-700 hover:text-indigo-600 py-1">日期选择器</a>
                            </li>
                            <li>
                                <a href="../forms/index.html" class="block text-gray-700 hover:text-indigo-600 py-1">全部表单组件</a>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- 容器组件 -->
                    <div>
                        <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                            <i class="ri-layout-masonry-line mr-2 text-purple-600"></i>
                            <span>容器组件</span>
                        </h4>
                        <ul class="ml-6 space-y-1">
                            <li>
                                <a href="./cards.html" class="block text-gray-700 hover:text-indigo-600 py-1 font-medium text-indigo-600">卡片容器</a>
                            </li>
                            <li>
                                <a href="./panels.html" class="block text-gray-700 hover:text-indigo-600 py-1">面板容器</a>
                            </li>
                            <li>
                                <a href="./boxes.html" class="block text-gray-700 hover:text-indigo-600 py-1">盒子容器</a>
                            </li>
                            <li>
                                <a href="./index.html" class="block text-gray-700 hover:text-indigo-600 py-1">全部容器组件</a>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- 数据展示 -->
                    <div>
                        <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                            <i class="ri-table-line mr-2 text-yellow-600"></i>
                            <span>数据展示</span>
                        </h4>
                        <ul class="ml-6 space-y-1">
                            <li>
                                <a href="../display/data-table.html" class="block text-gray-700 hover:text-indigo-600 py-1">数据表格</a>
                            </li>
                            <li>
                                <a href="../display/action-buttons.html" class="block text-gray-700 hover:text-indigo-600 py-1">操作按钮</a>
                            </li>
                            <li>
                                <a href="../display/page-header.html" class="block text-gray-700 hover:text-indigo-600 py-1">页面标题</a>
                            </li>
                            <li>
                                <a href="../display/index.html" class="block text-gray-700 hover:text-indigo-600 py-1">全部数据展示</a>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- 反馈组件 -->
                    <div>
                        <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                            <i class="ri-notification-3-line mr-2 text-indigo-600"></i>
                            <span>反馈组件</span>
                        </h4>
                        <ul class="ml-6 space-y-1">
                            <li>
                                <a href="../feedback/message.html" class="block text-gray-700 hover:text-indigo-600 py-1">消息提示</a>
                            </li>
                            <li>
                                <a href="../feedback/notification.html" class="block text-gray-700 hover:text-indigo-600 py-1">通知提醒</a>
                            </li>
                            <li>
                                <a href="../feedback/dialog.html" class="block text-gray-700 hover:text-indigo-600 py-1">对话框</a>
                            </li>
                            <li>
                                <a href="../feedback/alert.html" class="block text-gray-700 hover:text-indigo-600 py-1">警告提示</a>
                            </li>
                            <li>
                                <a href="../feedback/index.html" class="block text-gray-700 hover:text-indigo-600 py-1">全部反馈组件</a>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- 动画效果 -->
                    <div>
                        <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                            <i class="ri-slideshow-3-line mr-2 text-pink-600"></i>
                            <span>动画效果</span>
                        </h4>
                        <ul class="ml-6 space-y-1">
                            <li>
                                <a href="../animations/transitions.html" class="block text-gray-700 hover:text-indigo-600 py-1">过渡动画</a>
                            </li>
                            <li>
                                <a href="../animations/loading.html" class="block text-gray-700 hover:text-indigo-600 py-1">加载动画</a>
                            </li>
                            <li>
                                <a href="../animations/index.html" class="block text-gray-700 hover:text-indigo-600 py-1">全部动画效果</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 右侧内容区 -->
        <div class="flex-1 p-6 overflow-y-auto">
            <div class="container mx-auto px-4 py-8">
                <div class="flex items-center mb-8">
                    <a href="../index.html" class="text-gray-500 hover:text-indigo-600 mr-2">
                        <i class="ri-home-line"></i>
                    </a>
                    <span class="text-gray-400 mx-2">/</span>
                    <a href="../components.html" class="text-gray-500 hover:text-indigo-600 mr-2">
                        组件
                    </a>
                    <span class="text-gray-400 mx-2">/</span>
                    <a href="./index.html" class="text-gray-500 hover:text-indigo-600 mr-2">
                        容器组件
                    </a>
                    <span class="text-gray-400 mx-2">/</span>
                    <span class="text-gray-900 font-medium">卡片容器</span>
                </div>

                <div class="mb-8">
                    <h2 class="text-3xl font-bold mb-4 text-gray-900">DataScope 卡片容器组件</h2>
                    <p class="text-lg text-gray-600 max-w-4xl">
                        卡片容器用于展示和组织不同类型的内容和信息，具有优雅的外观和灵活的布局选项。
                    </p>
                </div>
        
                <!-- 基础卡片 -->
                <div class="bg-white shadow rounded-lg p-6 mb-8">
                    <h2 class="component-title">基础卡片</h2>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- 基础卡片 -->
                        <div class="bg-white rounded-lg shadow card p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-2">基础卡片</h3>
                            <p class="text-gray-500">简单的内容容器，可容纳各种内容</p>
                        </div>
                        
                        <!-- 带边框卡片 -->
                        <div class="bg-white rounded-lg border-2 border-gray-200 card p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-2">带边框卡片</h3>
                            <p class="text-gray-500">使用边框代替阴影的轻量级卡片</p>
                        </div>
                        
                        <!-- 无内边距卡片 -->
                        <div class="bg-white rounded-lg shadow card overflow-hidden">
                            <img src="https://via.placeholder.com/400x200" alt="示例图片" class="w-full h-40 object-cover">
                            <div class="p-6">
                                <h3 class="text-lg font-medium text-gray-900 mb-2">图片卡片</h3>
                                <p class="text-gray-500">顶部包含图片的卡片</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 卡片标题 -->
                <div class="bg-white shadow rounded-lg p-6 mb-8">
                    <h2 class="component-title">卡片标题</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- 标题分割线卡片 -->
                        <div class="bg-white rounded-lg shadow card overflow-hidden">
                            <div class="px-6 py-4 border-b border-gray-200">
                                <h3 class="text-lg font-medium text-gray-900">带分割线标题</h3>
                            </div>
                            <div class="p-6">
                                <p class="text-gray-500">标题与内容通过分割线分隔的卡片</p>
                            </div>
                        </div>
                        
                        <!-- 带副标题卡片 -->
                        <div class="bg-white rounded-lg shadow card p-6">
                            <div class="mb-4">
                                <h3 class="text-lg font-medium text-gray-900">主标题</h3>
                                <p class="text-sm text-gray-500">副标题描述信息</p>
                            </div>
                            <div class="border-t border-gray-200 pt-4">
                                <p class="text-gray-500">带有主标题和副标题的卡片</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 卡片操作 -->
                <div class="bg-white shadow rounded-lg p-6 mb-8">
                    <h2 class="component-title">卡片操作</h2>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- 底部操作卡片 -->
                        <div class="bg-white rounded-lg shadow card overflow-hidden">
                            <div class="p-6">
                                <h3 class="text-lg font-medium text-gray-900 mb-2">底部操作卡片</h3>
                                <p class="text-gray-500">包含底部操作区域的卡片</p>
                            </div>
                            <div class="bg-gray-50 px-6 py-3 flex justify-end space-x-2 border-t border-gray-200">
                                <button class="px-3 py-1.5 border border-gray-300 text-sm rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50">
                                    取消
                                </button>
                                <button class="px-3 py-1.5 border border-transparent text-sm rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700">
                                    确认
                                </button>
                            </div>
                        </div>
                        
                        <!-- 标题操作卡片 -->
                        <div class="bg-white rounded-lg shadow card overflow-hidden">
                            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                                <h3 class="text-lg font-medium text-gray-900">带操作标题</h3>
                                <div class="flex space-x-2">
                                    <button class="p-1 text-gray-400 hover:text-gray-500">
                                        <i class="ri-refresh-line"></i>
                                    </button>
                                    <button class="p-1 text-gray-400 hover:text-gray-500">
                                        <i class="ri-more-2-line"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="p-6">
                                <p class="text-gray-500">标题区域包含操作按钮的卡片</p>
                            </div>
                        </div>
                        
                        <!-- 悬浮操作卡片 -->
                        <div class="bg-white rounded-lg shadow card p-6 relative">
                            <div class="absolute top-2 right-2">
                                <button class="p-1 text-gray-400 hover:text-gray-500">
                                    <i class="ri-close-line"></i>
                                </button>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">悬浮操作卡片</h3>
                            <p class="text-gray-500">右上角包含悬浮操作的卡片</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>