<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DataScope 容器组件库</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        .component-card {
            transition: all 0.3s ease;
        }
        .component-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold mb-4">DataScope 容器组件库</h1>
            <p class="text-lg text-gray-600">标准化的容器组件，用于组织和展示内容</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
            <!-- 卡片容器 -->
            <a href="cards.html" class="block">
                <div class="bg-white rounded-lg shadow component-card p-6">
                    <div class="flex items-center justify-center h-16 w-16 rounded-md bg-blue-500 text-white mx-auto mb-4">
                        <i class="ri-layout-card-fill text-xl"></i>
                    </div>
                    <h3 class="text-xl font-medium text-gray-900 text-center mb-2">卡片容器</h3>
                    <p class="text-gray-500 text-center">用于展示内容的卡片式容器</p>
                    <div class="mt-4 flex justify-center">
                        <div class="w-full max-w-sm bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                            <h4 class="text-sm font-medium text-gray-900 mb-2">卡片标题</h4>
                            <p class="text-xs text-gray-500">卡片内容示例</p>
                        </div>
                    </div>
                </div>
            </a>
            
            <!-- 面板容器 -->
            <a href="panels.html" class="block">
                <div class="bg-white rounded-lg shadow component-card p-6">
                    <div class="flex items-center justify-center h-16 w-16 rounded-md bg-purple-500 text-white mx-auto mb-4">
                        <i class="ri-layout-masonry-fill text-xl"></i>
                    </div>
                    <h3 class="text-xl font-medium text-gray-900 text-center mb-2">面板容器</h3>
                    <p class="text-gray-500 text-center">可折叠的内容面板组件</p>
                    <div class="mt-4 flex justify-center">
                        <div class="w-full max-w-sm border border-gray-200 rounded-lg overflow-hidden">
                            <div class="bg-gray-50 px-4 py-2 flex justify-between items-center">
                                <h4 class="text-sm font-medium text-gray-900">面板标题</h4>
                                <i class="ri-arrow-down-s-line text-xs"></i>
                            </div>
                            <div class="p-4 bg-white border-t border-gray-200">
                                <p class="text-xs text-gray-500">面板内容示例</p>
                            </div>
                        </div>
                    </div>
                </div>
            </a>
            
            <!-- 盒子容器 -->
            <a href="boxes.html" class="block">
                <div class="bg-white rounded-lg shadow component-card p-6">
                    <div class="flex items-center justify-center h-16 w-16 rounded-md bg-green-500 text-white mx-auto mb-4">
                        <i class="ri-box-3-fill text-xl"></i>
                    </div>
                    <h3 class="text-xl font-medium text-gray-900 text-center mb-2">盒子容器</h3>
                    <p class="text-gray-500 text-center">多用途的通用盒子容器</p>
                    <div class="mt-4 flex justify-center">
                        <div class="w-full max-w-sm border-2 border-gray-200 rounded p-4">
                            <p class="text-xs text-gray-500">盒子内容示例</p>
                        </div>
                    </div>
                </div>
            </a>
        </div>
        
        <div class="mt-12 bg-white rounded-lg shadow p-6">
            <h2 class="text-2xl font-bold mb-4">容器组件使用指南</h2>
            <p class="mb-4">DataScope容器组件库提供了一套完整的内容容器组件，用于组织和展示内容。使用这些容器可以提高开发效率，并确保整个应用程序具有一致的外观和行为。</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="font-medium mb-2">容器选择指南</h3>
                    <ul class="list-disc pl-5 space-y-1 text-gray-600">
                        <li>卡片容器：用于展示独立的内容单元</li>
                        <li>面板容器：适合需要展开/折叠的内容区域</li>
                        <li>盒子容器：轻量级的通用内容容器</li>
                        <li>在布局中合理嵌套各类容器</li>
                    </ul>
                </div>
                
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="font-medium mb-2">实施原则</h3>
                    <ul class="list-disc pl-5 space-y-1 text-gray-600">
                        <li>保持容器视觉风格一致</li>
                        <li>内容分组时优先使用容器</li>
                        <li>避免容器嵌套过深</li>
                        <li>为不同类型的内容选择合适的容器</li>
                    </ul>
                </div>
            </div>
            
            <div class="mt-8">
                <h3 class="font-medium mb-2">应用场景示例</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-4">
                    <!-- 表单场景 -->
                    <div class="border border-gray-200 rounded-lg overflow-hidden">
                        <div class="bg-gray-50 px-4 py-2 border-b border-gray-200">
                            <h4 class="font-medium text-gray-700">表单分组</h4>
                        </div>
                        <div class="p-4">
                            <div class="space-y-2">
                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">用户名</label>
                                    <input type="text" class="w-full px-2 py-1 text-xs border border-gray-300 rounded-md" style="border: 2px solid #d1d5db;">
                                </div>
                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">密码</label>
                                    <input type="password" class="w-full px-2 py-1 text-xs border border-gray-300 rounded-md" style="border: 2px solid #d1d5db;">
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 数据展示场景 -->
                    <div class="border border-gray-200 rounded-lg overflow-hidden">
                        <div class="bg-gray-50 px-4 py-2 border-b border-gray-200">
                            <h4 class="font-medium text-gray-700">数据统计</h4>
                        </div>
                        <div class="p-4">
                            <div class="grid grid-cols-2 gap-2">
                                <div class="bg-blue-50 rounded p-2 text-center">
                                    <p class="text-xs text-blue-800 font-medium">总计</p>
                                    <p class="text-sm font-bold text-blue-600">1,234</p>
                                </div>
                                <div class="bg-green-50 rounded p-2 text-center">
                                    <p class="text-xs text-green-800 font-medium">完成</p>
                                    <p class="text-sm font-bold text-green-600">789</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 导航场景 -->
                    <div class="border border-gray-200 rounded-lg overflow-hidden">
                        <div class="bg-gray-50 px-4 py-2 border-b border-gray-200">
                            <h4 class="font-medium text-gray-700">快速链接</h4>
                        </div>
                        <div class="divide-y divide-gray-200">
                            <a href="#" class="block p-3 hover:bg-gray-50">
                                <div class="flex items-center">
                                    <i class="ri-dashboard-line mr-2 text-gray-400"></i>
                                    <span class="text-sm text-gray-700">仪表盘</span>
                                </div>
                            </a>
                            <a href="#" class="block p-3 hover:bg-gray-50">
                                <div class="flex items-center">
                                    <i class="ri-settings-line mr-2 text-gray-400"></i>
                                    <span class="text-sm text-gray-700">设置</span>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <footer class="bg-white border-t border-gray-200 py-8 mt-12">
        <div class="container mx-auto px-4">
            <p class="text-center text-gray-500">© 2023 DataScope UI 组件库</p>
        </div>
    </footer>
</body>
</html>