/**
 * SQL编辑器修复脚本
 * 
 * 由于Monaco编辑器在某些情况下可能无法正常显示内容，
 * 此脚本提供了一个全局辅助函数，可以在浏览器控制台中使用来强制更新编辑器内容。
 */

(function() {
  // 监听DOMContentLoaded事件，确保页面加载完成
  document.addEventListener('DOMContentLoaded', function() {
    console.log('SQL Editor Fix script loaded');
    
    // 定义全局辅助函数
    window.fixSqlEditor = function() {
      console.log('尝试修复SQL编辑器显示...');
      
      try {
        // 1. 从全局变量获取SQL内容
        const sqlContent = window.__SQL_CONTENT__;
        if (!sqlContent) {
          console.log('没有找到全局SQL内容');
          return false;
        }
        
        console.log('找到全局SQL内容，长度:', sqlContent.length);
        
        // 2. 尝试直接通过全局函数更新
        if (window.updateSqlEditor) {
          const result = window.updateSqlEditor(sqlContent);
          console.log('通过全局函数更新编辑器，结果:', result);
          return result;
        }
        
        // 3. 尝试找到编辑器实例并直接操作
        // 查找编辑器DOM元素
        const editorElements = document.querySelectorAll('.monaco-editor');
        if (editorElements.length === 0) {
          console.log('未找到编辑器元素');
          return false;
        }
        
        console.log('找到', editorElements.length, '个编辑器元素');
        
        // 4. 尝试触发编辑器内容更新事件
        setTimeout(function() {
          // 创建一个自定义事件
          const event = new CustomEvent('sqlEditorFix', { 
            detail: { content: sqlContent }
          });
          document.dispatchEvent(event);
          
          console.log('已发送编辑器修复事件');
          
          // 5. 最后尝试通过直接更新文本区域
          setTimeout(function() {
            const textareas = document.querySelectorAll('.monaco-editor textarea');
            if (textareas.length > 0) {
              console.log('找到编辑器文本区域，尝试更新');
              for (let i = 0; i < textareas.length; i++) {
                const textarea = textareas[i];
                textarea.value = sqlContent;
                
                // 触发input事件
                const inputEvent = new Event('input', { bubbles: true });
                textarea.dispatchEvent(inputEvent);
              }
              return true;
            }
            
            return false;
          }, 300);
        }, 200);
        
        return true;
      } catch (err) {
        console.error('修复SQL编辑器时出错:', err);
        return false;
      }
    };
    
    // 添加事件监听，当有全局SQL内容但编辑器为空时自动修复
    document.addEventListener('sqlEditorCheck', function(e) {
      setTimeout(function() {
        if (window.__SQL_CONTENT__ && 
            document.querySelector('.monaco-editor')) {
          // 检查编辑器内容是否为空
          const textareas = document.querySelectorAll('.monaco-editor textarea');
          if (textareas.length > 0 && !textareas[0].value) {
            console.log('检测到编辑器可能内容为空，尝试修复');
            window.fixSqlEditor();
          }
        }
      }, 1000);
    });
    
    // 主动触发一次检查
    setTimeout(function() {
      document.dispatchEvent(new CustomEvent('sqlEditorCheck'));
    }, 2000);
  });
})(); 